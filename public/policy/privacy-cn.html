<!-- saved from url=(0049)https://a1.app11.cn/dev/1729070117117_dpnzzp.html -->
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40">

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="ProgId" content="Word.Document" />
  <meta name="Generator" content="Microsoft Word 14" />
  <meta name="Originator" content="Microsoft Word 14" />
  <link rel="File-List"
    href="https://a1.app11.cn/dev/2024.10.12%E7%95%85%E8%AF%BB%E8%90%A5%E9%94%80%E5%B9%B3%E5%8F%B0%E9%9A%90%E7%A7%81%E6%94%BF%E7%AD%96-%E5%AE%9A%E7%A8%BF.files/filelist.xml" />
  <title></title>
  <!--[if gte mso 9
      ]><xml
        ><o:DocumentProperties
          ><o:Author>CD-W-Legal</o:Author><o:LastAuthor>强仔</o:LastAuthor><o:Revision>1</o:Revision
          ><o:Pages>19</o:Pages><o:Characters>13125</o:Characters></o:DocumentProperties
        ><o:CustomDocumentProperties
          ><o:KSOProductBuildVer dt:dt="string">2052-6.2.2.8394</o:KSOProductBuildVer
          ><o:ICV dt:dt="string"
            >011B8D92B7FDE821F0820F672739A2FD_43</o:ICV
          ></o:CustomDocumentProperties
        ></xml
      ><!
    [endif]-->
  <!--[if gte mso 9]><xml><o:OfficeDocumentSettings></o:OfficeDocumentSettings></xml><![endif]-->
  <!--[if gte mso 9
      ]><xml
        ><w:WordDocument
          ><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel
          ><w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery
          ><w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery
          ><w:DocumentKind>DocumentNotSpecified</w:DocumentKind
          ><w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing
          ><w:TrackRevisions /><w:View>Web</w:View
          ><w:Compatibility
            ><w:AdjustLineHeightInTable /><w:DontGrowAutofit /><w:BalanceSingleByteDoubleByteWidth /><w:DoNotExpandShiftReturn /><w:UseFELayout /></w:Compatibility
          ><w:Zoom>0</w:Zoom></w:WordDocument
        ></xml
      ><!
    [endif]-->
  <!--[if gte mso 9
      ]><xml
        ><w:LatentStyles
          DefLockedState="false"
          DefUnhideWhenUsed="true"
          DefSemiHidden="true"
          DefQFormat="false"
          DefPriority="99"
          LatentStyleCount="260">
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Normal"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="heading 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 7"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 8"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="heading 9"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 7"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 8"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index 9"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 7"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 8"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toc 9"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Normal Indent"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="footnote text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="annotation text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="header"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="footer"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="index heading"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            QFormat="true"
            Name="caption"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="table of figures"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="envelope address"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="envelope return"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="footnote reference"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="annotation reference"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="line number"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="page number"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="endnote reference"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="endnote text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="table of authorities"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="macro"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="toa heading"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Bullet"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Number"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Bullet 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Bullet 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Bullet 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Bullet 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Number 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Number 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Number 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Number 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Title"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Closing"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Signature"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Default Paragraph Font"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text Indent"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Continue"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Continue 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Continue 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Continue 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="List Continue 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Message Header"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Subtitle"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Salutation"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Date"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text First Indent"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text First Indent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Note Heading"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text Indent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Body Text Indent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Block Text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Hyperlink"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="FollowedHyperlink"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Strong"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Emphasis"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Document Map"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Plain Text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="E-mail Signature"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Normal (Web)"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Acronym"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Address"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Cite"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Code"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Definition"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Keyboard"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Preformatted"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Sample"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Typewriter"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="HTML Variable"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Normal Table"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="annotation subject"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="No List"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="1 / a / i"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="1 / 1.1 / 1.1.1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="Article / Section"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Simple 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Simple 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Simple 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Classic 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Classic 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Classic 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Classic 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Colorful 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Colorful 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Colorful 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Columns 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Columns 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Columns 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Columns 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Columns 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 7"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Grid 8"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 7"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table List 8"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table 3D effects 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table 3D effects 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table 3D effects 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Contemporary"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Elegant"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Professional"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Subtle 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Subtle 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Web 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Web 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Web 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Balloon Text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            QFormat="true"
            Name="Table Grid"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="0"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Table Theme"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="Placeholder Text"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="No Spacing"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="List Paragraph"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="Quote"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="99"
            SemiHidden="false"
            Name="Intense Quote"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3 Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid Accent 1"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3 Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid Accent 2"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3 Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid Accent 3"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3 Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid Accent 4"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3 Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid Accent 5"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="60"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Shading Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="61"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light List Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="62"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Light Grid Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="63"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 1 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="64"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Shading 2 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="65"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 1 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="66"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium List 2 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="67"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 1 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="68"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 2 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="69"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Medium Grid 3 Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="70"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Dark List Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="71"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Shading Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="72"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful List Accent 6"></w:LsdException>
          <w:LsdException
            Locked="false"
            Priority="73"
            SemiHidden="false"
            UnhideWhenUsed="false"
            Name="Colorful Grid Accent 6"></w:LsdException> </w:LatentStyles></xml
    ><![endif]-->
  <style>
    @font-face {
      font-family: 'Times New Roman';
    }

    @font-face {
      font-family: '宋体';
    }

    @font-face {
      font-family: 'Wingdings';
    }

    @font-face {
      font-family: 'Calibri';
    }

    @font-face {
      font-family: 'MicrosoftYaHei-Bold';
    }

    @font-face {
      font-family: '仿宋';
    }

    @font-face {
      font-family: '微软雅黑';
    }

    @list l0:level1 {
      mso-level-number-format: decimal;
      mso-level-suffix: none;
      mso-level-text: '（%1）';
      mso-level-tab-stop: none;
      mso-level-number-position: left;
      margin-left: 0pt;
      text-indent: 0pt;
      font-family: 'Times New Roman';
    }

    p.MsoNormal {
      mso-style-name: 正文;
      mso-style-parent: '';
      margin: 0pt;
      margin-bottom: 0.0001pt;
      mso-pagination: none;
      text-align: justify;
      text-justify: inter-ideograph;
      font-family: Calibri;
      mso-fareast-font-family: 宋体;
      mso-bidi-font-family: 'Times New Roman';
      font-size: 10.5pt;
      mso-font-kerning: 1pt;
    }

    span.10 {
      font-family: 'Times New Roman';
    }

    span.15 {
      font-family: 'Times New Roman';
      color: rgb(0, 0, 255);
      text-decoration: underline;
      text-underline: single;
    }

    p.MsoHeader {
      mso-style-name: 页眉;
      margin: 0pt;
      margin-bottom: 0.0001pt;
      border-top: none;
      mso-border-top-alt: none;
      border-right: none;
      mso-border-right-alt: none;
      border-bottom: none;
      mso-border-bottom-alt: none;
      border-left: none;
      mso-border-left-alt: none;
      padding: 1pt 4pt 1pt 4pt;
      layout-grid-mode: char;
      mso-pagination: none;
      text-align: justify;
      text-justify: inter-ideograph;
      font-family: Calibri;
      mso-fareast-font-family: 宋体;
      mso-bidi-font-family: 'Times New Roman';
      font-size: 9pt;
      mso-font-kerning: 1pt;
    }

    p.MsoFooter {
      mso-style-name: 页脚;
      margin: 0pt;
      margin-bottom: 0.0001pt;
      layout-grid-mode: char;
      mso-pagination: none;
      text-align: left;
      font-family: Calibri;
      mso-fareast-font-family: 宋体;
      mso-bidi-font-family: 'Times New Roman';
      font-size: 9pt;
      mso-font-kerning: 1pt;
    }

    p.MsoCommentText {
      mso-style-name: 批注文字;
      margin: 0pt;
      margin-bottom: 0.0001pt;
      mso-pagination: none;
      text-align: left;
      font-family: Calibri;
      mso-fareast-font-family: 宋体;
      mso-bidi-font-family: 'Times New Roman';
      font-size: 10.5pt;
      mso-font-kerning: 1pt;
    }

    span.msoIns {
      mso-style-type: export-only;
      mso-style-name: '';
      text-decoration: underline;
      text-underline: single;
      color: blue;
    }

    span.msoDel {
      mso-style-type: export-only;
      mso-style-name: '';
      text-decoration: line-through;
      color: red;
    }

    table.MsoNormalTable {
      mso-style-name: 普通表格;
      mso-style-parent: '';
      mso-style-noshow: yes;
      mso-tstyle-rowband-size: 0;
      mso-tstyle-colband-size: 0;
      mso-padding-alt: 0pt 5.4pt 0pt 5.4pt;
      mso-para-margin: 0pt;
      mso-para-margin-bottom: 0.0001pt;
      mso-pagination: widow-orphan;
      font-family: 'Times New Roman';
      font-size: 10pt;
      mso-ansi-language: #0400;
      mso-fareast-language: #0400;
      mso-bidi-language: #0400;
    }

    table.MsoTableGrid {
      mso-style-name: 网格型;
      mso-tstyle-rowband-size: 0;
      mso-tstyle-colband-size: 0;
      mso-padding-alt: 0pt 5.4pt 0pt 5.4pt;
      mso-border-top-alt: 0.5pt solid windowtext;
      mso-border-left-alt: 0.5pt solid windowtext;
      mso-border-bottom-alt: 0.5pt solid windowtext;
      mso-border-right-alt: 0.5pt solid windowtext;
      mso-border-insideh: 0.5pt solid windowtext;
      mso-border-insidev: 0.5pt solid windowtext;
      mso-para-margin: 0pt;
      mso-para-margin-bottom: 0.0001pt;
      mso-pagination: none;
      text-align: justify;
      text-justify: inter-ideograph;
      font-family: 'Times New Roman';
      font-size: 10pt;
      mso-ansi-language: #0400;
      mso-fareast-language: #0400;
      mso-bidi-language: #0400;
    }

    table.19 {
      mso-style-name: 'Table Normal';
      mso-tstyle-rowband-size: 0;
      mso-tstyle-colband-size: 0;
      mso-padding-alt: 0pt 0pt 0pt 0pt;
      mso-para-margin: 0pt;
      mso-para-margin-bottom: 0.0001pt;
      mso-pagination: widow-orphan;
      font-family: 'Times New Roman';
      font-size: 10pt;
      mso-ansi-language: #0400;
      mso-fareast-language: #0400;
      mso-bidi-language: #0400;
    }

    @page {
      mso-page-border-surround-header: no;
      mso-page-border-surround-footer: no;
    }

    @page Section0 {
      margin-top: 72pt;
      margin-bottom: 72pt;
      margin-left: 90pt;
      margin-right: 90pt;
      size: 595.3pt 841.9pt;
      layout-grid: 15.6pt;
      mso-header-margin: 42.55pt;
      mso-footer-margin: 49.6pt;
      mso-header: url('2024\.10\.12Mpr隐私政策-定稿.files/header.html') h0;
      mso-footer: url('2024\.10\.12Mpr隐私政策-定稿.files/header.html') f0;
    }

    div.Section0 {
      page: Section0;
    }
  </style>
</head>

<body style="tab-interval: 21pt; text-justify-trim: punctuation">
  <!--StartFragment-->
  <div class="Section0" style="layout-grid: 15.6pt">
    <p class="MsoNormal" align="center" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: center;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:MicrosoftYaHei-Bold;color:rgb(59,63,71);
font-weight:bold;font-size:24.0000pt;mso-font-kerning:0.0000pt;">
          <font face="MicrosoftYaHei-Bold">Mpr</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:MicrosoftYaHei-Bold;color:rgb(59,63,71);
font-weight:bold;font-size:24.0000pt;mso-font-kerning:0.0000pt;">
          <font face="MicrosoftYaHei-Bold">隐私政策</font>
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;font-size:18.0000pt;
mso-font-kerning:1.0000pt;">
        <o:p>&nbsp;</o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">欢迎您使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">！</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:MicrosoftYaHei-Bold;color:rgb(59,63,71);
font-weight:bold;font-size:13.0000pt;mso-font-kerning:0.0000pt;"><br /></span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:18.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">本隐私政策将帮助您了解以下内容：</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:18.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">一、相关词语解释</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">二、我们如何收集和使用信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">三、我们如何使用</font>
          <font face="仿宋">Cookies和同类技术 </font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">四、</font>
          <font face="仿宋">我们如何共享、转让、公开披露您的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">五、</font>
          <font face="仿宋">如何访问和控制您的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">六、</font>
          <font face="仿宋">我们如何存储和保护您的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">七、</font>
          <font face="仿宋">我们如何处理未成年人的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">八、</font>
          <font face="仿宋">隐私政策更新</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="仿宋">九、</font>
          <font face="仿宋">如何联系我们</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p>&nbsp;</o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">一、相关词语解释</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:MicrosoftYaHei-Bold;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">&nbsp;</span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:14.0000pt;mso-font-kerning:1.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">个人信息：个人信息是以电子或者其他方式记录的与已识别或者可识别的自然人有关的各种信息，不包括匿名化处理后的信息。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          敏感个人信息：敏感个人信息是一旦泄露或者非法使用，容易导致自然人的人格尊严受到侵害或者人身、财产安全受到危害的个人信息，包括生物识别、宗教信仰、特定身份、医疗健康、金融账户、行踪轨迹等信息，以及不满十四周岁未成年人的个人信息。敏感个人信息在本隐私政策中会做
        </font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">加粗星号</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">提示。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">未成年人：不满十八周岁的自然人为未成年人。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">处理：个人信息的处理包括个人信息的收集、存储、使用、加工、传输、提供、公开、删除等。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">匿名化：是指个人信息经过处理无法识别特定自然人且不能复原的过程。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">个人信息处理者：是指在个人信息处理活动中自主决定处理目的、处理方式的组织、个人。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">二、我们如何收集和使用信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">因向您提供的产品和</font>
        <font face="宋体">
          /或服务种类众多，我们将根据具体产品和/或服务范围和不同功能，遵循合法、正当和必要原则收集和使用您的个人信息。如果我们欲将您的个人信息用于本隐私政策未载明的其它用途，或基于特定目的将收集而来的信息用于其他目的，我们会及时以合理的方式向您告知，并在使用前再次征得您的同意。以下我们向您逐一告知各功能需要的个人信息类型，并在您使用具体功能场景时向您提示需授权的权限：
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.1 账号注册、登录 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）账户创建与登录 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">注册</font>
        <font face="宋体">/登录：当您注册、登录我们的产品/服务时，为了帮您顺利完成注册/登录流程，我们会收集由您主动提供给我们的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱地址</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*、</span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱验证码</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*</span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">信息。收集手机号码是为了满足相关法律法规的网络实名制需求，如您拒绝提供手机号进行核验，将导致注册</font>
        <font face="宋体">/登录失败，但影响您</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">平台使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">功能。您应知悉，</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱地址</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*、</span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱验证码</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*</span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">属于您的个人敏感信息，您只有提供真实准确的上述信息，才能成功创建和</font>
        <font face="宋体">/或登录账号并使用产品所提供的相关功能。如您拒绝提供可能导致您无法使用我们提供的此项功能，请您谨慎考虑后再提供。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">授权登录（</font>
        <font face="宋体">QQ/微信</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">/新浪微博</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">平台目前支持用户使用</font>
        <font face="宋体">QQ/微信</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">/新浪微博进</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          行登录操作，如您使用第三方平台的账号登录的，我们将根据您的授权获取该第三方账号下的相关信息（昵称、头像、地区和性别信息，具体以您的授权内容为准），请您仔细阅读第三方合作伙伴服务的用户协议或隐私政策</font>
        <font face="宋体">: 《QQ隐私保护指引》 《微信隐私保护指引》</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">《微博隐私政策》</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（注：以</font>
        <font face="宋体">QQ/微信</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">/微博</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          官方信息为准）。我们承诺，获取上述信息是用于为您提供账号登录服务以及保障您的账号安全，防范安全风险。如您拒绝授权此类信息，则您将无法使用第三方平台的账号登录我们平台，但不影响我们提供的其他功能的正常使用。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">账号密码登录：您可选择创建账号后，在</font>
        <font face="宋体">&lt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">登录</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">-</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">忘记密码</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&gt;修改密码，后续即可使用</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">Mpr</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">账号</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">+</span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">账号密码</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">的方式登录您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">Mpr</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">账号</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">。您的密码将以加密形式进行自动存储、传输、验证，我们不会以明文方式存储、传输、验证您的密码。您在保管、输入、使用您的密码时，应当对物理环境、电子环境审慎评估，以防密码外泄。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）资料维护 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">为了维护您的基本资料，提升您的服务体验，您可以选择填写完善个人信息，包括您的头像、昵称</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、银行账户信息等</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">。如您选择不提供这些信息，不会影响您使用基础服务功能。其中，当您设置</font>
        <font face="宋体">/更换头像时，我们会请求您授权</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">相机权限</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">或</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">存储权限（</font>Android系统）/照片权限（iOS系统）*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">，如果您拒绝提供</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">，</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">将无法使用此功能，但不影响您正常使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">其他功能。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.2 内容展示、浏览、下载、播放 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们将为您提供</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">一些</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">内容的展示、浏览、播放和下载功能，在此过程中为保障上述功能实现、履行网络系统运营安全义务，当您使用我们的产品和</font>
        <font face="宋体">/或服务时，我们会自动收集您的个人上网记录，并作为日志信息保存，包括您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">操作日志</font>*（浏览记录*、搜索记录*、点击行为*、下载行为*、访问日期和时间*）
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、服务日志、开发日志及闪退日志。</font> &nbsp;
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.3 搜索 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">的搜索功能时，我们将收集您查询的字词和收藏记录。我们收集这些信息是为了向您提供您所需要的内容和可能更感兴趣的服务，同时亦可以改进我们的产品和</font>
        <font face="宋体">/或服务。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.4
          &nbsp;个性化推荐为向您提供更便捷、更符合您个性化需求的推荐、搜索、广告及推送服务，我们会根据您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">设备唯一识别符</font>
          <font face="宋体">*</font>
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、手机运营商信息、您使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">时的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">阅读记录</font>*、浏览记录*、收藏记录*、搜索记录*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">，提取您的偏好特征，用于向您展示、推送消息。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="宋体">身份认证与账户找回</font> </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;font-size:12.0000pt;
mso-font-kerning:0.0000pt;">
        <font face="宋体">您可以选择人工认证方式完成实名认证程序，</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;font-weight:bold;
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">为了完成实名认证服务，我们将依据相关法律法规获取并存储您的个人身份信息，包括：姓名</font>
          *、出生日期、身份证明文件的正反面图片*（包括港澳居民来往内地通行证、台湾居民来往内地通行证、中国签发护照、外国人永久居留证、其他国家或地区身份证明）、手持相应身份证明文件的照片*。
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          您理解并确认，我们会以最大努力保障您的个人信息安全，我们收集这些信息是基于实名认证程序要求，仅供完成实名认证的目的，或其他法律法规所规定的用途，未经您明示授权不会用作其他目的。您可以拒绝提供，但将可能导致您无法登录账号并使用相关产品功能，请您谨慎考虑后再选择是否提供。基于法律法规的相关要求，您的实名认证信息还可能被我们用于青少年
        </font>
        <font face="宋体">/儿童保护、防沉迷相关的产品或服务。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）账号找回 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">如果您的账号遗失，您可以通过</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&lt;忘记密码&gt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">重置密码。但在此过程中，需您提供一些信息以验证您的身份。当您重置密码</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">时</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">，需提供</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱地址</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*、</span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱验证码</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*</span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">作为账号身份验证。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;background:rgb(255,255,0);
mso-highlight:rgb(255,255,0);">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">6</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="宋体">消息通知</font> </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          畅译与您之间会通过消息推送、站内信、短信、电话的方式进行互动。请您知悉并同意，为了向您提供消息告知、身份验证、安全验证的功能，以及向您提供您可能感兴趣的服务、功能或营销活动等商业性信息，我们可能会在前述过程中，收集您的
        </font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">手机号码</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、手机运营商信息、</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">设备唯一标识符</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">账号、设备品牌及型号、操作系统和应用程序版本、网络信息、设备屏幕信息。我们在运营中可能会通过前述一种或多种方式发送多类通知</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">，您可以通过</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&lt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">个人信息</font>
        <font face="宋体">-我的私信</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&gt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">中查看通知内容</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">7</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="宋体">客服、意见反馈</font> </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您通过客服进行咨询或使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">网站</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">的意见反馈功能时，为了您的账号与系统安全，我们可能需要您先行提供</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">账号信息</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">，并与您之前的个人信息相匹配以验证您的用户身份。在您使用客服或其他用户响应功能时，我们可能还会需要收集您的如下个人敏感信息：联系方式（您与我们联系时使用的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">手机号码</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">或您向我们主动提供的其他联系方式）、您与我们的沟通信息（包括文字</font>
        <font face="宋体">/图片/音视频/通话记录形式）、日志信息、与您需求相关联的其他必要信息。我们收集这些信息是为了调查事实与帮助您解决问题，如您拒绝提供可能导致您无法使用我们的客服等用户响应机制。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">8</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="宋体">产品安全保障功能</font> </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们需要收集您的一些信息来保障和评估您使用我们产品和</font>
        <font face="宋体">/或服务时的账号与系统安全、应用的稳定运行，并协助提升我们的产品和/或服务的安全性、可靠性和稳定性，此功能下可能收集您的如下个人信息：
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）账号信息：</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">Mpr</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">账号</font>*、手机号码*、电子邮箱地址*、实名认证信息*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）登录信息：登录时间、</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">IP地址*</span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）设备信息：</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">设备唯一识别符</font>
          *（Android：设备MAC地址*、IMEI*、IMSI*、MEID*、AndroidID*、安卓广告ID*、OAID*；iOS：设备MAC地址*、IDFA*、IDFV*）*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">设备品牌及型号、设备硬件信息（设备传感器信息、</font>
        <font face="宋体">CPU型号及频率、电池信息、内核版本）、操作系统和应用程序版本；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4）其他信息：</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">收益</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">金额</font>*、操作日志*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          我们会根据上述信息来综合判断您账号、账户及交易风险，进行身份验证、客户服务、检测及防范安全事件、诈骗监测、存档和备份用途，并依法采取必要的记录、审计、分析、处置措施，一旦我们检测出存在或疑似存在账号安全风险时，我们会使用相关信息进行安全验证与风险排除，确保我们向您提供的产品和
        </font>
        <font face="宋体">/或服务的安全性，以用来保障您的权益不受侵害。同时，当发生账号或系统安全问题时，我们会收集这些信息来优化我们的产品和/或服务。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">9</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="宋体">桌面组件</font> </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">提供的桌面组件服务时，为了判断您当前桌面组件状态，以确保点击组件并进入产品后能够正常使用产品功能，我们会使用系统广播能力，这可能会导致在系统界面</font>
        <font face="宋体">/日志/后台记录中（取决于您的终端型号），显示您产品有自启动的情况，但这是保证我们产品能正常提供服务所必须的步骤。您可以自行删除相应桌面组件。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">2.1</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">0</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;<font face="宋体">基于调用设备权限提供的扩展功能</font> </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">为了提升您在</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          的阅读体验，我们的以下扩展功能中会涉及调用设备的权限，可能会收集和使用您的个人信息。如果您不提供这些个人信息，您依然可以进行基本功能的使用，但可能无法享受相应扩展功能带来的乐趣。涉及的设备权限及其对应扩展功能包括：
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;</span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">相机权限</font>*（CAMERA）
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您使用上传</font>
        <font face="宋体">/更新账号头像</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、票据、下载等</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">功能时，我们将需要获取您的相机权限</font>
        <font face="宋体">*，并收集您提供的图片、视频内容信息。如您拒绝提供该权限，可能导致您无法使用与相机权限相关的扩展功能，但是不影响您使用此外的其他功能。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">2</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">电话状态权限</font>*（READ_PHONE_STATE-仅Android端）
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您首次启动</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">App时（Android10以下系统），我们会向您申请获取电话状态权限，目的是获取您的设备唯一识别符*，以便完成安全风控、进行统计和服务推送；同时也是为了避免打断您</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">进程，便于我们检测您的来电状态。如您拒绝提供该权限，可能导致您无法使用相关的扩展功能，但是不影响您使用此外的其他功能。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">3</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）</font>
        <font face="宋体">读取和</font>
        <font face="宋体">/或写入读取</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">外置存储器权限</font>*（READ_EXTERNAL_STORAGE
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">、</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">WRITE_EXTERNAL_STORAGE-仅Android端）</span></b><span
        style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您使用将</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">文字、音视频</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">资料存入</font>
        <font face="宋体">SD卡、或使用读取SD内资料的功能时，需要获取您的读取/写入外置存储器权限，并读取/写入文件信息。如您拒绝提供该权限，可能导致您无法使用相关的扩展功能，但是不影响您使用此外的其他功能。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">4</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）</font>
        <font face="宋体">添加</font>
        <font face="宋体">/使用</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">照片权限</font>*（Photo-仅iOS端）
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">当您使用上传</font>
        <font face="宋体">/更新账号头像、</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、票据、下载等</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">业务功能，选择上传</font>
        <font face="宋体">/保存至照片时，我们将需要获取您的照片权限，并收集您提供的图片、视频内容信息。如您拒绝提供该权限，可能导致您无法使用相关的扩展功能，但是不影响您使用此外的其他功能。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">5</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）网络权限（</font>
        <font face="宋体">Local
          Network-仅iOS端）：由于iOS端支持用户选择是否使用蜂窝网络或WIFI，而网络权限全部关闭后会无法使用任何联网产品和功能，因此我们为了向您提供产品与服务以及平台内容读取与更新，需要获取您的网络权限。如您拒绝提供该权限，可能导致您无法使用相关的扩展功能，但是不影响您使用此外的其他功能。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">6</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;</span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">剪切板权限</font>*（ClipboardManager）
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          当您复制文本信息、标题、短信验证码，分享或接收被分享信息时，您的剪切板功能可能会被调用。剪切板信息仅会在您的设备上进行处理，我们不会存储您的剪切板信息用于任何其他途径。如您拒绝提供该权限，可能导致您无法使用相关的扩展功能，但是不影响您使用此外的其他功能。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">(</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">7</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">) </font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">日历权限</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          当您在应用中使用提醒或预约功能时，我们会申请获取您的设备日历权限。在您开启相关权限后，我们会访问您的日历，帮助您将相关活动事件保存至日历中，以在活动开始时通过日历提醒通知您，帮助您设置、完成与修改活动预约。如您拒绝提供该权限，可能导致您无法使用相关的扩展功能，但是不影响您使用此外的其他功能。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">8</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）任务信息权限（</font>
        <font face="宋体">GET_TASKS-仅Android端）：当您使用推送服务、广告服务、异常反馈功能时，需要获取当前或最近运行的任务。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">9</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）前台服务权限（</font>
        <font face="宋体">FOREGROUND_SERVICE-仅Android端）：当您使用播放服务，需要获取前台服务权限。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;background:rgb(255,255,0);
mso-highlight:rgb(255,255,0);">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">上述扩展功能可能需要您在您的设备中向我们开启您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">相机权限</font>*、电话状态权限*、外置存储器权限*、照片权限*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">网络权限</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">、剪切板权限</font>*、日历权限*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">任务信息权限、前台服务权限以实现这些功能涉及的信息收集和使用。您可以在</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">App或设备系统中查看、开启/关闭上述权限的状态。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          请您注意，您开启这些权限即代表您授权我们可以收集和使用这些个人信息来实现上述功能，您关闭权限即代表您取消了这些授权，我们将不会再继续收集和使用您的这些个人信息，也无法为您提供上述与这些授权所对应的功能。如您拒绝提供，仅会使您无法使用该功能，但并不影响您正常使用产品和
        </font>
        <font face="宋体">/或服务的其他功能。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">请您理解并知悉，根据相关法律法规及国家标准，在以下情形中，我们会收集、使用您的个人信息，而无需征求您的同意</font>
        <font face="宋体">: </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）与国家安全、国防安全等国家利益直接相关的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）与公共安全、公共卫生、公众知情等重大公共利益直接相关的；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）与刑事侦查、起诉、审判和判决执行等直接相关的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4）为我们履行法定职责或者法定义务所必需的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">5）为应对突发公共卫生事件，或者紧急情况下为保护自然人的生命健康和财产安全所必需；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">6）依法在合理的范围内处理您自行公开或其他已经合法公开的您的个人信息；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">7）为订立或者履行您作为一方当事人的合同所必需； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">8）为公共利益实施新闻报道、舆论监督等行为在合理的范围内处理您的个人信息；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">9）法律法规规定的其他情形。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          请您理解，我们向您提供的功能和服务是不断更新和发展的，如果某一功能或服务未在前述说明中且可能收集您的个人信息，我们会在前述功能或服务正式启用前，通过页面提示、交互流程、网站公告等方式另行向您说明信息收集的内容、范围和目的，以征得您的同意。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">三、我们如何使用</font>
          <font face="宋体">Cookies和同类技术 </font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">在您使用我们的产品和</font>
        <font face="宋体">/或服务时，我们可能通过放置安全的Cookie及相关技术收集您的信息，包括：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">登录信息</font>*、浏览信息*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、偏好设置。</font>
        <font face="宋体">Cookie和同类技术收集该类信息是为了您使用我们的产品与/或服务的必要、简化您重复操作的步骤（如注册、登录）、便于您查看使用历史（如</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">浏览</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、搜索历史）、向您提供更切合您个人需要的服务内容和您可能更感兴趣的内容、保护您的信息和账号安全性、提升我们的产品和服务等。我们承诺，我们不会将</font>
        <font face="宋体">Cookie与同类技术用于本隐私政策所述目的之外的任何用途。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您可以根据自己的偏好管理或删除</font>
        <font face="宋体">
          Cookie。您可以清除计算机或移动设备上保存的所有Cookie，您有权接受或拒绝Cookie，您也可以通过浏览器设置管理Cookie。您理解并知悉：我们的某些产品/服务只能通过使用Cookie或同类技术才可得到实现，如您拒绝使用或删除的，您可能将无法正常使用我们的相关产品与/或服务或无法通过我们的产品与/或服务获得最佳的服务体验。Cookie
          对提升用户的网络使用体验十分重要，我们使用Cookie一般出于以下目的：
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1） 优化登录体验：
          Cookie可以在您进行注册、登录时，通过存储账号信息，帮您填入您最后登入的账号名称、简化您重复操作的步骤；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）
          安全：Cookie可帮助我们保障数据和服务的安全性，排查针对我们的产品和/或服务的作弊、黑客、欺诈行为；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3） 偏好设置：使用Cookie及其同类技术用来保存用户简单的个性化设置。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:微软雅黑;color:rgb(59,63,71);
font-size:10.5000pt;mso-font-kerning:0.0000pt;">&nbsp;</span><span style="mso-spacerun:&#39;yes&#39;;font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">四、我们如何共享、转让、公开披露您的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.1 信息的共享 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们非常重视保护您的个人信息。未经您的授权同意，我们不会与任何公司、组织和个人共享您的个人信息，除非存在以下一种或多种情形：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.1.1 在获得您的明确同意或授权或您主动选择的情况下共享 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">特别提示，您在使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">过程中主动公开的个人信息可被任何第三方阅读、收集和使用，可能会同步到我们的关联产品</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          ，并且可被一些与我们没有关联关系的第三方独立地复制、存储。请注意，您主动公开的个人信息可能包含您的个人身份信息、个人财产信息等敏感信息。请您谨慎考虑披露您的相关个人敏感信息。您主动公开的信息中可能包含他人的个人信息，请您务必取得他人的合法授权，避免非法泄露他人的个人信息。您可以控制您分享信息的范围，也可通过服务中的路径设置或我们提供的指引联系我们删除您公开分享的信息。但请您注意，这些信息仍可能由其他用户或不受我们控制的非关联第三方独立地保存。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.1.2 在法定情形下的共享 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们可能会根据法律法规规定、诉讼争议解决需要，或按行政、司法机关依法提出的要求，对外共享您的个人信息。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.1.3 与授权业务合作伙伴必要共享 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">请您理解并同意，为了必要</font>
        <font face="宋体">
          /合理的业务的顺利开展、满足您的要求，我们可能与第三方合作为您提供某些服务，向部分业务合作伙伴共享您的部分信息。我们将在数据共享前对授权合作伙伴进行数据安全能力及数据共享风险进行评估，并将通过协议约束其只能接触到为其履行职责所需信息，且我们不得将您的个人信息用于其他任何目的。如超出或改变共享目的使用您个人信息，我们将要求其另行遵循您的明确同意。目前，我们的合作伙伴包括以下类型，具体请查阅
          《</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">与第三方个人信息共享清单》：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）合作的第三方SDK服务商：我们可能会与第三方软件工具开发包（SDK）或其他类似的应用程序类服务商分享您的个人信息，以保障平台的稳定运行、功能实现，当前已接入的SDK列表见
          《第三方个人信息共享清单》；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）支付服务提供方：为帮助您完成款项支付，我们会将您的用户标识信息、</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">交易金额、</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">订单信息</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">提供给支付服务的合作方；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）短信服务提供商：我们会向其提供您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">手机号码</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、短信内容以实现对应的短信服务；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">邮件</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">服务提供商：我们会向其提供您的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">邮箱地址</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">*</span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">、</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">邮件</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">内容以实现对应的</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">邮件</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">服务；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">5</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）客服反馈服务提供方：为了提供更好的客服反馈服务，我们会将您的用户信息、设备信息及反馈内容提供给在线客服服务提供方。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          请您放心，我们会努力审查并努力要求合作方的合法合规性与安全性。在我们将可以联系或识别您身份的个人信息与授权合作伙伴共享时，我们会最大程度保障您的信息安全。此外，我们强烈建议您在使用任何第三方合作服务前先行查看其隐私条款，第三方的具体信息收集和使用情况请以第三方运营方的隐私政策或相关声明为准。为保障您的合法权益，当您发现这等
        </font>
        <font face="宋体">SDK或其他类似的应用程序存在风险时，建议您立即终止相关操作并及时与我们取得联系。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.2 信息的转让 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">转让是指将个人信息控制权向其他公司、组织或个人转移的过程。原则上我们不会将您的个人信息转让，但以下情况除外：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1） 您自行提出要求的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2） 事先已征得您的明确授权同意； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）
          如我们进行兼并、收购、重组、分立、破产、资产转让或类似的交易，而您的个人信息有可能作为此类交易的一部分而被转移，我们会要求新持有人继续遵守和履行该隐私政策的全部内容（包括使用目的、使用规则、安全保护措施等），否则我们将要求其重新获取您的明示授权同意；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4） 法律法规等规定的其他情形。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          如具备上述事由确需转让的，我们会在转让前通过推送通知、公告形式向您告知转让的信息的目的、类型（如涉及您的个人敏感信息的，我们还会向您告知涉及的敏感信息的内容），并告知接收方的名称或者姓名和联系方式，在征得您的授权同意后再转让，但法律法规另有规定的或本政策另有约定的除外。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.3 信息的公开披露 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">公开披露是指向社会或不特定人群发布信息的行为。除了因需要对违规账号、欺诈行为等进行处罚公告、公布中奖</font>
        <font face="宋体">
          /获胜者等名单时脱敏展示相关信息等必要事宜而进行的必要披露外，我们不会对您的个人信息进行公开披露，如因经法律授权或具备合理事由确需公开披露的，我们会在公开披露前向您告知公开披露的信息的目的、类型（如涉及您的个人敏感信息的，我们还会向您告知涉及的敏感信息的内容），并在征得您的明示同意后再公开披露，但法律法规另有规定的或本政策另有约定的除外。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          根据法律、法规的要求、强制性的行政执法或司法要求所必须提供您个人信息的情况下，我们可能会依据所要求的个人信息类型和披露方式公开披露您的个人信息。在符合法律法规的前提下，当我们收到上述披露信息的请求时第一时间且审慎审查其正当性、合理性、合法性，我们会要求必须出具与之相应的法律文件，如传票或调查函。我们坚信，对于要求我们提供的信息，应该在法律允许的范围内尽可能保持透明。我们对所有的请求都进行了慎重的审查，以确保其具备合法依据，且仅限于执法部门因特定调查目的且有合法权利获取的数据。在法律法规许可的前提下，我们披露的文件均在加密密钥的保护之下。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4.4 例外情形 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">未经您的同意，畅译不会向畅译以外的任何公司、组织和个人共享、转让、公开披露您的个人信息，但下列情况除外：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1） 与我们履行法律法规规定的义务相关的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2） 与国家安全、国防安全直接相关的；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3） 与公共安全、公共卫生、重大公共利益直接相关的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4）
          与犯罪侦查、起诉、审判和判决执行等直接相关的；或根据法律法规的要求、行政机关或公检法等有权机关的要求的；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">5） 出于维护您或其他个人的生命、财产等重大合法权益但又很难得到您本人同意的；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">6）
          个人信息是您自行向社会公开的或者是我们从合法公开披露的渠道（如合法的新闻报道、政府信息公开等渠道）中收集到的；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">7） 根据与您签订和履行相关协议或其他书面文件所必需的； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">8） 法律法规等规定的其他情形。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:MicrosoftYaHei-Bold;color:rgb(59,63,71);
font-weight:bold;font-size:13.0000pt;mso-font-kerning:0.0000pt;"><br /></span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">五、如何访问和控制自己的个人信息</font>
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您对我们产品和</font>
        <font face="宋体">
          /或服务中的您的个人信息享有访问与控制的权利，包括：您可以访问、复制、更正/修改、删除您的个人信息，也可以撤回之前作出的对您个人信息的同意，同时您也可以注销您的账号。为便于您行使您的上述访问与控制权，我们在产品的相关功能页面为您提供了操作指引和操作设置，您可以自行进行操作，如您在操作过程中有疑惑或困难的可以通过文末的方式联系我们来进行控制，我们会及时为您处理。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5.1 访问您的个人信息 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您可以在我们的产品和</font>
        <font face="宋体">/或服务中查询或访问您的相关个人信息，包括： </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
          mso-list: l0 level1 lfo1;
        ">
      <!--[if !supportLists]--><span
        style="font-family: 宋体; color: rgb(59, 63, 71); font-size: 12pt; mso-font-kerning: 0pt"><span
          style="mso-list: Ignore">（1）</span></span><!--[endif]--><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">账号信息：您可以登录您的个人账号，通过</font>
        <font face="宋体">&lt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">个人信息</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&gt;访问的账号中的个人资料信息，包括：头像、昵称； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">2</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">）</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">收入和提现</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">信息</font>
        <font face="宋体">*：您可以通过访问&lt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">统计</font>
        <font face="宋体">-每日统计/提现管理&gt;查看您的分成收入统计和历史提现申请</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">；</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">3</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          ）其他信息：如您在此访问过程中遇到操作问题的或如需获取其他前述无法获知的个人信息内容，您可通过文末提供的方式联系我们，我们将在核实您的身份后在合理期限内向您提供，但法律法规另有规定的或本政策另有约定的除外。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5.2 复制您的个人信息 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您有权获取您的个人信息副本。如您需要获取我们收集的您的个人信息副本，请您随时联系我们（联系方式详见</font>
        <font face="宋体">“九、如何联系我们”）。在符合相关法律规定且技术可行的前提下，我们将根据您的要求向您提供您的个人信息副本。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5.3 更正/修改您的个人信息 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您可以在我们的产品和</font>
        <font face="宋体">
          /或服务中更正/修改您的相关个人信息。为便于您行使您的上述权利，我们为您提供了在线自行更正/修改和向我们提出更正/修改申请两种方式。出于安全性和身份识别（如号码申诉服务）的考虑，您可能无法修改注册时提供的初始注册信息及其他验证信息。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">对于您的部分个人信息，我们在产品的相关功能页面为您提供了操作指引和操作设置，您可以直接进行更正</font>
        <font face="宋体">/修改，操作指引如下： </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）“头像/昵称/</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">个人</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">简介</font>
        <font face="宋体">”信息的更正/修改界面为&lt;个人</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">信息</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&gt;； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">“</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">账号密码</font>*
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">”信息的更正/修改界面为&lt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">登录</font>
        <font face="宋体">-忘记密码</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">&gt;。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5.4 删除您的个人信息 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">一般而言，我们只会在法律法规规定或必需且最短的时间内保存您的个人信息。为便于您行使您的上述删除权，我们为您提供了在线自行删除和向我们提出删除申请两种方式。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">对于您的部分个人信息，我们在产品的相关功能页面为您提供了操作指引和操作设置，您可以直接进行删除，一旦您删除后，我们即会对此类信息进行删除或匿名化处理，除非法律法规另有规定</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;background:rgb(255,255,0);
mso-highlight:rgb(255,255,0);">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">在以下情形下，您可以直接向我们提出删除您个人信息的请求，但已做数据匿名化处理或法律法规另有规定的除外：</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）如果我们处理个人信息的行为违反法律法规； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）如果我们收集、使用您的个人信息，却未征得您的同意； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）如果我们处理个人信息的行为违反了与您的约定； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4）如果您不再使用我们的产品和/或服务，或您注销了账号； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">5）如果我们不再为您提供产品和/或服务。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5.5 撤回同意权 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">您可以通过关闭设备功能改变您授权我们继续收集个人信息的范围或撤回您的授权。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5.6 注销您的账号 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们为您提供账号注销权限，您可以通过</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">网站</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">中功能操作或联系我们的客服注销您的账号，操作路径</font>
        <font face="宋体">&lt;</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">个人信息</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          -账号注销&gt;。一旦您注销账号，将无法使用我们提供的全线用户产品和/或服务且自动放弃已有的权益，因此请您谨慎操作。除法律法规另有规定外，注销账号之后，我们将停止为您提供我们所有的产品和/或服务，您曾通过该账号使用的我们的产品和/或服务下的所有内容、信息、数据、记录将会被删除或匿名化处理（但法律法规另有规定或监管部门另有要求的除外，如依据《中华人民共和国网络安全法》规定，您的网络操作日志将至少保留六个月的时间）。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">在您访问、修改和删除相关信息时，我们可能会要求您进行身份验证，以保障账号的安全。请您理解，由于技术所限、法律或监管要求，我们可能无法满足您的所有要求，我们会在合理的期限内答复您的请求。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:微软雅黑;color:rgb(59,63,71);
font-size:10.5000pt;mso-font-kerning:0.0000pt;">
        <o:p>&nbsp;</o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">六、我们如何存储和保护您的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">6.1
          信息存储您在注册账号或使用本服务的过程中，可能需要收集和使用一些必要的信息（详见二-我们如何收集和使用信息）。若您填写的真实信息不完整，则可能在使用对应服务过程中受到限制。一般而言，我们仅为实现目的所必需的最短时间保留您的个人信息。当我们的产品和/或服务发生停止运营的情形时，我们将采取通知或公告的形式通知您，并在合理的期限内删除或匿名化处理您的个人信息。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">个人信息存储和超期处理方式：</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）我们在中华人民共和国境内运营中收集和产生的个人信息，均存储在中国境内。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）根据相关法律规定，日志信息存储期限不少于6个月；交易信息保存时间自交易完成之日起不少于三年；记录备份保存时间不少于60日。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）除非法律有强制的存留要求；在您的个人信息超出保留期间后，我们会根据适用的反馈要求删除您的个人信息、或使其匿名化处理。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">6.2 信息保护 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">6.2.1
          保护用户个人信息是畅译的一项基本原则。我们视用户信息安全与隐私保护为自己的”生命线”。我们致力于提升信息处理透明度，增强您对信息管理的便捷性，保障您的信息及通信安全。严格遵守法律法规，遵循以下隐私保护原则，为您提供更加安全、可靠的服务：
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）
          安全可靠：我们竭尽全力通过合理有效的信息安全技术及管理流程，防止您的信息泄露、损毁、丢失；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2） 保护通信秘密：我们严格遵照法律法规，保护您的通信秘密，为您提供安全的通信服务；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3） 合理必要：为了向您和其他用户提供更好的服务，我们仅收集必要的信息；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4）
          清晰透明：我们努力使用简明易懂的表述，向您介绍隐私政策，以便您清晰地了解我们的信息处理方式；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">5）
          将隐私保护融入产品设计：我们在产品和/或服务开发的各个环节，综合法律、产品、设计的多方因素，融入隐私保护的理念。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">6.2.2
          畅译对相关信息会采用专业加密存储与传输方式，保障用户个人信息的安全，运用各种安全技术和程序建立完善的管理制度来保护您的个人信息，以免遭受未经授权的访问、使用或披露。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）
          数据安全传输方面，采用传输层安全协议等密码技术，通过Https等方式防止传输链路被窃听、截取的风险，建立安全的隐私数据采集环境，保证数据采集的私密性和完整性；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）
          数据安全存储方面，对数据进行分类分级处置并对用户个人敏感信息采取独立、加密存储等额外的安全保护措施；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）
          数据访问和使用的安全控制方面，实施严格的数据权限控制机制，采取多重身份认证技术，并对能够处理您的信息的行为进行监控，避免数据被违规访问和未授权使用；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4） 其他实现数据安全保护的措施。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">6.2.3
          为防止安全事故的发生，我们制定了妥善的预警机制和应急预案。一旦发生个人信息安全事件，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议和对您的补救措施，并立即启动应急预案，力求将损失最小化。我们将及时将事件相关情况以电话、推送通知等方式告知您。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">&nbsp;</span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">七、我们如何处理未成年人的个人信息</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">7.1
          我们高度重视未成年人个人信息的保护问题，并持续探索更新的未成年人个人信息保护方式。如您为未满18周岁的未成年人（尤其是不满14周岁的未成年人）如果您未满18周岁，请在监护人的陪同下阅读本政策，尤其是其中关于未成年人个人信息的条款，并事先取得您的家长或法定监护人的同意。若您是未成年人的监护人，当您对您所监护的未成年人的个人信息有相关疑问时，请通过客服与我们联系。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">7.2 如果您未满18周岁，为保障您的合法权益，我们可能将您使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">相关的信息（包括但不限于您账号的</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:12.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">登录信息</font>
          <font face="宋体">*、使用时间*</font>
        </span></b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">等）提供给您的监护人，使得您的监护人可及时或同步了解您使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">的情况。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">7.3
          若您是未成年人的监护人，请您关注您所监护的未成年人是否是在取得您的授权同意之后使用</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">
          或提供其个人信息。如果您对您所监护的未成年人的个人信息有疑问，请通过本政策底部的方式与我们联系。如果我们发现在未事先获得可证实的监护人同意的情况下收集了未成年人的个人信息，则会尽快删除相关数据。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">八、隐私政策更新</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">畅译可能对本政策进行不定期修改，协议修改后我们将通过站内信、页面提示、标注红点的方式另行向您说明政策的变更范围和内容。根据法律法规要求，我们将在官方网站列明本政策的上一版本</font>
        <font face="宋体">，以便于您查阅。对于重大变更，我们还会提供更为显著的通知（包括通过平台公示通知甚至向您进行弹窗提示），并向您提供自主选择同意的方式，且在征得您明示同意后收集、使用您的个人信息。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">1）我们的服务模式发生重大变化：如处理个人信息的目的、处理的个人信息类型、个人信息的使用方式；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">2）我们在控制权方面发生重大变化：如并购重组引起的所有者变更；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">3）个人信息共享、转让或公开披露的主要对象发生变化； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">4）您参与个人信息处理方面的权利及其行使方式发生重大变化； </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">5）我们负责处理个人信息安全的责任部门、联络方式及投诉渠道发生变化时；
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">（</font>
        <font face="宋体">6）个人信息安全影响评估报告表明存在高风险时 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">为了您能及时接收到通知，建议您在您个人的联系方式更新时及时通知我们。我们鼓励您在每次使用我们服务时都查阅本政策</font>
        <font face="宋体">,您可以在</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">Mpr</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">通过</font>
        <font face="宋体">
          &lt;我的-设置-隐私政策&gt;中查看本政策。如您在本政策更新生效后继续使用我们的服务，即代表您已充分阅读、理解并接受更新后的政策并愿意受更新后的政策约束；如果您不同意本协议的修改，请立即停止使用平台或取消已经获得的服务。
        </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;"><br /></span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
        ">
      <b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <font face="宋体">九、如何联系我们</font>
        </span></b><b><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-weight:bold;font-size:14.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们设立了专门的个人信息保护负责人和专门邮箱，如果您对个人信息保护相关事宜有任何疑问或投诉、建议时，您可通过</font>
        <font face="宋体"><EMAIL></font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">邮件</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">方式</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">或网站内企业微信</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">与我们联系</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">。</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:Calibri;mso-fareast-font-family:宋体;
mso-bidi-font-family:&#39;Times New Roman&#39;;font-size:10.5000pt;mso-font-kerning:1.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">我们将尽快审核所涉问题，并在</font>
        <font face="宋体">15个工作日或法律法规规定的期限内答复您的请求。 </font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <o:p></o:p>
      </span>
    </p>
    <p class="MsoNormal" style="
          margin-bottom: 7.85pt;
          mso-para-margin-bottom: 0.5gd;
          text-autospace: ideograph-numeric;
          page-break-after: avoid;
          mso-pagination: widow-orphan;
          text-align: left;
          line-height: 25pt;
          mso-line-height-rule: exactly;
        ">
      <span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">注：本隐私政策版本更新日期为</font>202
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">4</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">年</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">10</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">月</font>1
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">5</font>
      </span><span style="mso-spacerun:&#39;yes&#39;;font-family:宋体;color:rgb(59,63,71);
font-size:12.0000pt;mso-font-kerning:0.0000pt;">
        <font face="宋体">日</font>
      </span><b><span style="mso-spacerun:&#39;yes&#39;;font-family:仿宋;color:rgb(59,63,71);
font-weight:bold;font-size:18.0000pt;mso-font-kerning:0.0000pt;">
          <o:p></o:p>
        </span></b>
    </p>
  </div>
  <!--EndFragment-->
</body>

</html>