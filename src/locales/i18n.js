import { createI18n } from 'vue-i18n'

/**
 * 创建带store的i18n对象
 * @param {string} locale 语言
 * @param {object} initMessages 本地国际化资源
 * @param {import('pinia').Store} store
 * @returns { import('vue-i18n').I18n } i18n
 */
export default function createPiniaI18n(locale, initMessages, store) {
  // 项目只使用中文，固定为cn
  const i18n = createI18n({
    locale: 'cn',
    fallbackLocale: 'cn',
    messages: initMessages,
  })

  store &&
    store.$subscribe(
      (mutation, state) => {
        const preMessages = i18n.global.getLocaleMessage(i18n.global.locale)

        i18n.global.setLocaleMessage(i18n.global.locale, { ...preMessages, ...state.messages })
      },
      { detached: true, immediate: true }
    )

  return i18n
}
