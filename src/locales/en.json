{"Banktransfer_account": "Banktransfer account number", "TrueMoney_account": "TrueMoney account number", "account_country": "Country", "bank_account": "Bank card number", "bank_address": "Current address", "bank_branch": "Bank branch name", "bank_location": "Bank location", "bank_name": "Bank name", "bank_svift_code": "SWIFT code or routing number", "bank_user_name": "Recipient name", "board_continue_watch": "Continue watching", "bonus_checkin_reward": "Check-in Successful", "bonus_pointcenter": "Points Center", "btn_yes_download_continue": "Continue reading", "contact_us": "Contact us", "contact_us_QRcode": "2. <PERSON><PERSON> the QR code to access custom service on WeChat", "contact_us_email": "1. Email address", "contact_us_tittle": "You can reach us through the following channels:", "koc_20_char_pw_en_num_char_combo": "A 20-character password composed of English letters, numbers or symbols", "koc_Invita_code": "Invite Code", "koc_abnormal_remit": "Payment error", "koc_access_system": "Log in", "koc_account_change": "Change the account", "koc_account_change_tips": "The binning is successful, please log in again", "koc_account_login": "<PERSON><PERSON>", "koc_account_name": "Account Name", "koc_account_period": "Account Period", "koc_account_switch": "Switch Account", "koc_accumulated_income": "Total income", "koc_activity_all_award": "Total budget", "koc_activity_config": "Incentive allocation", "koc_activity_end_award": "Deadline for claiming rewards", "koc_activity_end_sub": "Event sign-up deadline", "koc_activity_every_award": "Reward for each new user registration", "koc_activity_every_max_award": "Max reward per marketer", "koc_activity_every_max_num": "The highest number of marketers participating in the event", "koc_actual_price": "Actual payment", "koc_add_menu": "Menu Addition", "koc_adddesk1": "Add it to your home screen for quicker access", "koc_adddesk2": "Add", "koc_adddesk3": "Add KOC Marketing Platform to your home screen", "koc_adddesk4": "Click the button below", "koc_adddesk5": "Add to the home screen", "koc_adddesk6": "Success", "koc_admin_distribution_ratio": "Distribution Ratio（Books/Mini-series）", "koc_admin_distribution_ratio_type1": "Pre-agreed flat distribution ratio", "koc_admin_distribution_ratio_type2": "Custom distribution ratio", "koc_allow_login": "Login Allowed or Not", "koc_app_change": "App Modification", "koc_app_edit": "Edit app items", "koc_app_icon": "Icon", "koc_app_list": "App List", "koc_app_name": "Application Name", "koc_app_name_edit": "<PERSON><PERSON>", "koc_app_new": "App Addition", "koc_app_service_tips": "Take a screenshot and save it. Use WeChat to scan the QR code to contact customer service.", "koc_approved": "Approved", "koc_appstore_url": "AppStore link", "koc_asset_points": "Points", "koc_async_seccess": "Synchronization completed", "koc_authorith_apply": "Authorize", "koc_authorithed": "Authorized", "koc_authorithed_tip": "To ensure the security of your payments, we require your authorization to facilitate payment through the Payoneer platform. Once you approve, you will not be able to grant authorization to other accounts. If you wish to change your account authorization, please reach out to Payoneer's support team to assist you in unlinking your account and then authorizing a new account.", "koc_authorithed_toast": "It is Pay<PERSON>er's policy to require your authorization before we can initiate a payment to you.", "koc_authorization_cooperate": "Cooperation Information", "koc_authorization_language": "Language", "koc_auto_invite_code": "Exclusive invite code: {0}", "koc_auto_invite_label": "Click the link to register as a marketer: {0}", "koc_bank_account": "Bank Account", "koc_bank_address": "Bank address", "koc_banner_invitation00": "Invite Users", "koc_banner_invitation01": "Gain Revenue", "koc_banner_invitation02": "Inviters will always get 15% of invitees' earnings as commission (paid by us)", "koc_below_menu_operation_failed_retry": "The following menu operation failed to save, do you want to retry?", "koc_bill_amount": "Payment amount", "koc_bind": "Link", "koc_bind__error_tips1": "WeChat has been bound", "koc_bind__error_tips2": "The user is bound", "koc_bind__error_tips3": "Failed to get WeChat information", "koc_bind__error_tips4": "Please log in first and bind WeChat", "koc_bind_error_tips": "Failed to add", "koc_bind_info": "Linking information", "koc_bind_subtilte": "Bind to Changdu Expert account and synchronize promotion data", "koc_binding_email_tips": "Please link your email first", "koc_book_series_title": "Book/Mini-series name", "koc_button_all": "All", "koc_button_batch": "Bulk Import", "koc_button_cancel": "Cancel", "koc_button_cleanup": "<PERSON><PERSON>ing", "koc_button_close": "Show Less", "koc_button_copy": "Copy", "koc_button_copy_success": "<PERSON>pied", "koc_button_date": "Date", "koc_button_delete": "Delete", "koc_button_delete_sure": "Delete the user?", "koc_button_delete_title": "Delete Operation", "koc_button_details": "Details", "koc_button_download": "Download", "koc_button_download_all": "Download all", "koc_button_edit": "Edit", "koc_button_expand": "More", "koc_button_export": "Export", "koc_button_hide": "<PERSON>de", "koc_button_invert": "Reverse", "koc_button_jumpto": "Go to", "koc_button_login": "Permission for Login", "koc_button_login_no": "Sign-in Blocked", "koc_button_login_yes": "Sign-in Permitted", "koc_button_material": "Download", "koc_button_name": "Button Name", "koc_button_network": "Copy cloud storage link", "koc_button_new": "New", "koc_button_novel_name": "Use book title", "koc_button_novel_text": "Use the first{0}chapters", "koc_button_operate": "Functions", "koc_button_query": "Search", "koc_button_reels_introduction": "Use synopsis", "koc_button_reels_name": "Use mini-series title", "koc_button_renew": "Updated at {0}", "koc_button_reset": "Reset", "koc_button_reset_password": "Reset password", "koc_button_reset_sure": "Reset the user's password?", "koc_button_reset_title": "Reset Operation", "koc_button_save": "Save", "koc_button_select": "Please select", "koc_button_sure": "YES", "koc_button_synchronous": "Sync", "koc_change_permission": "Change Rights", "koc_changepwd_error": "New password and confirmation password do not match", "koc_changepwd_success": "Password changed successfully, please log in again", "koc_channel_fee": "Payment", "koc_channel_name": "Channel Name/Account <PERSON><PERSON><PERSON>", "koc_check_email_format": "Please check the format of the email address.", "koc_check_in": "Check-in Streak: {0} Day(s)", "koc_check_in_bottom": "Check in", "koc_check_in_everyday": "Day {0}", "koc_check_in_grand": "Grand Prize", "koc_check_in_new_round": "A new round of check-in has started", "koc_check_in_reset": "Check-in streak was interrupted has been reset", "koc_check_in_today": "Today", "koc_check_phone_number": "Please check the phone number.", "koc_check_qq_format": "Please check the format of the QQ number.", "koc_click_or_drag_file_upload": "Click or drag files here to upload", "koc_close": "Collapse all nodes", "koc_code": "Code", "koc_code_guide": "Tutorial", "koc_code_network": "Password", "koc_company_bill": "Settlement Statement", "koc_confirm_delete_button": "Delete the button？", "koc_confirm_delete_menu": "Delete the menu?", "koc_confirm_password": "Re-enter password", "koc_contact_admin_configure_code": "Please contact the administrator to set up a code.", "koc_contract_list": "Contract List", "koc_contract_tooverseas": "Joint Promotion Agreement By signing this Joint Promotion Agreement (hereinafter referred to as Agreement), you acknowledge that you have read, understood, and agreed to this Agreement, have entered into an agreed relationship with Party A, and agree to accept compliance with this Agreement. If you do not agree with the contents of this Agreement or cannot accurately understand the terms of this Agreement, we recommend that you stop using the service. Before accepting this Agreement, please read carefully and fully understand the contents of the terms of this Agreement, which is concluded by you and Party A and has contractual effect. Party A: CHANGDU (HK) TECHNOLOGY LIMITED Address: 100 N HOWARD ST STE R, SPOKANE, WA, 99201, UNITED STATES Party B: Social Security Number: Address: Email: Party A, a company established under the laws of People's Republic of China, and the owner of the promotional content provided in this Agreement to Party B, hereby enter into and execute this Agreement regarding the cooperation between Party A and Party B for the use of the content provided by Party A and the provision of content promotion services for Party A's designated products. I.Overview Following the signing of this Agreement, Party B will promote the designated products and content using materials provided by Party A as per Party A's requirements. 1.Party A's requirements will be communicated via Party A's KOC Marketing Platform or other third-party advertising platforms or channels. 2.Party A's promotion materials include novels, mini-series, audiobooks, etc., owned by Party A, to be used in accordance with each promotion's specific requirements. II.Term and Termination The validity of this Agreement will extend from the effective signing date until the cessation of promotional services by Party B. This Agreement may be executed through methods such as electronic signature, paper contract or scanned copies, etc. Irrespective of the signing method used, Party B must independently review and comprehend the entire Agreement before signing. Upon signing, Party B enters into the Agreement with Party A and agrees to comply with its terms. III.Rights and Responsibilities of Party A 1. Party A holds legal copyrights to its products and promotion materials or has secured legal authorization from the copyright owners. Party B will not violate any third party's legal rights by promoting the works as per Party A's requirements. 2. Party A possesses the authority to assign promotion tasks to Party B and remunerate promotion service fees based on Party B's fulfillment of promotion tasks and outcomes. IV.Rights and Responsibilities of Party B 1. Party B possesses the legal capacity and competence to enter into this Agreement. 2. Party B agrees to adhere to the promotion requirements, service agreements, policies, standards, stipulations, etc. established and modified by Party A periodically as necessary, based on the actual circumstances. 3. Party B retains the right to select promotion tasks distributed by Party A in alignment with Party A's stipulations and Party B's individual circumstances. 4. Party B is solely permitted to utilize Party A's products and provided promotion materials within the bounds of this Agreement and must refrain from adapting or using the provided promotion materials beyond the specified purposes of this Agreement, encompassing but not limited to copywriting, images, audio, video, among others. 5. Party B is permitted to use the promotion materials issued by Party A directly or modify them for promotional purposes. In the event of utilizing modified materials, Party B assures that all adapted materials generated to fulfill promotion tasks (including all outcomes during the adaptation process) are originated and completed independently by Party B without distortion. Party B further guarantees that all adapted materials created for promotion tasks maintain the original essence, intent, storyline, and character relationships of the provided content. The copyright of both the promotion material and any adapted material remains the exclusive property of Party A. Party B's adapted materials must not infringe upon the legal rights of Party A, its affiliates, or any third party, including but not limited to copyrights and other intellectual property rights, rights of reputation, name rights, image rights, and other associated rights as provided under the laws of PRC. Party B ensures that there are no legal disputes, including intellectual property conflicts, arising from the adapted materials, and that they are compliant with the provisions of the Copyright Law and other pertinent laws and regulations. 6. Party B is bound by a duty of confidentiality regarding the custody of material provided and must ensure that any collaboration with Party A remains undisclosed through any means or channels. Should Party B contravene the stipulations outlined in this Agreement, resulting in losses to Party A, Party B shall be held liable for compensating Party A accordingly (including but not limited to direct revenue loss, foregone revenue, expenses, legal fees, litigation costs, etc.). 7. Party B guarantees that no illicit gains shall be procured through improper practices during the promotion process, including but not limited to fake engagement activities, commercial denigration, or unethical promotional strategies. Party B pledges not to adopt names resembling others' businesses, products, or brands in a misleading manner, thereby confusing users about their association with other entities. 8. Party B's promotion practices must align with legal frameworks, regulatory requirements, guidelines of advertising platforms, and any applicable regulations. Party B is prohibited from publishing content that distorts, defames, or harms Party A, its affiliates, brands, products, contracted authors, or works, potentially influencing negative perceptions. Party B assumes full responsibility for the consequences of their promotional activities and must compensate Party A for any losses incurred due to Party B's actions. 9. Party B acknowledges that any content provided by Party A (such as trademark logos, company names, logos, product information, and works of Party A) does not constitute a transfer of intellectual property rights. Party B must not use this content without authorization or acquire it through unauthorized means via any channel. Party B is prohibited from using the content beyond the authorized scope or allowing third parties to utilize it. The intellectual property rights associated with creations like copywriting, audio, video, layouts, graphics, reports, designs, and information derived from Party A's works during the Agreement's execution belong exclusively to Party A. Party B may not utilize these materials for purposes other than the cooperation defined in this Agreement. Additionally, Party B is restricted from conducting profit-oriented activities or commercial ventures while performing promotional tasks or utilizing work outcomes. This includes actions like placing advertisements in comment sections or monetizing traffic on advertising platforms. 10. Party B is forbidden from transferring, subcontracting,outsourcing or delegating promotion tasks to third parties without obtaining written consent from Party A. V. Payment and Taxation 1. Party B will promote Party A's works according to the terms outlined in this Agreement, and both parties will share the accrued recharging revenue and advertising income generated by users through Party B's promotion activities. 2. Payment method: Monthly payment will occur between the parties. Calculation of Party B's promotion tasks and revenue details will be conducted in USD, with specific data determined based on Party A's designated backend statistics. Settlement regulations will adhere to the rules periodically published by Party A on KOC Marketing Platform. 3. Party B is responsible for paying taxes in compliance with the platform and relevant state regulations. Party A retains the right to withhold and remit Party B's taxes, subsequently disbursing the remaining amount to Party B. 4. Party B must provide accurate collection account information through Party A's designated system. Any discrepancies or changes in the provided account information leading to legal consequences are solely Party B's responsibility. VI. Confidentiality 1. Throughout the Agreement's validity and post its termination or cancellation, Party B must not reveal, disclose, or provide to any external party, in any manner, the confidential information acquired from Party A without written consent from Party A. The exception to this is when compelled by laws, regulations, government bodies, or pertinent authorities (including but not limited to court orders, stock exchanges, 2. The confidential information as outlined includes, but is not limited to the following aspects: (a) Commercial secrets: This encompasses details such as cooperation methods, work content, the Agreement itself, contents or portions of its appendices, information arising during the agreement's execution (including email exchanges, chat logs, etc.). (b) Financial secrets: Involves elements like payment criteria, methods, periods, revenues generated by the agreement, and their distribution. (c) Technical secrets: Encompasses technical standards, access methods and solutions, technical updates and modifications, interface standards, and access agreements. (d) Business methods: Encompasses business types, status, user data, promotional and advertising methodologies, and their implementations. 3. Party B is obligated to maintain confidentiality regarding the pertinent information until such time as the confidential information is legally allowed to be disclosed. 4. Following the termination or dissolution of this Agreement, Party B must either return or destroy the confidential information in its possession as instructed by Party A. VII. Force Majeure 1. Force majeure encompasses unforeseeable, unavoidable, and insurmountable objective events. This includes, but is not limited to, natural disasters, societal occurrences, alterations in national policies, and changes in laws and regulations. 2. In the event that force majeure circumstances prevent the partial or complete fulfillment of this Agreement, both parties are absolved of liability for breaching the contract. Resolution efforts through negotiation must be undertaken; if negotiations fail to resolve the issue, and the agreement cannot be continued, the parties agree to terminate the agreement early, settling all incurred costs accordingly. VIII. Default 1. In the event of a breach by either party, if the defaulting party does not rectify the breach within 3 days of receiving notice from the non-defaulting party requesting correction, the non-defaulting party may terminate the Agreement. Subsequently, the defaulting party is required to compensate the non-defaulting party for all damages, reasonable expenses, and breach compensation. Other relevant provisions of the Agreement remain applicable. 2. Should Party B violate the terms outlined in Article IV of the Agreement (IV.Rights and Responsibilities of Party B), Party A holds the right to promptly terminate the Agreement. Additionally, Party B must refund all fees paid (unresolved payments exempt), pay liquidated damages amounting to three times the fees paid to Party A, and provide compensation for any losses incurred by Party A as a result of Party B's actions. IX. Service of Documents and Dispute Resolution 1. Party B consents that the address stipulated at the commencement of the contract will serve as the address for receiving notices from Party A as per this agreement, as well as for the delivery of legal documents pertaining to arbitration. 2. All contractual disputes between Party A and Party B should be initially addressed through negotiation. If unresolved, both parties mutually agree to pursue arbitration through FUZHOU ARBITRATION COMMISSION, China. X. Notice This contract will be executed in duplicate, with each party signing one copy, thereby becoming effective upon signatures or seals from both parties. Party A (Seal)： Party B (Signature/Seal):", "koc_controller_name": "Controller name", "koc_cooperation_aisle": "Service Fee Payment", "koc_cooperation_aisle1": "Pre-agreed flat payment", "koc_cooperation_aisle2": "User-defined payment", "koc_cooperation_aisle3": "Pre-agreed transaction-based payment", "koc_cooperation_aisle4": "No service fee", "koc_cooperation_confirm": "The following configuration has been modified, {0}, please confirm whether to modify.", "koc_cooperation_cost": "Cost Coefficient", "koc_cooperation_cost1": "Pre-agreed flat cost coefficient", "koc_cooperation_cost2": "User-defined cost coefficient", "koc_cooperation_cost3": "No cost coefficient", "koc_cooperation_cps": "Revenue sharing", "koc_cooperation_date": "Cooperation Duration", "koc_cooperation_date_finish": "End date", "koc_cooperation_date_month": "{0} month", "koc_cooperation_date_start": "Start date", "koc_cooperation_date_unlimited": "N/A", "koc_cooperation_date_year": "{0} year", "koc_cooperation_input": "The percentage of profit the organization can acquire, a positive integer between 0 and 100", "koc_cooperation_new": "Add new cooperation information", "koc_cooperation_proportion": "Profit Sharing Ratio", "koc_cooperation_type": "Cooperation Mode", "koc_copy": "Copy", "koc_copy_act1": "<PERSON><PERSON> the first chapter", "koc_copy_copywriting": "Copy", "koc_copy_link": "Copy link", "koc_country_choose": "Region", "koc_country_inner": "domestic", "koc_country_outer": "foreign", "koc_country_tips": "Note: The country or region you select will determine your transaction currency and withdrawal method. Please choose carefully, as changes cannot be made afterward.", "koc_cps_close_all": "Close permanently", "koc_cps_close_title": "Close", "koc_cps_close_today": "Close for today", "koc_cps_end": "Registration for the current event is full. Please look forward to the next round!", "koc_cps_tip": "MoboReels new user bonus is currently available. CPA settlement offers a reward of $ {0} for each new registered user, up to a maximum limit of $ {1}. You have earned $ {2}so far. The event concludes on {3}. The CPS profit-sharing ratio remains unchanged.", "koc_create_promotion_link": "Create promotion links", "koc_created_at": "Created at", "koc_daily_statistical_report": "Daily statistics report-", "koc_deeplink": "Deep Link", "koc_deeplink_get": "Create TikTok deep links", "koc_deeplink_serial": "Deep Link Acquisition Status", "koc_delete_success": "Deleted", "koc_detail_materials": "Edited Content", "koc_detail_original": "Original Content", "koc_detail_skits_link": "promotion link", "koc_disabled": "Not-enabled", "koc_disabled_warning": "Deactivated", "koc_divide_ratio_book": "Profit sharing ratio (books)", "koc_divide_ratio_option1": "Pre-agreed flat profit sharing ratio", "koc_divide_ratio_option2": "Custom profit sharing ratio", "koc_divide_ratio_video": "Profit sharing ratio (mini-series)", "koc_download_all_episodes": "Download all episodes", "koc_download_template": "Download the template", "koc_drama_details": "Details", "koc_drama_details_episode": "EP{0}", "koc_drama_double": "General", "koc_drama_grade": "Rating", "koc_drama_grade_inot": "{0}", "koc_drama_local": "Local", "koc_drama_toll": "Paid content begins at EP{0}", "koc_drama_type": "Classification", "koc_email": "Email", "koc_email_address": "Email address", "koc_enabled": "Enabled", "koc_english": "English", "koc_enter_body_content": "Enter the text content", "koc_enter_file_name": "Please enter the filename", "koc_enter_login_password": "Enter password", "koc_enter_number": "Please enter numbers", "koc_enter_title_copy": "Enter the title", "koc_example_edit": "For example: Edit", "koc_exception": "Error description: {0}", "koc_exchange_rate": "Exchange rates", "koc_existing_app": "Current Applications", "koc_existing_menu": "Existing <PERSON><PERSON>", "koc_expand": "Expand all nodes", "koc_expert_affiliation": "Organization", "koc_expert_id": "Marketer ID", "koc_expert_id_input": "Enter marketer ID", "koc_expert_input": "e.g. <PERSON>", "koc_expert_list": "List of marketers", "koc_expert_name": "Marketer's Name", "koc_expert_name_input": "Enter name or acronym", "koc_expert_renew": "Renewing Time", "koc_export_details": "Export breakdown", "koc_extend": "Promotion Method", "koc_fb_login": "Login with Facebook", "koc_file_upload_empty": "Empty files are not supported", "koc_file_upload_limit": "Please upload files in JPG, JPEG, PNG, or PDF format (max size: 2MB)", "koc_file_upload_size_limit": "The maximum file size for uploads is 2MB", "koc_file_upload_type": "JPG, JPEG, PNG, and PDF only", "koc_filipino": "Filipino", "koc_fill_according_to_template_avoid_upload_fail": "To prevent upload failure, please fill in according to the template.", "koc_filter": "Filter", "koc_french": "French", "koc_german": "German", "koc_get_link_fail": "Failed to obtain download link", "koc_get_promotion_link": "Check promotion link", "koc_gift_voucher": "Free Coins", "koc_go_home": "Get home", "koc_google_login": "Login with Google", "koc_googleplay_url": "Google Play link", "koc_guide_tips1": "Click the upper right {0} button", "koc_guide_tips2": "select", "koc_guide_tips3": "Open in browser", "koc_has_menu": "Owned Menu", "koc_hide_operation_continue": "Continue to hide?", "koc_hide_success": "Success", "koc_import_successful": "Import successful", "koc_in_review": "Under review", "koc_income_blank_bottom": "Earn distribution revenue", "koc_income_statistics": "Revenue Statistics", "koc_incorrect_bank_card_number_format": "The bank card number format is incorrect.", "koc_incorrect_id_number_format": "The ID card number format is incorrect.", "koc_incorrect_name_format": "The name format is incorrect.", "koc_indonesian": "Indonesian", "koc_influencer_management": "Manage Marketers", "koc_influencer_statistical_report": "Marketer statistics report-", "koc_input_example": "For example", "koc_institution_statistical_report": "Organization statistics report-", "koc_invitation_botton": "Add a distributor", "koc_invitation_code": "Invite Code: {0}", "koc_invitation_code_failed": "Failed to add", "koc_invitation_code_forbidden": "The marketer is one of your distribution members. You can't add them.", "koc_invitation_code_successed": "Success", "koc_invitation_code_wrong": "Invalid invite code", "koc_invitation_ratio": "Distribution Ratio: {0}", "koc_invitation_share_botton": "Copy and Share", "koc_invitation_status": "Distribution Status: {0}", "koc_invitation_status0": "Not added", "koc_invitation_status1": "Added", "koc_invitation_step": "Only 3 simple steps to get revenue", "koc_invitation_step01": "Share", "koc_invitation_step02": "Invite users to sign up and promote", "koc_invitation_step03": "Acquired distribution revenue", "koc_invitation_title": "Invite New Users", "koc_invitation_total_income": "Total distribution revenue", "koc_invitation_total_members": "Invited", "koc_invitation_total_person": "{0} invitee(s)", "koc_invite_bind": "Link invite code", "koc_invite_canlimit": "Remaining chance(s):", "koc_invite_check_error": "The invite code is linked. Please verify.", "koc_invite_placeholder": "Click to enter the invite code", "koc_invite_tips": "Before you start your promotion, please register the invite code. The invite code consists of 8 characters and can be obtained from your inviter or customer service.", "koc_invoice_upload": "Upload invoice", "koc_invoice_upload_status": "Invoice upload status", "koc_ipwhite": "Whitelist", "koc_is_recommend": "Recommended Or Not", "koc_japanese": "Japanese", "koc_korean": "Korean", "koc_label_code": "Promotional code:", "koc_label_copywriting": "Promotional text:", "koc_label_email": "Email", "koc_label_name": "Name", "koc_label_openwhite": "TikTok FAB", "koc_language_all": "All", "koc_language_text": "Language", "koc_last_month": "A recent month", "koc_last_three_months": "Three recent months", "koc_last_week": "A recent week", "koc_length_cannot_exceed_characters": "The length cannot exceed {0}characters.", "koc_limit": "Restricted", "koc_limit_book_or_album": "Income-restricted designated books/series", "koc_limited_free_card": "Free Access", "koc_link_generate": "Link Generating..", "koc_link_guide": "Tutorial", "koc_list_API": "API", "koc_list_basics": "Basic Information", "koc_list_cooperation": "Cooperation Information", "koc_list_create": "Created at {0}", "koc_list_default": "<PERSON><PERSON><PERSON>", "koc_list_end": "Completed ({0}/{1})", "koc_list_partner_name": "Enter organization name", "koc_list_password": "Code", "koc_list_remark": "Note", "koc_list_remark_input": "Note", "koc_list_serialization": "Ongoing ({0}/{1})", "koc_list_show": "{0} result(s)", "koc_list_total": "Total {0} items", "koc_list_updated": "Updated at {0}", "koc_log": "Log", "koc_login": "<PERSON><PERSON>", "koc_login_account": "Enter login ID", "koc_login_email": "Email Address", "koc_login_email_input": "Enter email address", "koc_login_invitation_code": "Invite code (optional)", "koc_login_invitation_code_empty": "Invitation code cannot be empty", "koc_login_link": "Login URL", "koc_login_manage": "Permitted to Login", "koc_login_manage_no": "No", "koc_login_manage_yes": "Yes", "koc_login_network_error": "Sorry, the current network environment does not support this login method", "koc_login_password": "Password", "koc_login_password_again": "Please confirm the password", "koc_login_password_confirm": "Confirm Password", "koc_login_password_inot": "Default password: 123456", "koc_login_password_input": "6-20 characters, using English letters, numbers, or special characters", "koc_login_password_manage": "Password Options", "koc_logout_successful": "You've successfully logged out!", "koc_lost_help_me_home": "I'm lost. Please help me get home.", "koc_lp_app_name": "App", "koc_lp_button_book": "Continue reading", "koc_lp_button_reels": "Continue watching", "koc_lp_chapter_tooltip": "The symbol ### should be placed before chapter titles", "koc_lp_clear": "<PERSON>ache", "koc_lp_content": "Content", "koc_lp_copy": "Copy", "koc_lp_create": "Create promotion links", "koc_lp_get": "Obtain promotion links", "koc_lp_input_bookname": "Enter the book name", "koc_lp_input_video": "Enter the mini-series title", "koc_lp_manage": "Promotion Links Management", "koc_lp_modify": "Alter the promotion link", "koc_lp_name": "Filename", "koc_lp_not_get_book_title": "The book name could not be retrieved", "koc_lp_not_get_short_show_title": "The mini-series title could not be retrieved", "koc_lp_operation_time": "Operation Time", "koc_lp_operator": "Operator", "koc_lp_product_info": "Work", "koc_lp_production_time": "Addition time", "koc_lp_reels_name": "Mini-series Name", "koc_lp_select_book": "Select a book", "koc_lp_select_language": "Please select language.", "koc_lp_select_video": "Select a mini-series", "koc_lp_slecet_media": "Select the channel", "koc_lp_template_type": "Templates", "koc_lp_template_type1": "Template 1", "koc_lp_template_type2": "Template 2", "koc_lp_tltle": "Headline", "koc_lp_updated_time": "Update time", "koc_lp_upload_failed": "Media {0} failed to upload, please try again later.", "koc_lp_url": "Link", "koc_lp_work_name": "Work Name", "koc_make_code_video": "Code video", "koc_make_code_video1": "Your exclusive code video is being generated", "koc_make_code_video2": "Your exclusive code watermark will automatically appear in the upper left corner of the video", "koc_make_code_video_complate": "The code video is complete<br/>Click to download it", "koc_make_code_video_for": "Generate your exclusive code video", "koc_make_code_video_progress": "Your exclusive code video is being generated<br/>Click to check the progress/download it", "koc_make_old_video": "Original video", "koc_manu_extend": "Promotion", "koc_me_invitation_set": "Distribution Settings", "koc_me_invitation_set01": "Invite Friends for Revenue", "koc_mechanism_id": "Organization ID", "koc_mechanism_id_input": "Enter organization ID", "koc_mechanism_name": "Organization", "koc_mechanism_name_input": "Enter organization's name or acronym", "koc_mechanism_type": "Classification of Organization", "koc_media": "Channel", "koc_menu": "<PERSON><PERSON>", "koc_menu_account": "Account Management", "koc_menu_account_expert": "Marketer", "koc_menu_account_mechanism": "Organization", "koc_menu_change": "Menu Modification", "koc_menu_content": "Category", "koc_menu_content_reels": "Mini-series", "koc_menu_edit": "Edit menu items", "koc_menu_front": "Home", "koc_menu_front_accoun": "ID", "koc_menu_front_my": "Profile", "koc_menu_front_name": "Name", "koc_menu_input": "Enter menu name", "koc_menu_list": "Menu List", "koc_menu_logout": "Sign Out", "koc_menu_lp_novel": "Links-Books", "koc_menu_lp_reels": "Links-Mini-series", "koc_menu_marketing": "Marketing", "koc_menu_new": "Add menus", "koc_menu_reports": "Data", "koc_menu_system": "System", "koc_menu_system_button": "Button Management", "koc_menu_system_limit": "User Rights", "koc_menu_system_menu": "Menu Management", "koc_menu_system_role": "Role Management", "koc_menu_system_rolebutton": "<PERSON>", "koc_menu_system_user": "User Administration", "koc_message_ipwhite": "The whitelist format does not meet the requirements. Please verify.", "koc_minimum_chinese": "Please enter at least 2 Chinese characters", "koc_mission_add_soscial02": "Go", "koc_mission_ball_tittle": "Earn Points", "koc_mission_bottom_claim": "<PERSON><PERSON><PERSON>", "koc_mission_bottom_done": "Done", "koc_mission_bottom_go": "Go", "koc_mission_invitation02": "Go", "koc_mission_invite_people": "{0} invitee(s)", "koc_mission_points": "{0} Points", "koc_mission_promote_share_1": "👉Tap the link in my profile🔥to watch the full series! #moboreels #drama", "koc_mission_promote_share_2": "🔥To view the full episode, please click👉{0}", "koc_mission_promote_share_3": "👉Click the link on my profile🔥to read the full story! #moboreader", "koc_mission_promote_share_4": "🔥To read the full story, please click👉{0}", "koc_mission_promote_share_fb": "Share on Facebook", "koc_mission_promote_share_tt": "Share on TikTok", "koc_mission_promote_step_1": "Add to MoboTree", "koc_mission_promote_step_2": "Download content", "koc_mission_promote_step_3": "Share on social media", "koc_mission_promote_tutorial": "Complete promotion tutorial", "koc_mission_share_fail": "You need to complete the sharing to receive reward", "koc_mobotree_add": "Add to MoboTree", "koc_mobotree_add_success": "Added to MoboTree", "koc_mobotree_guide": "MoboTree Tutorial", "koc_mobotree_my": "My MoboTree", "koc_mobotree_title": "Add MoboTree on your profile", "koc_mount_tip": "Copy the universal links corresponding to each region in the list below.", "koc_mount_tip1": "Copy the event link and open it with a browser (requires VPN) , and click <span>[OPEN TIKTOK]</span>", "koc_mount_tip2": "Click <span>[Join]</span> and then </span>[View collaborations]</span> to join the event", "koc_mount_tip3": "To upload a video, click <span>[Upload Video]</span>. Once the link is attached to the video, you can download the app by clicking the button in the lower left corner of the video player.", "koc_mount_tip4": "Note: The universal link can be shared freely without restrictions, with no need for whitelisting and unlimited number of shares. Highly recommended for use.", "koc_msystem_button": "<PERSON><PERSON>", "koc_msystem_button_input": "Enter button name", "koc_msystem_button_list": "Button List", "koc_msystem_controller": "Controller", "koc_msystem_create": "Creation Time", "koc_msystem_create1": "Start time", "koc_msystem_create2": "End time", "koc_msystem_login": "Logged in at{0}", "koc_msystem_login_account": "ID", "koc_msystem_menu": "Character Menu", "koc_msystem_menu_edit": "Edit menu", "koc_msystem_role": "Character", "koc_msystem_role_edit": "Edit character", "koc_msystem_role_have": "Existing Characters", "koc_msystem_role_linput": "Enter character name", "koc_msystem_role_list": "Character List", "koc_msystem_type": "Type", "koc_msystem_type_super": "Super Administrator", "koc_msystem_type_user": "Ordinary User", "koc_msystem_user": "User", "koc_msystem_user_input": "Enter username", "koc_msystem_user_list": "User List", "koc_msystem_user_name": "Username", "koc_msystem_user_phone": "Phone number", "koc_msystem_user_role": "User Character", "koc_msystem_user_type": "User type", "koc_multi_language": "Multiple Languages", "koc_my_info": "Personal Information", "koc_my_know": "Confirm", "koc_name": "Marketing Platform", "koc_network_error": "Network error. Please check your network settings.", "koc_new_password": "New Password", "koc_no_content": "Failed to access introduction", "koc_no_description": "No synopsis at the moment.", "koc_note": "Attention", "koc_notify_select_media": "Please select the social media platform to which the account belongs", "koc_novel_book": "Book Title", "koc_novel_book_input": "e.g. Deep Love", "koc_novel_channels": "Channel", "koc_novel_channels_female": "Ladies'", "koc_novel_channels_input": "All", "koc_novel_completion": "Status", "koc_novel_completion_end": "Completed", "koc_novel_genres": "Genre", "koc_novel_list": "List of books", "koc_novel_list_books": "Title", "koc_novel_type": "Classification", "koc_novice_guide": "Be<PERSON><PERSON>'s Tutorial", "koc_numeric_value_range_zero_to_infinity_integers": "The entered values are limited to integers in the range [0, ∞).", "koc_old_password": "Old Password", "koc_open_white": "TikTok promotion whitelist", "koc_openwhite_apply": "Apply for Disclosure", "koc_openwhite_log": "Log", "koc_organization_account": "Account name", "koc_organization_info": "Company Information", "koc_organization_name": "Company name", "koc_organization_place": "Company address", "koc_organization_settlement": "<PERSON><PERSON><PERSON><PERSON>", "koc_original_download": "Download original videos", "koc_original_text": "Original Text", "koc_page": "Page", "koc_page_items": "{0} rows/page", "koc_password": "Password", "koc_password_get": "Generate code", "koc_password_input": "e.g. A021", "koc_password_mismatch_reenter": "Passwords don't match. Please try again.", "koc_password_readercopy1": "💥 To read the full story 👉 Download {0} and search for [{1}] and dive into the novel ✅#{1} #{0}", "koc_password_readercopy2": "✨Download {0} 👉 Copy the code [{1}] and search。👀Dive into a world of endless stories waiting for you.🌈#{1} #{0}", "koc_password_readercopy3": "🌟 Continue the story here 👉🏻 📲 Download the \"{0}\" app, 🔍 search for \"{1}\", and watch the full series ✨!#{1} #{0}", "koc_password_reelscopy1": "💥Watch the full episode. 👉Download the {0} app and search for [{1}] to watch online✅#{1} #{0}", "koc_password_reelscopy2": "✨Download the {0} app. 👉copy the code [{1}] and search it. 👀A huge number of short dramas waiting for you to explore. 🌈#{1} #{0}", "koc_password_reelscopy3": "🌟 Continue the story here 👉🏻 📲 Download the \"{0}\" app, 🔍 search for \"{1}\", and watch the full series ✨!#{1} #{0}", "koc_password_serial": "Code Acquisition Status", "koc_pen_name_requirements_chinese_english_numbers": "Pen names must consist of Chinese characters, English letters or numbers.", "koc_period_detail": "Breakdown of account period", "koc_piont": "Points", "koc_piont_acquired_date": "Acquired at {0}", "koc_piont_acquired_type01": "Acquired by mission", "koc_pionts_center": "Points Center", "koc_pionts_center_coming_soon": "Coming soon", "koc_placeholder_address": "Enter contact address", "koc_placeholder_bank_area": "Enter bank location", "koc_placeholder_bank_card": "Enter bank card number", "koc_placeholder_bank_name": "Enter bank name", "koc_placeholder_bank_of_deposit": "Enter branch bank name", "koc_placeholder_bank_swift": "Enter SWIFT code or or routing number", "koc_placeholder_belonging_country": "Enter the recipient's country", "koc_placeholder_between_0_100": "Positive integer between 0-100", "koc_placeholder_cooperation": "Please complete the configuration of cooperation information", "koc_placeholder_current_address": "Enter the recipient's current address", "koc_placeholder_idcard": "Please enter your ID number.", "koc_placeholder_payee_name": "Enter the recipient's full name", "koc_placeholder_paylal": "Enter PayPal account number", "koc_placeholder_realname": "Please enter your real name.", "koc_placeholder_token": "Click the button on the right to automatically generate a token", "koc_plan1": "Method 1", "koc_plan2": "Method 2", "koc_please_enter": "Please enter", "koc_points_acquired": "Acquired", "koc_points_all": "All", "koc_points_expired": "Expired", "koc_portuguese": "Portuguese", "koc_preview_detail": "Details", "koc_priview": "Preview", "koc_product": "App", "koc_profile_account_type": "Receipt account type", "koc_profile_bank_account": "Bank Account Information", "koc_profile_certificat_fail": "Verification failed", "koc_profile_certificated": "Verification successful", "koc_profile_pic_size": "Image size must be smaller than 1MB!", "koc_profile_pic_type": "Only JPG/PNG files are supported!", "koc_profit_account_info": "Account", "koc_promotion_link": "Promotion Link", "koc_promotion_rule": "Three promotion methods are available.\n1. Code: Generate a unique code for specific content, assisting users in locating the content by entering the code after downloading the app.\n2. Promotion Link: Create a promotion link (H5 landing page) to direct users to access the designated content after opening the H5 landing page and downloading the app. \n3. Deep Link: Employed in TikTok and similar platforms that support ad links, directing users to click on the ad link for automatic access to the corresponding content. Deep links are accessible on both ends. App download links can be found on the homepage.", "koc_quick_other": "Select login method", "koc_random": "Generate randomly", "koc_re_verify": "Reverification", "koc_read_marketing_platform": "KOC Marketing Platform", "koc_read_unified_login_portal": "Unified Login Portal", "koc_rebind": "Change", "koc_recommend": "RECOM", "koc_recommend_import": "Import", "koc_recommend_level": "Rating", "koc_recommend_reason": "Reason for recommendation", "koc_recommendtime": "Recommended time", "koc_reels_drama": "Mini-series", "koc_reels_drama_list": "List of mini-series", "koc_refresh_promotion_link_success": "The promotion link has been successfully updated", "koc_refund_amount": "Refund amount", "koc_regist_report": "Statistics of newly registered experts", "koc_regist_report_total": "Number of new talents", "koc_rejected": "Rejected", "koc_release": "Premiere", "koc_remember_password": "Remember password", "koc_remit_to": "Transferred at {0}", "koc_remitted": "Completed", "koc_remitting": "Pending", "koc_remove": "Deselect", "koc_reports_all": "Total", "koc_reports_cpa": "Activated Users Count", "koc_reports_date": "Time Range", "koc_reports_dimensions": "Dimension", "koc_reports_dimensions_business": "Book|Mini-series", "koc_reports_dimensions_content": "Book/Mini-series", "koc_reports_dimensions_day": "Daily Statistics", "koc_reports_dimensions_month": "Monthly Statistics", "koc_reports_dimensions_password": "Code", "koc_reports_illustrate": "1. The black section (activated user count, total income, revenue share income) details data for accounts of organizations or marketers in both normal and banned status. \n2. The green section (activated user count, total income, revenue share income) displays data only for accounts of organizations or marketers in normal status.", "koc_reports_income": "Actual Income", "koc_request_exception": "Bad Request", "koc_resource": "Resource Status", "koc_role_button_management": "Character Button Management", "koc_role_menu": "Role Menu", "koc_rules": "Rules", "koc_russian": "Russian", "koc_save_success": "Saved Successfully", "koc_search_input": "Enter search content", "koc_search_input_no": "No data", "koc_select": "Select", "koc_select_agency_affiliation": "Select organization affiliation", "koc_select_all": "Select All", "koc_select_authorization_scope": "Select authorization scope", "koc_select_authorized_product": "Select the authorized product", "koc_select_work": "Please select a work", "koc_service_fb_bottom": "Contact Us", "koc_service_fb_nickname": "<PERSON>", "koc_service_fb_tip": "Click the button below to {0} contact the customer service", "koc_service_header": "Contact Us", "koc_service_qrcode_save": "<PERSON> press on the image to add customer service", "koc_service_qrcode_tips": "Scan the QR code to add customer service on WeCom", "koc_service_request_timeout": "Request timed out ${0} second(s) ago", "koc_service_subtitl": "Exclusive customer service to answer questions", "koc_service_tiktok_header": "Dedicated Customer Service", "koc_service_tiktok_subtitle": "Find Installation Instructions for TK", "koc_service_tiktok_title": "Add Customer Service and Send", "koc_service_title": "Changdu customer service", "koc_set_password_8_20_characters_combo_of_en_num_or_chars_2_kinds": "Please set a password of 8-20 characters, containing a combination of at least two of the following, English letters, numbers and special characters.", "koc_settilement_window": "Settlement period", "koc_settle_accounts": "Closing amount", "koc_settlement": "Settlement Management", "koc_settlement_download": "Download statement", "koc_settlement_rate": "Settlement exchange rate", "koc_settlement_status": "Settlement status", "koc_settlement_threshold": "Settlement threshold", "koc_settlement_threshold_custom": "Please enter a custom settlement threshold", "koc_share_titile1": "Share Mpr", "koc_share_titile26": "Share my Mpr to...", "koc_share_titile27": "Share to...", "koc_share_titile29": "Share on {0}", "koc_share_titile30": "Share via {0}", "koc_sign_company": "Contracting party", "koc_signin_contract_id": "Contract Code", "koc_signin_contract_name": "Contract Title", "koc_signin_contract_signed": "Signed", "koc_signin_contract_todo": "Sign Contract", "koc_signin_contract_view": "View", "koc_signin_finish_time": "Signature Date", "koc_signin_manage": "Contract Management", "koc_signin_status": "Signing Status", "koc_signin_tip": "Sign up to become a Mpr marketer, share captivating content, and earn commissions with ease", "koc_signin_tripartite2": "Authorization cancelled", "koc_signin_unfinished": "Please sign the Joint Promotion Agreement first.", "koc_simplified": "Simplified Chinese", "koc_slide_watch_more": "Swipe to view more", "koc_spanish": "Spanish", "koc_star_activity": "Marketer CPA incentive", "koc_start_add": "The number of new registered users referred by the marketer", "koc_start_invite_code": "Team leader invitation code", "koc_start_regist": "Cumulative number of new registered users referred by the marketer", "koc_start_time_zone": "Marketer's time zone", "koc_status": "Status", "koc_stay_withdraw": "Unpaid", "koc_stop_attributing_again": "Re-attribution not counted", "koc_submit": "Submit", "koc_subtitles_video": "Subtitled Video", "koc_subtitles_video_making": "Processing", "koc_swiftcode_rule": "A SWIFT code is made up of 8 or 11 characters and can be purely alphabetical or a combination of letters and numbers.", "koc_system_management": "System Management", "koc_tag_hot": "Hot", "koc_task_done_tips": "New tasks are coming.", "koc_taxid_rule": "The tax number consists of 18 letters and numbers", "koc_teach_code": "Tutorial", "koc_teach_link": "Mounting tutorial", "koc_template_one": "Template 1", "koc_template_two": "Template 2", "koc_thai": "Thai", "koc_tip": "Note", "koc_title": "Title", "koc_tk_account": "TK Account", "koc_tk_account_placeholder": "Please enter your TK account", "koc_today": "Today", "koc_tooltip_ipwhite": "The IP whitelist should be separated by commas", "koc_total_detail": "{0} in total", "koc_total_income": "Total Income (USD)", "koc_total_income_cny": "Total Income (CNY)", "koc_total_invitation": "Distribution income", "koc_total_invitation_income": "Total distribution income", "koc_total_invitation_members": "Distribution members ({0})", "koc_total_promotion": "Revenue share income", "koc_total_promotion_income": "Total revenue share income", "koc_total_rmb": "Total revenue (CNY)", "koc_traditional": "Traditional Chinese", "koc_type_of_settlement": "Settlement currency type", "koc_typein_subtitles_loading": "A subtitled video is being generated. Please wait.", "koc_typein_subtitles_success": "The subtitled video is ready. Click to download.", "koc_unlimited": "Unrestricted", "koc_updated_at": "Updated at", "koc_url": "Address", "koc_url_network": "Cloud storage link", "koc_user_and_createtime": "Created by {0}at {1}", "koc_user_and_updatetime": "Updated by {0} at {1}", "koc_user_service": "Customer Service", "koc_userinfo_step": "Complete the recipient account information", "koc_userinfo_tips": "1.The country or region you select will determine your transaction currency and withdrawal method. Please choose carefully, as changes cannot be made afterward.<br>2.Within the Chinese Mainland: Transactions are in CNY (RMB); you can withdraw to your domestic Chinese bank account.<br>3. Other Countries and Regions: Transactions are in USD; withdrawals are supported via Payoneer, PayPal, bank cards, and PayerMax.", "koc_username_requirements_english_num_combination": "The username must be a combination of English letters and numbers.", "koc_username_requirements_for_english_num_combination": "English pen names must consist of English letters or numbers.", "koc_valid_address": "Please select the location of the contact address.", "koc_valid_address_detail": "Enter a detailed contact address", "koc_valid_bankinfo": "Please enter at least one piece of your bank account information.", "koc_video_inpicture": "Miniplayer", "koc_video_preview": "Preview", "koc_video_speed": "Playback speed", "koc_welcome": "Welcome to", "koc_welcome_to_use_system_logging_in": "Welcome! Logging in...", "koc_welfare_sign": "Sign-in module", "koc_welfare_task": "Common tasks", "koc_welfare_task_center": "Task Center", "koc_welfare_task_store": "Tasks", "koc_white_open_link": "Temporary review link", "koc_white_open_placeholder": "If the application fails, the reason for the failure will be in the link.", "koc_white_zone": "Region", "koc_withdraw_tips1": "In order to ensure the continuity of the account period, the starting withdrawal account period is not allowed to be modified.", "koc_withdraw_tips2": "The withdrawal threshold amount is {0}. Please make sure that the total amount of the selected account period is greater than the threshold amount, or you cannot withdraw.", "koc_withdraw_tips3": "Receiving account information will be synchronized to Marketing-Personal Information-Receiving Account after successful saving.", "koc_withdraw_tips4": "All incomes are converted based on the exchange rate on the day they are generated. Please note that the exchange rate fluctuates over time.", "koc_withdrawn": "Withdrawn", "koc_working_time": "Working hours:", "koc_wx_login": "<PERSON><PERSON><PERSON>", "koc_wx_name": "WeChat ID", "koc_wx_name_input": "Please enter WeChat ID", "koc_wxlogin_notpop": "No more pop-ups", "koc_wxlogin_tips": "Use WeChat to scan to log in", "koc_yesterday": "Yesterday", "koc_zone1": "USA", "koc_zone2": "Japan", "koc_zone3": "Republic of Korea", "koc_zone4": "Thailand", "koc_zone5": "England", "kocs_Institutiona_statistics": "Organizational Data", "kocs_Leader": "Distributor", "kocs_Sketch": "Mini-series", "kocs_active_new_user": "New users", "kocs_active_user": "Active users", "kocs_active_user_type": "Activated user type", "kocs_app_authorization": "Authorized App", "kocs_apptype": "App", "kocs_ar": "Arabic", "kocs_asset_data_not_exist": "No results found", "kocs_businesstype": "Business mode", "kocs_day_statistics": "Daily Data", "kocs_incometype": "Income type", "kocs_institutionid": "Organization Name", "kocs_institutionid_list": "List of organizations", "kocs_it": "Italian", "kocs_netdisk_address_add": "Add cloud storage link", "kocs_netdisk_bottom_copy": "Copy All", "kocs_netdisk_bottom_download": "Download", "kocs_netdisk_download": "Download from cloud storage", "kocs_netdisk_expire_time": "Cloud storage expires on", "kocs_netdisk_pop_tittle": "Download from cloud storage and unlock episodes", "kocs_newrealdevnum": "Number of New Users", "kocs_oper_success": "Success", "kocs_parameters_lack": "Required parameters are incomplete", "kocs_projecttype": "Genre", "kocs_realdevnumrate": "Ratio of New Users", "kocs_scope_authorization": "Authorized Category", "kocs_starid": "Marketer's Name", "kocs_talent_statistics": "Personal Data", "kocs_total": "Total", "kocs_user_old_pwd_error": "Unable to change password. The old password provided is incorrect.", "kocs_vi": "Vietnamese", "kocs_web_article": "Books", "link_id": "Link ID", "login_fogot_code": "Forgot password?", "login_go_to_sign": "Click here to register", "login_no_account": "Haven't got an account?", "login_title": "<PERSON><PERSON>", "mckoc_detail_charge": "Recharge amount", "mckoc_detail_type": "Commission", "mckoc_guid_tip3": "If the browser did not respond to the automatic download, please refresh the page or restart the browser.", "mckoc_hit": "Weekly Top Picks", "mckoc_lang_all": "All languages", "mckoc_media_id": "Channel ID/Account ID", "mckoc_media_link": "Channel Link/Account Link", "mckoc_media_name": "Media", "mckoc_media_nickname": "Channel Name/Account <PERSON><PERSON><PERSON>", "mckoc_media_placeholde_id": "Please enter the channel ID/account ID", "mckoc_media_placeholde_link": "Please enter channel link/account link", "mckoc_media_placeholde_name": "Please select the media", "mckoc_media_placeholde_nickname": "Please provide a channel name/account username", "mckoc_media_title": "Add Media Account", "mckoc_open_white": "Application form", "mckoc_promotion": "Promotion", "mckoc_rank_all": "Overall", "mckoc_rank_book": "Novel", "mckoc_rank_new_book": "Latest", "mckoc_rank_new_video": "Latest", "mckoc_select_right_account": "Please enter the correct TK account", "mckoc_select_zone": "Please select the region where your TK account belongs", "mckoc_statics_cap": "Member", "mckoc_statics_detail": "Revenue Details", "mckoc_statics_get": "Estimated revenue", "mckoc_statics_my": "You", "mckoc_statics_only_cap": "Only members", "mckoc_statics_only_my": "Only you", "mckoc_statics_order": "Number of orders", "mckoc_statics_platform": "Promotion platform", "mckoc_statics_recharge_total": "Total amount of recharge", "mckoc_statics_total": "Estimated total revenue (USD)", "mckoc_statics_total_cn": "Estimated total revenue (CNY)", "mckoc_statics_type": "Type", "mckoc_teach_book_name": "Novel title", "mckoc_teach_code_content1": "Guide users to search for passwords in the app to open the corresponding {0}, {1}   and pay, and get promotion commissions.", "mckoc_teach_code_content2": "When editing a video, put your own code in the upper left corner of the video.", "mckoc_teach_code_content3": "Include a promotional text with the code in the title or comment section.", "mckoc_teach_code_content4": "Users can download the app you promote through the ad in the lower left corner of the video (this method is the best way to promote, and TikTok whitelist permission is required). Users can also choose to go to Appstore to download the app.", "mckoc_teach_code_content4_1": "This method is highly recommended for optimal results, especially when obtaining special permissions on TikTok by opening whitelist.", "mckoc_teach_code_content4_2": "User can also go to Appstore to download the app 【{0}】", "mckoc_teach_code_content5": "Either way to download the app, users need to search for the code  in the app to open the corresponding {0}, {1} and pay for you to get promotion commissions", "mckoc_teach_code_content6": " Click <span>[Copy link]</span> on the {0} details page after choosing the region.", "mckoc_teach_code_content6_1": "After submitting the application, the whitelist will be successfully opened within 24 hours, and the <span>[Temporary Review Link]</span> will be displayed in the whitelist record.n", "mckoc_teach_code_content7": "To use it, copy the <span>[temporary review link]</span> and open it in your browser; you may need to use a VPN, a tool that helps you access content from different regions. Click \"Open\" to launch the TikTok app", "mckoc_teach_code_content7_1": "Click <span>[Join Event]</span>, click <span>[Upload Video]</span>, and after the video is uploaded, users can click the lower left corner of the video to download the app", "mckoc_teach_code_content8": "A temporary link can only be used to upload 10 videos. Once the limit is reached, you'll need to request another link for uploads (feel free to request several links in advance just in case~).", "mckoc_teach_code_fill1": "Watch", "mckoc_teach_code_fill2": "Read", "mckoc_teach_code_title1": "What is the code?", "mckoc_teach_code_title2": "How to use the code?", "mckoc_teach_code_title3": "How to guide viewers to download the app?", "mckoc_teach_code_title4": "How to use the TikTok FAB?", "mckoc_teach_contact_serve": "If you have trouble installing TikTok, please contact customer service>>", "mckoc_teach_link_content1": "Click the link to enter the {0} promotion page. Tap <span>【{1}】</span> to download the app, which will automatically open the corresponding {2}. Users proceed with {3} and payment for you to earn rewards.", "mckoc_teach_link_content10": "You can also add other platform links", "mckoc_teach_link_content11": "After adding the link, you can long press the area as shown in the sample below to drag the module and adjust the order", "mckoc_teach_link_content12": "You can click Preview to see the final effect.", "mckoc_teach_link_content13": "After confirming that the final effect is OK, click the share botton in the upper right corner, click copy to use it", "mckoc_teach_link_content14": "Copy {0} promotion link and click <span>[add]</span> to generate", "mckoc_teach_link_content15": "After completing the above steps, you can rearrange the blocks by dragging them up or down. In the top right corner, the final personalized link is displayed. Click \"Share\" to easily copy the link.", "mckoc_teach_link_content16": "When posting the video, add a catchy caption encouraging viewers to check out your profile for more to find {0}.", "mckoc_teach_link_content17": "Facebook title example:", "mckoc_teach_link_content18": "TikTok title example:", "mckoc_teach_link_content19": "Personal homepage caption example:", "mckoc_teach_link_content2": "Link your personal MoboTree page to your TikTok profile page. This page will include all promotional links for multiple mini-series.", "mckoc_teach_link_content20": "✨\"{0}\" is now showing!✨<br>Related:<br>Paste your own Linktree address<br>🎉 Click the link to download the {1} app and watch the entire short series!", "mckoc_teach_link_content21": "✨ Popular novel \"{0}\"! ✨  <br>Connect:  <br>Paste your own Linktree address  <br>🎉 Click the link to download the {1} app and read more exciting content!", "mckoc_teach_link_content22": "\"Click on my profile link to watch more thrilling shows.\"", "mckoc_teach_link_content23": "\"🎉 Check out my profile for more thrilling stories! ✨\"nnIf you have another excerpt from the novel that you'd like translated, please feel free to share it!", "mckoc_teach_link_content24": "🎉 Click the link to watch the entire series!", "mckoc_teach_link_content25": "🎉 Click the link to read the entire series!", "mckoc_teach_link_content3": "After a user clicks the link, they can see all the promotional links. The user can click on the title to view the corresponding content.", "mckoc_teach_link_content4": "To link a personal website on your TikTok profile, you need to have at least 1,000 followers. If you have fewer than 1,000 followers, you can only include a MoboTree link in your profile description.", "mckoc_teach_link_content5": "On the details page, find the promotion module, tap [{0}] and click <span class='mark'>[MoboTree]</span>", "mckoc_teach_link_content6": "Website", "mckoc_teach_link_content7": "You can also click MoboTree at the bottom to add platform content.", "mckoc_teach_link_content8": "After selecting the content you want to add, click <span>[Add to MoboTree]<span>", "mckoc_teach_link_content9": "Add platform link", "mckoc_teach_link_title1": "What are promotional links?", "mckoc_teach_link_title2": "How to add MoboTree link to your profile?", "mckoc_teach_link_title3": "How to create exclusive links in MoboTree?", "mckoc_teach_link_title4": "How to guide users to personal homepage?", "mckoc_teach_open_book": "View the tutorial on how to add TikTok FAB", "mckoc_teach_open_white": "Immediate White-listing", "mckoc_teach_title": "Marketing promotion platform", "mckoc_teach_video": "Video tutorials", "mckoc_teach_video_code1": "Skit Commands Tutorial", "mckoc_teach_video_code2": "Essential Guide for Beginners to Marketing", "mckoc_teach_video_code3": "Tutorial for Using Promotion Links", "mckoc_teach_video_name": "Title", "mckoc_tk": "TikTok", "mckoc_tutorial": "Tutorial", "mckoc_white_apply_access": "Success", "mckoc_white_apply_failed": "Failed", "mckoc_white_apply_link": "Temporary review link", "mckoc_white_apply_loading": "Applying", "mckoc_white_apply_platform": "Application platform", "mckoc_white_apply_time": "Application Time", "mckoc_white_attention": "Note:", "mckoc_white_attention1": "1. Open your browser and type the username found after the {'@'} symbol in the homepage link.", "mckoc_white_attention2": "2. Be careful not to use {'@'}, and pay attention to uppercase and lowercase letters and symbols;", "mckoc_white_attention3": "3. Each account can post up to 10 videos after opening whitelist for once. Once the limit is reached, a new application is required.", "mckoc_white_attention4": "4. Please refer to the following figure to obtain the TK account", "mckoc_white_attention5": "5、Successful application within 24 hours after submission", "mckoc_white_list": "Whitelist record", "mckoc_white_reader": "Read before applying", "mckoc_wx_limit": "Due to platform restrictions, video content cannot be downloaded directly. Please click {0} to copy the link and paste it into your browser's address bar to download.", "mkoc__account1": "Recipient's Information", "mkoc__account2": "You can withdraw the income from the previous month and earlier between the {0} and {1} of each month.", "mkoc_about": "About Us", "mkoc_banner1": "Overseas Commission", "mkoc_banner2": "Novels & Mini-series—Start at 35%", "mkoc_banner3": "Share content—profiting from users recharging on the APPs", "mkoc_banner4": "Join", "mkoc_banner5": "without <PERSON><PERSON>", "mkoc_banner6": "Free access to novel and mini-series resources", "mkoc_banner7": "to Earn <PERSON>", "mkoc_bind_code": "Bind the invitation code", "mkoc_bind_code_description": "Click to enter the invitation code", "mkoc_bind_code_tip": "The invitation code consists of 8 characters. Please ask your inviter for it.", "mkoc_bind_leader": "Bind the team leader", "mkoc_book_preview1": "For more information, please check on the app", "mkoc_checus_bank_tip": "Only available in Russia", "mkoc_detail1": "Details", "mkoc_detail10": "Upload your downloaded materials on platforms like TikTok or Facebook and include the copied code or link.", "mkoc_detail11": "Wait for users to use the code or link to recharge and earn commission.", "mkoc_detail2": "Play", "mkoc_detail3": "More", "mkoc_detail4": "Blurb", "mkoc_detail5": "How to gain income?", "mkoc_detail6": "Follow the steps below to share the app with other users. You will receive a percentage of their recharge amount as commission.", "mkoc_detail7": "Promotion steps", "mkoc_detail8": "Click on<span> [Download]</span>, and select any material you like", "mkoc_detail9": "Click on <span>[Go now!]</span> and select a promotion method. Copy the corresponding code or link.", "mkoc_extension1": "Promotion", "mkoc_extension2": "Please enter a title", "mkoc_extension3": "Search", "mkoc_extension4": "Mini-series", "mkoc_extension5": "Novel", "mkoc_extension6": "Short Story", "mkoc_extension_choose1": "Select a promotion method", "mkoc_extension_choose2": "Code + module", "mkoc_extension_choose3": "Click the button below to generate a code associated with this novel. Direct users to search for this code within the app to discover the novel.", "mkoc_extension_choose4": "Promotion Link", "mkoc_extension_choose5": "Click the button below to create a promotion link linked to this novel. Direct users to click on the link to download the app.", "mkoc_extension_choose6": "<PERSON>pied", "mkoc_extension_choose7": "Copy", "mkoc_extension_choose8": "Create a promotion link", "mkoc_foreign_bank_card_tip": "Unavailable in Russia. English only.", "mkoc_material1": "{0} Playback Speed", "mkoc_material2": "All the materials", "mkoc_material3": "Switch", "mkoc_material4": "Download", "mkoc_material5": "Start downloading", "mkoc_material6": "Download successful", "mkoc_material7": "List of materials", "mkoc_material8": "Download failed. Please try again.", "mkoc_material9": "Operation failed. Please try again later.", "mkoc_my": "Profile", "mkoc_my1": "Unverified", "mkoc_my10": "Copy", "mkoc_my11": "Are you sure you want to sign out?", "mkoc_my12": "Cancel", "mkoc_my13": "Please select", "mkoc_my14": "Please verify your identity before proceeding.", "mkoc_my15": "The Joint Promotion Agreement has been generated for you and sent to your login email address:{0}", "mkoc_my16": "Please check your email for the Agreement and sign it promptly. Upon successful signing, you can proceed with promoting to generate income.", "mkoc_my17": "Didn't receive the email? Click to resend.", "mkoc_my18": "Go back and modify your personal information.", "mkoc_my19": "I have checked the email and signed the contract.", "mkoc_my2": "Verify", "mkoc_my20": "Signing failed. Please click to try again.", "mkoc_my3": "My Account", "mkoc_my4": "Total Income (USD)", "mkoc_my5": "Withdrawable Income (USD)", "mkoc_my5_cny": "Withdrawable Income (CNY)", "mkoc_my6": "Language", "mkoc_my7": "Success!", "mkoc_my8": "<PERSON><PERSON><PERSON>", "mkoc_my9": "Please describe your issues and email them to:", "mkoc_only_english": "English only", "mkoc_payer_max_true_money_tip": "Thailand only", "mkoc_paypal_tip": "Unavailable in Russia", "mkoc_phone_format_error": "Please check phone number format", "mkoc_privacy_policy": "Privacy Policy", "mkoc_profit1": "Income", "mkoc_profit10": "Last month", "mkoc_profit11": "Details of data", "mkoc_profit12": "Code", "mkoc_profit13": "Access more detailed data from a PC", "mkoc_profit14": "No data", "mkoc_profit15": "Revenue Share Income (USD)", "mkoc_profit15_cny": "Revenue Share Income (CNY)", "mkoc_profit16": "Your balance is below the minimum withdrawal threshold.", "mkoc_profit2": "Activated user count", "mkoc_profit3": "Revenue Share Income (USD)", "mkoc_profit4": "Withdraw", "mkoc_profit5": "Today", "mkoc_profit6": "Yesterday", "mkoc_profit7": "Last 7 days", "mkoc_profit8": "Last 15 days", "mkoc_profit9": "This month", "mkoc_rank": "Rankings", "mkoc_realname1": "Congratulations on completing the signing! You can now withdraw money.", "mkoc_realname2": "Withdraw", "mkoc_recommendations": "Latest recommendations", "mkoc_reel_play1": "Episodes", "mkoc_settings": "Settings", "mkoc_shelves": "Recently on the shelves", "mkoc_signin1": "Share for money", "mkoc_signin10": "Please agree to the agreement", "mkoc_signin11": "Registration succeeded", "mkoc_signin2": "Sign up as a marketer and begin earning money by sharing our apps", "mkoc_signin3": "Create an account to make money", "mkoc_signin4": "Sign in to make money", "mkoc_signin5": "Marketer Platform", "mkoc_signin6": "Enter a password.", "mkoc_signin7": "Change Password", "mkoc_signin8": "Confirm", "mkoc_signin9": "Sent Successfully", "mkoc_sort": "Sort", "mkoc_square": "Square", "mkoc_tutorial": "Tutorial", "mkoc_tutorial_1": "How to gain income?", "mkoc_tutorial_10": "Promotional link: Users click the link to access the page, download and open the app, which links them to the corresponding mini-series. They can then watch the mini-series and recharge.", "mkoc_tutorial_11": "Promotional link: When users click the link, they land on a page where they can download and open the app. The system automatically opens the corresponding novel, allowing users to view and make purchases.", "mkoc_tutorial_2": "To promote the app <span>MoboReels</span> to users, follow the steps below, and you will receive a share of their spending based on the amount they spend.", "mkoc_tutorial_3": "To promote the app <span>MoboReader</span> to users, follow the steps below. You will receive a percentage-based commission based on the amount spent on in-app purchases.", "mkoc_tutorial_4": "Marketer operation process", "mkoc_tutorial_5": "Find the mini-series you're promoting->Select a code or promotional link (make sure to copy your own code or link from the page, otherwise it will affect the order). <br><span>Note</span>: Marketers can guide users to download <span>[MoboReels]</span> from Appstore and ensure users search for the code in the app. Alternatively, the app download can be accessed in a TikTok video widget. Regardless of the method, users need to <span>search for the code</span> in the app.n", "mkoc_tutorial_6": "Find the books you're promoting->Select a code or promotional link (make sure to copy your own code or link from the page, otherwise it will affect the order). <br><span>Note</span>: Marketers can guide users to download <span>[MoboReader]</span> from Apptore and ensure users search for the code in the app. Alternatively, the app download can be accessed in a TikTok video widget. Regardless of the method, users need to <span>search for the code</span> in the app.", "mkoc_tutorial_7": "User operation process", "mkoc_tutorial_8": "Code: Users download <span>[MoboReels]</span>, search for the code in MoboReels, watch the mini-series, and recharge;", "mkoc_tutorial_9": "Users download the app, search for the shared code in <span>MoboReader</span>, and view the novel and recharge;", "mkoc_tutorial_tkurl_1": "<span>TikTok FAB (Recommended)</span><br>Note: MoboReader's official links redirection (Use region-specific URLs based on different regions)", "mkoc_tutorial_tkurl_2": "<span>TikTok FAB (Recommended)</span><br>Note: MoboReader's official links redirection (Use region-specific URLs based on different regions)", "mkoc_tutorial_tkurl_3": "U.S. URL: <span>{0}</span>", "mkoc_tutorial_tkurl_4": "To use the official link, there's no need for any extra steps. You can post as many times as you like without restrictions. Simply use your browser to directly access the link and access the download link. This link is solely for downloading purposes and is not intended for promotion. Marketers still need to guide users to search the code on the app.", "mkoc_upload": "Upload", "mkoc_upload_image": "Upload image", "mkoc_upload_image_limit": "Image files only. The image should not exceed 10 MB.", "mkoc_upload_image_only": "Image files only", "mkoc_user_agreement": "User Agreement", "mkoc_withdraw1": "Withdrawable Income (CNY)", "mkoc_withdraw10": "The minimum withdrawal amount (including): {0}dollars", "mkoc_withdraw11": "The withdrawal amount (CNY) is the total of each income detail converted based on the exchange rate on the day it was generated.", "mkoc_withdraw12": "The actual received amount = withdrawal amount - withdrawal fee (some withdrawal methods may incur fees) You can check the details in 'Withdrawal Log'.", "mkoc_withdraw13": "Withdrawable Income (USD)", "mkoc_withdraw14": "Withdrawable upon reaching USD {0} ", "mkoc_withdraw15": "The minimum withdrawal amount (including): {0}dollars", "mkoc_withdraw2": "Incorrect information?", "mkoc_withdraw3": "Go fix it", "mkoc_withdraw4": "All incomes are converted based on the exchange rate on the day they are generated. The current estimated exchange rate is {0}. Please note that the exchange rate fluctuates over time.", "mkoc_withdraw5": "Withdraw from {0} to {1}", "mkoc_withdraw6": "Withdrawable when it reaches {0} dollars", "mkoc_withdraw7": "Withdrawal Request Successful", "mkoc_withdraw8": "Notice", "mkoc_withdraw9": "You can withdraw your income from the previous month and earlier between the {0} and {1} of each month. Your account will receive it within the same month.", "mkoc_withdraw_bank1": "Bank card", "mkoc_withdraw_bank10": "Bank location", "mkoc_withdraw_bank11": "Enter bank location", "mkoc_withdraw_bank12": "Current address", "mkoc_withdraw_bank13": "Enter the recipient's current address", "mkoc_withdraw_bank2": "Bank card number", "mkoc_withdraw_bank3": "Enter bank card number", "mkoc_withdraw_bank4": "Bank name", "mkoc_withdraw_bank5": "Enter bank name", "mkoc_withdraw_bank6": "Bank branch name", "mkoc_withdraw_bank7": "Enter branch bank name", "mkoc_withdraw_bank8": "SWIFT code or routing number", "mkoc_withdraw_bank9": "Enter SWIFT code or routing number", "mkoc_withdraw_cbank1": "Location of bank", "mkoc_withdraw_cbank2": "Please enter the location of bank", "mkoc_withdraw_cbank3": "Mobile number", "mkoc_withdraw_cbank4": "Enter the number linked to your account", "mkoc_withdraw_fail1": "The receiving account details are invalid. Please modify them promptly within this month to reinitiate the withdrawal. If not, you will need to wait until the next month.", "mkoc_withdraw_fail2": "Save and Try Again", "mkoc_withdraw_fail3": "No errors? Contact customer service", "mkoc_withdraw_fail4": "Withdrawal Request Reinitiated", "mkoc_withdraw_fail5": "Reason for failure: {0}. Please verify your receiving account details and resubmit by the end of this month. If not, you will need to wait until next month.", "mkoc_withdraw_fail6": "Try Again", "mkoc_withdraw_fail7": "Receiving's Information Modification", "mkoc_withdraw_fail8": "<PERSON><PERSON><PERSON> failed. Please try again.", "mkoc_withdraw_history1": "<PERSON><PERSON><PERSON> Log", "mkoc_withdraw_history2": "All", "mkoc_withdraw_history3": "Withdrawal in Progress", "mkoc_withdraw_history4": "Successful Withdrawals", "mkoc_withdraw_history5": "Failed Withdrawals", "mkoc_withdraw_history6": "Rejected", "mkoc_withdraw_history7": "Account <PERSON><PERSON>r", "mkoc_withdraw_ing1": "Withdrawal in progress. Please pay attention to your bank statement.", "mkoc_withdraw_ing2": "If you have any questions, please <span>contact customer service</span>.", "mkoc_withdraw_ing3": "Got it", "mkoc_withdraw_payermax1": "Taxpayer identification number", "mkoc_withdraw_payermax10": "Enter agent bank account number", "mkoc_withdraw_payermax11": "Your address", "mkoc_withdraw_payermax12": "Enter your address", "mkoc_withdraw_payermax13": "Passport issuance date", "mkoc_withdraw_payermax14": "Enter passport issuance date", "mkoc_withdraw_payermax15": "Date of birth", "mkoc_withdraw_payermax16": "Enter your date of birth", "mkoc_withdraw_payermax2": "Enter taxpayer identification number", "mkoc_withdraw_payermax3": "Phone number", "mkoc_withdraw_payermax4": "Enter phone number", "mkoc_withdraw_payermax5": "SWIFT code or routing number", "mkoc_withdraw_payermax6": "Enter SWIFT code or routing number", "mkoc_withdraw_payermax7": "City of the bank", "mkoc_withdraw_payermax8": "Enter the city of the bank", "mkoc_withdraw_payermax9": "Agent bank account number", "mkoc_withdraw_payoneer1": "Payoneer account number", "mkoc_withdraw_payoneer2": "Enter Payoneer account number", "mkoc_withdraw_paypal1": "Please provide the recipient's information. The withdrawal amount will be deposited into the account you specify.", "mkoc_withdraw_paypal10": "Save", "mkoc_withdraw_paypal2": "Receiving Account", "mkoc_withdraw_paypal3": "Receiving Account Details", "mkoc_withdraw_paypal4": "Recipent name", "mkoc_withdraw_paypal5": "Enter the recipient's full name", "mkoc_withdraw_paypal6": "Country", "mkoc_withdraw_paypal7": "Enter your account's country or region", "mkoc_withdraw_paypal8": "PayPal account number", "mkoc_withdraw_paypal9": "Enter PayPal account number", "mkoc_withdraw_success1": "<PERSON><PERSON><PERSON> Amount", "mkoc_withdraw_success2": "Time", "mkoc_withdraw_success3": "Exchange rate for USD to CNY:{0}", "mkoc_withdraw_success4": "Actual received amount", "mkoc_withdraw_success5": "The actual received amount = withdrawal amount * exchange rate for USD to CNY", "mkoc_withdraw_success6": "Withdrawal fee", "mkoc_withdraw_success7": "The actual received amount = withdrawal amount - withdrawal fee", "mkoc_withdraw_toast1": "Fill out the recipient's information before withdrawal.", "mkoc_withdraw_truemoney1": "TrueMoney account number", "mkoc_withdraw_truemoney2": "Enter TrueMoney account number", "Mpr_adddesk3": "Add Mpr to your home screen", "Mpr_bind_subtitle": "Bind to Expert account and synchronize promotion data", "paypal_country": "Account Region", "paypal_email": "PayPal account number", "paypal_name": "Recipent name", "profit_China_mainland": "Chinese Mainland", "profit_China_mainland1": "The Chinese Mainland (Transactions are in CNY; you can withdraw to your domestic Chinese bank account.)", "profit_ID_number": "ID number", "profit_ID_picture": "ID card photos", "profit_ID_picture_down": "Back side of the ID card", "profit_ID_picture_explain": "1. Upload method: You can take a photo or choose from existing photos to upload. <br>2. Ensure that the ID card is fully displayed in the photo without any obstructions. <br>3. The photo should be clear, showing the background pattern and text without blurriness or white spots.<br>Examples of uploading an ID card photo:", "profit_ID_picture_up": "Front side of the ID card", "profit_ID_picture_upload": "Upload", "profit_address": "Contact address", "profit_certificate": "Identity verification", "profit_certificate_state": "Verification status", "profit_certificate_tips": "(To ensure a successful income withdrawal, please use your authentic personal identification information. Identification details cannot be changed after the withdrawal is made.)", "profit_certificate_unverified": "Unverified", "profit_certificate_verified": "Verified", "profit_country": "Country or region", "profit_next": "Next", "profit_other_countries": "Nations or regions other than Chinese Mainland", "profit_other_countries1": "Other Countries and Regions (Transactions are in USD; withdrawals are supported via Payoneer, Checus, PayPal, bank cards, and PayerMax.)", "profit_real_name": "Real name", "profit_tel": "Contact number", "promote_tip_title": "Congratulations on completing the signing! Now click on the category menu on the left to start generating income.", "receipt_account": "Receiving Account", "receipt_tips": "(To ensure smooth payment of earnings, please use your own bank card information.)", "reset_code_back_bottom": "Return to log in", "reset_code_confirm_bottom": "Confirm", "reset_code_done": "Password updated. Please log in with the new password", "reset_code_email": "Email address", "reset_code_email_false": "Invalid email address. Please enter a valid email address.", "reset_code_email_notexist": "The account doesn't exist", "reset_code_email_tip": "Please enter your login email address.", "reset_code_sendemail": "Confirm and send the email", "reset_code_successed": "Reset password email has been sent. <br>Please go to the email address{0}to reset your password.", "reset_code_time_limit": "Email validity period: 2 hours", "reward_game_tasktitle": "Congrats on getting", "sign_contract_agreement": "I have carefully read the promotion cooperation agreement and have no objections to its content.", "sign_contract_botton": "Agree to sign the relevant promotion cooperation agreement", "sign_contract_confirm_bottom": "I've checked the email and signed the paper", "sign_contract_fail_to_receive": "Didn't receive an email?", "sign_contract_havenot_sign": "Please slide to read the full text of the Joint Promotion Agreement and confirm your signature at the end.", "sign_contract_not_sign_status": "You haven't signed the agreement yet. Please check your email again and confirm your signature.", "sign_contract_refuse_sign_status": "You've rejected the agreement. Please go back to review your information and regenerate the agreement, or you won't be able to proceed.", "sign_contract_resent": "Click here to resend the email", "sign_contract_resent_success": "An email has been sent. Please check your inbox.", "sign_contract_tips_to_overseas": "An exclusive Joint Promotion Agreement has been generated for you and sent to your mailbox at {0}. Please check your email, complete the signing on time, and start promoting to generate income once signed successfully!", "sign_flows1_complete_profile": "Complete personal info", "sign_flows1_confirm": "Identification unverified", "sign_flows1_confirmed": "Identification verified", "sign_flows2_sign": "Unsigned", "sign_flows2_sign_the_contract": "Sign promo agreement", "sign_flows2_signed": "Signed", "sign_flows3_notqualified": "Undone", "sign_flows3_qualified": "Go now!", "sign_flows3_start_share": "Start promoting and generate income!", "signin_access_code": "Send code", "signin_agreement": "I have read and agree to {0} and {1}.", "signin_agreement1": "Privacy Policy", "signin_agreement2": "User Agreement", "signin_confirm_code": "Confirm password", "signin_email": "Enter email address", "signin_private_code": "6-20 chars, at least two of the following: letters, numbers or symbols", "signin_resend": "Resend in {0}", "signin_sign_and_log": "Register and Log In", "signin_title": "Registration", "signin_verifiable_code": "Enter the 4-digit code", "tutorial_facebook_description1": "Click on the content you like and select 【Promote】", "tutorial_facebook_description10": "Select 【Short Drama Promotion Link】 and click Copy", "tutorial_facebook_description11": "Open Facebook and click Publish", "tutorial_facebook_description12": "Click 【Image/Video】 to select the video to publish", "tutorial_facebook_description13": "Paste the previously copied link and guide fans to view it.", "tutorial_facebook_description14": "Click 【Publish】", "tutorial_facebook_description2": "Click 【Copy】 or 【Play】 to download the video", "tutorial_facebook_description3": "Click 【Download】", "tutorial_facebook_description4": "After downloading, click 【Download List】", "tutorial_facebook_description5": "Click the corresponding video", "tutorial_facebook_description6": "Click the 【Share】 button", "tutorial_facebook_description7": "Click 【Save Video】 and the video will be saved to the album", "tutorial_facebook_description8": "Return to the Mpr homepage, select the corresponding content, and click 【Promote】", "tutorial_facebook_description9": "Scroll the page to the 【Promote】 module", "tutorial_step_one": "Step 1", "tutorial_step_one_description1": "MoboTree is a tool that allows you to add a link in your social media profile or comment section. When your followers click on this link, they can view the content you recommend and select.", "tutorial_step_one_description2": "<span style=\"color: #FF8124;\">Method 1: </span>Click <strong style=\"color: #121212;\">\"Home,\"</strong> select the content you like, then click <strong style=\"color: #121212;\">\"Promotion\"</strong> to view <strong style=\"color: #121212;\">\"Details.\"</strong> Finally, click <strong style=\"color: #121212;\">\"Add to MoboTree\"</strong> to add the content.", "tutorial_step_one_description3": "<span style=\"color: #FF8124;\">Method 2: </span>Click <strong style=\"color: #121212;\">\"MoboTree\"</strong> on the bottom menu bar, then click <strong style=\"color: #121212;\">\"Platform content.\"</strong> After choosing your preferred content, click <strong style=\"color: #121212;\">\"Add to MoboTree.\"</strong>", "tutorial_step_one_img_tip1": "Link MoboTree to your profile page. When your followers click this link, they'll be directed to a page featuring a collection of links.", "tutorial_step_one_img_tip2": "1. Click on <span style=\"color: #FF8124;\">\"Home\"</span>", "tutorial_step_one_img_tip3": "2. Choose the content you like and click <span style=\"color: #FF8124;\">\"Promotion\"</span>", "tutorial_step_one_img_tip4": "3. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Add to MoboTree\"</span>", "tutorial_step_one_img_tip5": "1. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"MoboTree\"</span> on the bottom menu bar", "tutorial_step_one_img_tip6": "2. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Platform content\"</span>", "tutorial_step_one_img_tip7": "3. Select the content you like", "tutorial_step_one_img_tip8": "4. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Add to MoboTree\"</span>", "tutorial_step_one_title1": "Select the content you are interested in and join MoboTree", "tutorial_step_one_title2": "What is MoboTree?", "tutorial_step_one_title3": "How to add content to MoboTree?", "tutorial_step_three": "Step 3", "tutorial_step_three_description1": "Open TikTok, click <strong style=\"color: #121212;\">[+]</strong>, and create a post. In the post title and comment section, encourage users to check out your profile with a message like, \"Check out my profile for more exciting content!\"", "tutorial_step_three_description2": "<span style=\"color: #FF8124;\">It is highly recommended to use your own edited videos</span> to gain more traffic. The platform offers video materials that you can download from the details page. Steps: Click [Home], select [Promotion] and click [Download].", "tutorial_step_three_img_tip1": "1. Open TikTok and click <span style=\"color: #FF8124;\">[+]</span>", "tutorial_step_three_img_tip2": "2. Upload the video", "tutorial_step_three_img_tip3": "3. Use the post title to encourage users to explore your profile, such as: \"Check out my profile for more exciting content!\"", "tutorial_step_three_img_tip4": "4. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Save\"</span>", "tutorial_step_three_img_tip5": "Click <span style=\"color: #FF8124;\">the \"Download\" icon</span>. You can also click the \"Copy\" icon to copy the link and paste it into your browser or on your PC to download the videos.", "tutorial_step_three_title1": "Post on TikTok and direct your followers to check out your profile and explore MoboTree", "tutorial_step_three_title2": "How to post on TikTok?", "tutorial_step_three_title3": "What type of content can be posted?", "tutorial_step_two": "Step 2", "tutorial_step_two_description1": "<span style=\"color: #FF8124;\">Method 1: </span>Click \"MoboTree\" on the bottom menu bar, then select \"Share\" and \"Copy.\"", "tutorial_step_two_description2": "<span style=\"color: #FF8124;\">Method 2: </span>Click \"Home,\" then tap \"Promotion\" and select the \"Copy\" icon.", "tutorial_step_two_description3": "Open <strong style=\"color: #121212;\">Tik<PERSON></strong>, tap <strong style=\"color: #121212;\">Me</strong>, select <strong style=\"color: #121212;\">Edit profile</strong>, click<strong style=\"color: #121212;\"> Bio</strong> and paste the copied MoboTree link into your profile.", "tutorial_step_two_img_tip1": "1. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"MoboTree\"</span> on the bottom menu bar", "tutorial_step_two_img_tip10": "4. Click on <span style=\"color: #FF8124;\">\"Save\"</span>", "tutorial_step_two_img_tip11": "5. Once saved, your MoboTree link will appear on your profile page, enabling your followers to click and view the content you recommend", "tutorial_step_two_img_tip2": "2. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Share\"</span>", "tutorial_step_two_img_tip3": "3. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Copy\"</span>", "tutorial_step_two_img_tip4": "1. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Home\"</span>", "tutorial_step_two_img_tip5": "2. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Promotion\"</span>", "tutorial_step_two_img_tip6": "3. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Copy\"</span> icon", "tutorial_step_two_img_tip7": "1. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Edit profile\"</span>", "tutorial_step_two_img_tip8": "2. <PERSON><PERSON> <span style=\"color: #FF8124;\">\"Add to your profile\"</span>", "tutorial_step_two_img_tip9": "3. Paste the copied MoboTree link here", "tutorial_step_two_title1": "Link MoboTree to your TikTok profile page", "tutorial_step_two_title2": "How to get your MoboTree link?", "tutorial_step_two_title3": "How to link MoboTree to your TikTok profile page?", "tutorial_tiktok_description1": "Click on the content you like and select 【Promote】", "tutorial_tiktok_description10": "Click 【Copy】 to get the MoboTree link. If you want to preview MoboTree, click 【MoboTree】", "tutorial_tiktok_description11": "Open Tiktok and edit your Tiktok homepage", "tutorial_tiktok_description12": "Click 【Bio】", "tutorial_tiktok_description13": "Paste the previously copied MoboTree link", "tutorial_tiktok_description14": "Click 【Save】", "tutorial_tiktok_description15": "Now your followers can see the MoboTree link on your Tiktok homepage", "tutorial_tiktok_description16": "Open Tiktok and click 【+】", "tutorial_tiktok_description17": "Select the video you want to upload", "tutorial_tiktok_description18": "Edit the post caption to guide followers to the homepage to click MoboTree, e.g., \"Click the homepage to see the full content\"", "tutorial_tiktok_description19": "Click 【Publish】", "tutorial_tiktok_description2": "Click 【Copy】 or 【Play】 to download the video", "tutorial_tiktok_description3": "Click 【Download】", "tutorial_tiktok_description4": "After downloading, click 【Download List】", "tutorial_tiktok_description5": "Click the corresponding video", "tutorial_tiktok_description6": "Click the 【Share】 button", "tutorial_tiktok_description7": "Click 【Save Video】 to save it to your album", "tutorial_tiktok_description8": "Return to the Mpr homepage, select the corresponding content, and click 【Promote】", "tutorial_tiktok_description9": "Click 【+】 to join MoboTree", "tutorial_tiktok_title": "3 simple steps to earn income", "tutorial_tiktok_title2": "Tiktok & MoboTree Step-by-Step Tutorial", "waitLoading": "Loading, please wait...", "withdraw_account_tip": "Any changes to the receiving account information will be synchronized to [Profile-Personal Information-Receiving Account] after it is saved successfully.", "withdraw_actual": "Actual payment$", "withdraw_actual_tip": "1. The actual payment amount after deducting the handling fee;<br> 2. If you find that the actual arrival and this amount are not the same, it may be that the payment platform has charged other handling fees, and you can check with the payment platform.", "withdraw_amount": "Total Amount", "withdraw_anomalous": "Error: {0}", "withdraw_anomalous_cause01": "The payment information of the provided account is incorrect, please check and change the information of the receiving account.", "withdraw_anomalous_cause02": "Marketer's account cannot receive (PayPal restricts reception. Please contact Paypal or wait 21 days for payment to arrive)", "withdraw_anomalous_cause03": "Payee's account cannot receive USD. Please change to an account that supports USD.", "withdraw_anomalous_cause04": "Unable to pay due to legislative requirements", "withdraw_anomalous_cause05": "Payee's unable to receive due to regional lockout", "withdraw_anomalous_cause06": "Incorrect account information (Swift Code/ABA, name, bank name, etc.)", "withdraw_anomalous_cause07": "Reaching payee's account limits", "withdraw_anomalous_cause08": "Account is unable to receive US dollars", "withdraw_anomalous_cause09": "Regional lockout", "withdraw_anomalous_cause10": "Transit bank is unable to process (international wire transfers)", "withdraw_anomalous_cause11": "Incorrect bank code.", "withdraw_anomalous_cause12": "Bank card number does not match the name of the bank.", "withdraw_anomalous_cause13": "Bank card number does not match the name of the account holder.", "withdraw_anomalous_cause14": "Incorrect tax code of the payee", "withdraw_anomalous_cause15": "Payermax volatility", "withdraw_anomalous_cause16": "Invalid account information. Not a payoneer account.", "withdraw_anomalous_cause17": "Incomplete account information (Contact your local payoneer customer service for additional information)", "withdraw_anomalous_cause18": "Other", "withdraw_available": "<PERSON><PERSON><PERSON><PERSON>", "withdraw_bank_charge": "Handling fees incurred when transferring", "withdraw_bar": "Withdrawal Management", "withdraw_bottom_tip1": "1. Please initiate withdrawal requests from the {0} to {1} of each month;", "withdraw_bottom_tip2": "2. <PERSON><PERSON><PERSON> threshold: {0}", "withdraw_detail": "<PERSON><PERSON><PERSON> Breakdown", "withdraw_exchange_rate_change": "Please note that the exchange rate has been updated", "withdraw_frozen": "Transferring", "withdraw_id": "Slip number of withdrawal", "withdraw_search": "Search by month (account period)", "withdraw_status01": "Review pending", "withdraw_status01_explain": "A withdrawal request has been submitted and is awaiting review.", "withdraw_status02_explain": "Payment pending", "withdraw_status03_explain": "The money has been transferred for you. It will arrive in 3-5 working days. Please check your account.", "withdraw_status04_explain": "Please modify your withdrawal request and submit it as soon as possible within the same month, and allow at least 1~2 days for the transfer, otherwise the transfer will fail and you will need to re-initiate the withdrawal request the following month.", "withdraw_status05_explain": "Unfortunately, the payment failed. Please re-initiate the withdrawal request during the next withdrawal time.", "withdraw_status06_explain": "Not approved. Please resubmit your application the following month.", "withdraw_time": "Payment time", "writecode_please": "Enter invitation code"}