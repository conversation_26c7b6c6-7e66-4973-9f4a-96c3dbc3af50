<template>
  <div class="layout">
    <div class="body">
      <RouterView />
    </div>
    <van-tabbar route active-color="#ff6600">
      <!-- 招募/保洁入口 - 根据用户角色动态显示 -->
      <van-tabbar-item v-if="isCleaningStaff" replace to="/layout/roomClean" icon="brush-o">
        <span>保洁</span>
      </van-tabbar-item>
      <van-tabbar-item  replace to="/layout/home" icon="wap-home-o">
        <span>招募</span>
      </van-tabbar-item>
      <!-- 我的项目入口 - 仅在有项目时显示 -->
      <van-tabbar-item v-if="hasProjects&&!isCleaningStaff" replace to="/layout/myProjects" icon="orders-o">
        <span>我的项目</span>
      </van-tabbar-item>
      <van-tabbar-item replace to="/layout/mine" icon="contact-o">
        <span>我的</span>
      </van-tabbar-item>
    </van-tabbar>

    <!-- 绑定邮箱弹窗 -->
    <BindEmailModal
      v-model:show="showBindEmailModal"
      @success="handleBindEmailSuccess"
      @skip="handleBindEmailSkip" />
  </div>
</template>

<script setup>
import { onMounted, computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'
import useMyProjectStore from '@/stores/myProject'
import { showToast } from 'vant'
import BindEmailModal from '@/components/BindEmailModal.vue'

const router = useRouter()
const loginStore = useLoginStore()
const homeStore = useHomeStore()
const myProjectStore = useMyProjectStore()

const showBindEmailModal = ref(false)
const projectsLoading = ref(false)

// 从store获取用户信息
const userInfo = computed(() => loginStore.getUserInfo)

// 从store获取当前角色
const currentRole = computed(() => loginStore.getCurrentRole)

// 从store获取账户信息
const accountInfo = computed(() => homeStore.accountInfo)

// 检查是否有项目
const hasProjects = computed(() => myProjectStore.hasProjects)

// 检查是否是保洁员工
const isCleaningStaff = computed(() => {
  return currentRole.value === 'staff' && accountInfo.value?.roleType === 51
})

// 获取我的项目列表
const fetchMyProjects = async () => {
  if (!loginStore.getCurrentRole) {
    return
  }

  projectsLoading.value = true
  try {
    const personType = loginStore.getCurrentPersonType
    await myProjectStore.fetchMyProjects(personType)
  } catch (error) {
    console.error('获取项目列表失败:', error)
    // 不阻断页面加载，只是记录错误
  } finally {
    projectsLoading.value = false
  }
}

// 布局初始化逻辑
onMounted(async () => {
  // 检查登录状态
  if (!loginStore.isLoggedIn) {
    router.push('/login')
    return
  }

  // 初始化用户信息（从localStorage恢复到store）
  loginStore.initUserInfo()

  // 初始化角色信息（从sessionStorage恢复到store）
  loginStore.initRole()

  // 检查是否有角色信息
  if (!loginStore.getCurrentRole) {
    // 如果没有角色信息，跳转到角色选择页面
    router.push('/switch')
    return
  }

  // 根据用户角色获取账户信息
  try {
    await homeStore.fetchAccountInfo(loginStore.getCurrentRole)
  } catch (error) {
    console.error('获取账户信息失败:', error)
    // 不阻断页面加载，只是记录错误
  }


  // 处理存储的项目ID跳转
  const storedProjectId = sessionStorage.getItem('applayProjectId')
  const joinProjectId = sessionStorage.getItem('joinProjectId')

  if (storedProjectId||joinProjectId) {
    if (accountInfo?.value?.personId) {
      // 跳转到项目详情页
      if(joinProjectId){
        sessionStorage.removeItem('joinProjectId')
        router.push({
          name:  'joinProject',
          params: { id: joinProjectId },
        })
      }else if(storedProjectId){
        sessionStorage.removeItem('applayProjectId')
        router.push({
          name: 'recruitmentDetail',
          params: { id: storedProjectId },
        })
      }
     
    } else {
      // 跳转到个人信息页面
      if (currentRole.value === 'staff') {
        router.push('/staffInfo')
      } else if (currentRole.value === 'actor') {
        router.push('/actor')
      }
    }
  } else {
    // 检查邮箱绑定状态
    checkEmailBindingStatus()
  }

  // 获取我的项目列表
  fetchMyProjects()
})

// 检查邮箱绑定状态
const checkEmailBindingStatus = () => {
  const userInfo = loginStore.getUserInfo
  // 如果用户未绑定邮箱或跳过了绑定，显示绑定邮箱弹窗
  if (!userInfo?.hasBindEmail || userInfo?.hasBindEmail === '0') {
    // 延迟显示弹窗，确保页面已经加载完成
    setTimeout(() => {
      showBindEmailModal.value = true
    }, 200)
  }
}

// 处理邮箱绑定成功
const handleBindEmailSuccess = () => {
  showToast('邮箱绑定成功！')
  showBindEmailModal.value = false
}

// 处理跳过邮箱绑定
const handleBindEmailSkip = () => {
  showToast('已跳过邮箱绑定')
  showBindEmailModal.value = false
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: $bg-secondary;
  overflow-y: auto;
}

.body {
  width: 100%;
  flex: 1;
  padding-bottom: 54px;
}
</style>
