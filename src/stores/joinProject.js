import { defineStore } from 'pinia'
import { get as requestGet } from '@/utils/request'

export default defineStore({
    id: 'project',
    state: () => {
        return {
            // 项目详情信息
            projectInfo: null,
            // 项目人员房间信息
            projectRoomInfo: null,
            // 加载状态
            loading: false,
            // 错误信息
            error: null,
            // 是否已加入项目状态
            hasJoinedProject: false
        }
    },
    persist: true,
    getters: {
        // 获取项目基本信息
        getProjectInfo: (state) => state.projectInfo,

        // 获取项目房间信息
        getProjectRoomInfo: (state) => state.projectRoomInfo,

        // 获取项目状态文本
        getProjectStatusText: (state) => {
            if (!state.projectInfo) return ''
            const statusMap = {
                0: '剧本调研',
                1: '项目筹备',
                2: '拍摄中',
                3: '后期制作',
                4: '上架准备',
                5: '上架准备',
                6: '暂停',
                7: '取消',
                8: '补拍'
            }
            return statusMap[state.projectInfo.status] || '未知状态'
        },

        // 检查是否已加入项目
        getHasJoinedProject: (state) => state.hasJoinedProject
    },
    actions: {
        // 根据ID获取项目详情
        async fetchProjectById(id) {
            this.loading = true
            this.error = null
            try {
                const res = await requestGet('/PrProductions/GetProductionsById', { id })
                if (res?.data?.dataList) {
                    this.projectInfo = res?.data?.dataList
                    return {
                        status: true,
                        data: res,
                        message: '获取项目信息成功'
                    }
                } else {
                    this.error = '获取项目信息失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },
        // 根据ID获取项目详情
        async fetchProjects(personType) {
            try {
                const res = await requestGet('/PrProductions/WxProductions', { personType })

            } catch (error) {

            } finally {
            }
        },
        // 检查是否已加入项目
        async checkHasJoinedProject(productionId, personType) {
            this.loading = true
            this.error = null
            try {
                const res = await requestGet('/PrProductions/WxHasJoinPersonRoom', {
                    productionId,
                    personType
                })
                if (res?.data) {
                    this.hasJoinedProject = res.data?.hasJoin || false
                    return {
                        status: true,
                        data: res.data,
                        message: res.message || '检查成功'
                    }
                } else {
                    this.error = res.message || '检查失败'
                    return {
                        status: false,
                        data: false,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: false,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 加入项目
        async joinProject(productionId, personType, roleType = null) {
            this.loading = true
            this.error = null
            try {
                const params = {
                    productionId,
                    personType
                }

                // 如果提供了 roleType，添加到参数中
                if (roleType !== null && roleType !== undefined) {
                    params.roleType = roleType
                }
                const res = await requestGet('/PrProductions/WxJoinProductions', params)
                if (res.status) {

                    this.hasJoinedProject = true
                    return {
                        status: true,
                        data: res.data,
                        message: res.message || '加入项目成功'
                    }
                } else {

                    this.error = res.message || '加入项目失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 获取项目人员房间信息
        async fetchProjectRoomInfo(productionId, personType) {
            this.loading = true
            this.error = null
            try {
                const res = await requestGet('/PrProductions/ProductionPersonRoomDto', {
                    productionId,
                    personType
                })
                if (res?.data?.dataList) {
                    res.data.dataList.endTime = res.data.endTime
                    res.data.dataList.startTime = res.data.startTime
                    this.projectRoomInfo = res.data.dataList
                    return {
                        status: true,
                        data: res,
                        message: '获取项目房间信息成功'
                    }
                } else {
                    // 没有房间信息时也算成功，只是数据为空
                    this.projectRoomInfo = null
                    return {
                        status: true,
                        data: null,
                        message: '暂无房间信息'
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 重置项目信息
        resetProjectInfo() {
            this.projectInfo = null
            this.projectRoomInfo = null
            this.hasJoinedProject = false
            this.error = null
        },

        // 设置项目信息（用于外部设置）
        setProjectInfo(projectInfo) {
            this.projectInfo = projectInfo
        },

        // 设置项目房间信息（用于外部设置）
        setProjectRoomInfo(projectRoomInfo) {
            this.projectRoomInfo = projectRoomInfo
        },

        // 更新加入项目状态
        updateJoinStatus(status) {
            this.hasJoinedProject = status
        }
    }
})