import { defineStore } from 'pinia'
import { get as requestGet } from '@/utils/request'

export default defineStore({
  id: 'myProject',
  state: () => {
    return {
      // 项目列表数据
      projectList: [],
      // 当前选中的项目
      currentProject: null,
      // 当前项目的房间信息
      currentProjectRoom: null,

      // 加载状态
      loading: false,
      // 错误信息
      error: null,
    }
  },
  persist: false,
  getters: {
    // 获取当前项目信息
    getCurrentProject: (state) => {
      return state.currentProject
    },
    // 获取当前项目房间信息
    getCurrentProjectRoom: (state) => {
      return state.currentProjectRoom
    },
    // 获取项目列表
    getProjectList: (state) => {
      return state.projectList
    },
    // 检查是否有项目数据
    hasProjects: (state) => {
      return state.projectList && state.projectList.length > 0
    }
  },
  actions: {
    // 获取参与的项目列表
    async fetchMyProjects(personType) {
      this.loading = true
      this.error = null
      try {
        const res = await requestGet('/PrProductions/WxProductions', { personType })
        if (res.status && res.data?.dataList) {
          this.projectList = res.data.dataList
          // 如果有项目且当前没有选中项目，默认选择第一个
          if (this.projectList.length > 0 && !this.currentProject) {
            this.setCurrentProject(this.projectList[0])
          }
          return {
            status: true,
            data: res.data,
            message: '获取项目列表成功'
          }
        } else {
          this.error = res.message || '获取项目列表失败'
          return {
            status: false,
            data: null,
            message: this.error
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return {
          status: false,
          data: null,
          message: this.error
        }
      } finally {
        this.loading = false
      }
    },

    // 设置当前项目
    setCurrentProject(project) {
      this.currentProject = project
      // 清空之前的房间信息
      this.currentProjectRoom = null
    },

    // 获取当前项目的房间信息
    async fetchCurrentProjectRoom(personType) {
      if (!this.currentProject) {
        return {
          status: false,
          message: '没有选中的项目'
        }
      }

      this.loading = true
      this.error = null
      try {
        const res = await requestGet('/PrProductions/ProductionPersonRoomDto', {
          productionId: this.currentProject.id,
          personType
        })
        if (res.status && res.data?.dataList) {
          res.data.dataList.endTime = res.data.endTime
          res.data.dataList.startTime = res.data.startTime
          this.currentProjectRoom = res.data.dataList
          return {
            status: true,
            data: res.data.dataList,
            message: '获取房间信息成功'
          }
        } else {
          // 没有房间信息时也算成功，只是数据为空
          this.currentProjectRoom = null
          return {
            status: true,
            data: null,
            message: '暂无房间信息'
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return {
          status: false,
          data: null,
          message: this.error
        }
      } finally {
        this.loading = false
      }
    },

    // 清空数据
    clearData() {
      this.projectList = []
      this.currentProject = null
      this.currentProjectRoom = null
      this.error = null
    }
  }
})
