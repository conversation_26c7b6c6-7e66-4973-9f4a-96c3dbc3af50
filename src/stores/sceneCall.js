import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
    id: 'sceneCall',
    state: () => {
        return {
            sceneCallList: [], // 通告单列表数据
            loading: false, // 加载状态
            error: null // 错误信息
        }
    },
    persist: false,
    getters: {

    },
    actions: {
        // 获取项目通告单列表
        async fetchSceneCall(productionId, personType) {
            this.loading = true
            this.error = null
            try {
                const res = await get('/PrProductions/GetWxSceneCall', {
                    productionId,
                    personType
                })

                if (res.status && res.data?.dataList) {
                    // 处理返回的数组数据
                    this.sceneCallList = Array.isArray(res.data?.dataList) ? res.data?.dataList : []
                    return {
                        status: true,
                        data: this.sceneCallList,
                        message: res.message || ''
                    }
                } else {
                    this.error = res.message || '获取通告单失败'
                    return {
                        status: false,
                        data: [],
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: [],
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 重置通告单数据
        resetSceneCall() {
            this.sceneCallList = []
            this.error = null
        },

        // 清除错误信息
        clearError() {
            this.error = null
        },

        // 刷新通告单数据（重新获取）
        async refreshSceneCall(productionId, personType) {
            return await this.fetchSceneCall(productionId, personType)
        },

        // 保存项目销场记录
        async saveSceneVerification(personType, verification) {
            this.loading = true
            this.error = null
            try {
                const res = await post('/PrProductions/SaveWxSceneVerification', {
                    personType,
                    verification
                })

                if (res.status) {
                    return {
                        status: true,
                        data: res.data,
                        message: res.message || '销场成功'
                    }
                } else {
                    this.error = res.message || '销场失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 删除项目销场记录
        async deleteSceneVerification(id, productionId, personType) {
            this.loading = true
            this.error = null
            try {
                const res = await get('/PrProductions/DeleteWxSceneVerification', {
                    id,
                    productionId,
                    personType
                })

                if (res.status) {
                    return {
                        status: true,
                        data: res.data,
                        message: res.message || '删除销场记录成功'
                    }
                } else {
                    this.error = res.message || '删除销场记录失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },
    }
})