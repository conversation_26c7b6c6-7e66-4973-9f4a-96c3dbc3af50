import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
  id: 'contract',
  state: () => {
    return {
      contractList: [], // 合同列表
      loading: false, // 加载状态
      error: null // 错误信息
    }
  },
  persist: false,
  getters: {
    // 获取合同总数
    contractCount: (state) => state.contractList.length,

    // 获取有效合同列表（排除已删除的）
    validContractList: (state) => {
      return state.contractList.filter(contract => !contract.isDelete)
    },

    // 按状态分组的合同
    contractsByStatus: (state) => {
      const grouped = {}
      state.contractList.forEach(contract => {
        const status = contract.status || 0
        if (!grouped[status]) {
          grouped[status] = []
        }
        grouped[status].push(contract)
      })
      return grouped
    }
  },
  actions: {
    // 获取合同列表
    async fetchContractList(role) {
      this.loading = true
      this.error = null
      try {
        // 根据角色确定 personType 参数：0人员，1演员
        const personType = role === 'actor' ? 1 : 0
        const res = await get('/CommContract/GetCommContractByUnionId', { personType })

        if (res.status && res.data) {
          // 处理合同列表数据
          this.contractList = res.data.dataList || res.data || []
          return {
            status: true,
            data: this.contractList,
            message: res.message || ''
          }
        } else {
          this.error = res.message || '获取合同列表失败'
          return {
            status: false,
            data: [],
            message: this.error
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return {
          status: false,
          data: [],
          message: this.error
        }
      } finally {
        this.loading = false
      }
    },

    // 根据ID获取单个合同
    getContractById(contractId) {
      return this.contractList.find(contract => contract.id === contractId)
    },

    // 更新合同状态（本地更新，不调用接口）
    updateContractStatus(contractId, status) {
      const contract = this.getContractById(contractId)
      if (contract) {
        contract.status = status
      }
    },

    // 添加合同到列表（用于实时更新）
    addContract(contract) {
      const existingIndex = this.contractList.findIndex(c => c.id === contract.id)
      if (existingIndex >= 0) {
        // 如果合同已存在，更新它
        this.contractList[existingIndex] = contract
      } else {
        // 如果是新合同，添加到列表开头
        this.contractList.unshift(contract)
      }
    },

    // 移除合同（标记为删除）
    removeContract(contractId) {
      const contract = this.getContractById(contractId)
      if (contract) {
        contract.isDelete = true
      }
    },

    // 重置合同列表
    resetContractList() {
      this.contractList = []
      this.error = null
    },

    // 清除错误信息
    clearError() {
      this.error = null
    },

    // 刷新合同列表（重新获取）
    async refreshContractList(role) {
      return await this.fetchContractList(role)
    }
  }
})
