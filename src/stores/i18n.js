import { defineStore } from 'pinia'
import cnTranslation from '@/locales/cn.json'
import { withFallback } from '@/utils/index'

export default defineStore({
  id: 'langResource',
  state: () => {
    return {
      messages: {},
      messagescn: {},
      loaded: false,
      isCN: true,
    }
  },
  persist: true,
  actions: {
    async fetchI18nMessages(type = 'cn') {
      // 项目只使用中文，不再从接口获取数据
      localStorage.lang = 'cn'
      this.loaded = true
      this.messages = withFallback({}, cnTranslation)
    },
    async isCNAsync() {
      // 项目只使用中文，直接返回true
      this.isCN = true
      return true
    },
  },
})
