import { defineStore } from 'pinia'
import { get } from '@/utils/request'

export default defineStore({
    id: 'todaySceneCall',
    state: () => {
        return {
            todaySceneCall: null, // 当天通告单数据
            loading: false, // 加载状态
            error: null // 错误信息
        }
    },
    persist: false,
    getters: {
        // 获取当天通告单数据
        getTodaySceneCall: (state) => state.todaySceneCall,

        // 获取当天场景列表
        getTodayScenes: (state) => state.todaySceneCall?.sceneInfos || [],

        // 获取用餐信息
        getMeals: (state) => state.todaySceneCall?.meals || [],

        // 获取演员信息
        getActors: (state) => state.todaySceneCall?.actors || [],

        // 检查是否有通告单数据
        hasSceneCallData: (state) => state.todaySceneCall !== null,

        // 检查是否有场景数据
        hasSceneData: (state) => {
            const scenes = state.todaySceneCall?.sceneInfos || []
            return scenes.length > 0
        },

        // 获取加载状态
        isLoading: (state) => state.loading,

        // 获取错误信息
        getError: (state) => state.error,

        // 获取天气信息
        getWeatherInfo: (state) => state.todaySceneCall?.weatherStr,

        // 按计划类型分组场景
        getScenesByPlanType: (state) => {
            const grouped = {
                scenes: [], // planType = 0 场次明细
                remarks: [], // planType = 1 场次备注
                transfers: [] // planType = 2 转场
            }

            const scenes = state.todaySceneCall?.sceneInfos || []
            scenes.forEach(scene => {
                if (scene.planType === 0) {
                    grouped.scenes.push(scene)
                } else if (scene.planType === 1) {
                    grouped.remarks.push(scene)
                } else if (scene.planType === 2) {
                    grouped.transfers.push(scene)
                }
            })

            return grouped
        },

        // 获取场景统计
        getSceneStats: (state) => {
            const stats = {
                total: 0,
                scenes: 0,
                remarks: 0,
                transfers: 0
            }

            const scenes = state.todaySceneCall?.sceneInfos || []
            scenes.forEach(scene => {
                stats.total++
                if (scene.planType === 0) {
                    stats.scenes++
                } else if (scene.planType === 1) {
                    stats.remarks++
                } else if (scene.planType === 2) {
                    stats.transfers++
                }
            })

            return stats
        },

        // 获取通告单基本信息
        getSceneCallInfo: (state) => {
            if (!state.todaySceneCall) return null
            return {
                id: state.todaySceneCall.id,
                productionId: state.todaySceneCall.productionId,
                dayNumber: state.todaySceneCall.dayNumber,
                scheduleRemark: state.todaySceneCall.scheduleRemark,
                responsibleDept: state.todaySceneCall.responsibleDept,
                contact: state.todaySceneCall.contact,
                remark: state.todaySceneCall.remark,
                createTime: state.todaySceneCall.createTime,
                updateTime: state.todaySceneCall.updateTime
            }
        }
    },
    actions: {
        // 获取项目当天通告单
        async fetchTodaySceneCall(productionId, personType) {
            this.loading = true
            this.error = null
            try {
                const res = await get('/PrProductions/GetWxSingleSceneCall', {
                    productionId,
                    personType
                })

                if (res.status && res.data?.dataList) {
                    // 接口返回的是包含 dataList 的对象
                    this.todaySceneCall = res.data.dataList
                    this.todaySceneCall.weatherStr = res.data.weatherStr
                    return {
                        status: true,
                        data: this.todaySceneCall,
                        message: res.message || '获取当天通告单成功'
                    }
                } else {
                    this.error = res.message || '获取当天通告单失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 重置通告单数据
        resetTodaySceneCall() {
            this.todaySceneCall = null
            this.error = null
        },

        // 清除错误信息
        clearError() {
            this.error = null
        },

        // 刷新通告单数据（重新获取）
        async refreshTodaySceneCall(productionId, personType) {
            return await this.fetchTodaySceneCall(productionId, personType)
        }
    }
})
