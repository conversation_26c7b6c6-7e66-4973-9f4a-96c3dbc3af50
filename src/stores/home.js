import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

const initInfo = {
  // 基础信息
  id: null,
  personId: null,
  personName: '',
  dateOfBirth: null,
  isChildActor: false,
  gender: 1, // 1: 男, 2: 女
  personType: 1, // 1: 个人, 2: 团体
  idNumber: '',
  idCardFrontPhoto: '',
  idCardVersoPhoto: '',
  address: '',
  phone: '',
  eMail: '',

  // 角色特有信息
  stageName: '', // 演员特有
  isInternal: 0, // 0: 否, 1: 是
  height: null, // 演员特有
  weight: null, // 演员特有
  bust: null, // 演员特有
  waist: null, // 演员特有
  hips: null, // 演员特有
  headUrl: '', // 演员特有
  school: '', // 演员特有
  specialty: '', // 演员特有
  actingStyle: '', // 演员特有
  cityName: '', // 演员特有
  jobNumber: '', // 工作人员特有

  // 银行信息
  bankName: '',
  accountName: '',
  accountNumber: '',
  bankInfos: [],

  // 其他信息
  unionId: '',
  fddVerifyStatus: null,
  fddCustomerVerifyUrl: '',
  roleType: null,
  company: '',
  isDelete: false,
  createTime: '',
  updateTime: '',
  creator: '', // 工作人员特有
  lastModifier: '' // 工作人员特有
}
export default defineStore({
  id: 'home',
  state: () => {
    return {
      data: {},
      accountInfo: { ...initInfo },
      loading: false,
      error: null
    }
  },
  persist: true,
  actions: {
    // 根据用户角色获取账户信息
    async fetchAccountInfo(role) {
      this.loading = true
      this.error = null
      try {
        let res
        this.accountInfo = { ...initInfo }
        if (role === 'actor') {
          // 演员信息接口
          res = await get('/PrActors/GetByUnionId')
        } else if (role === 'staff') {
          // 工作人员信息接口
          res = await get('/PrPerson/GetByUnionId')
        } else {
          throw new Error('不支持的角色类型')
        }

        if (res.status && res.data && res.data.dataList) {
          // 处理响应数据：兼容不同的响应格式
          const rawData = res.data.dataList || res.data
          const processedData = { ...rawData }

          // 处理银行信息：从bankInfos数组中取出第一个银行信息
          if (processedData.bankInfos && processedData.bankInfos.length > 0) {
            const firstBankInfo = processedData.bankInfos[0]
            processedData.bankName = firstBankInfo.bankName || ''
            processedData.accountName = firstBankInfo.accountName || ''
            processedData.accountNumber = firstBankInfo.accountNumber || ''
          } else {
            // 如果没有银行信息，设置默认值
            processedData.bankName = ''
            processedData.accountName = ''
            processedData.accountNumber = ''
          }
          if (role === 'staff') {
            processedData.personId = processedData.id
          }

          // 更新状态
          this.accountInfo = { ...this.accountInfo, ...processedData }
          return res
        } else {

          this.error = res.message || '获取账户信息失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 重置账户信息
    resetAccountInfo() {
      this.accountInfo = { ...initInfo }
      this.error = null
    },

    // 查询实名状态
    async checkRealNameStatus(role) {
      try {
        // 根据角色确定 personType 参数：0人员，1演员
        const personType = role === 'actor' ? 1 : 0
        const res = await get('/PrPerson/ValidRealNameByUnionId', { personType })
        if (res.status) {
          // ValidResult：0信息不全，1已实名，2未实名
          return {
            status: true,
            validResult: res.data?.validResult || 0,
            url: res.data?.url || '',
            message: res.message || ''
          }
        } else {
          return {
            status: false,
            validResult: 0,
            message: res.message || '查询实名状态失败'
          }
        }
      } catch (error) {
        return {
          status: false,
          validResult: 0,
          message: error.message || '网络错误'
        }
      }
    },

    // 清除错误信息
    clearError() {
      this.error = null
    }
  },
})
