import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
  id: 'venue',
  state: () => ({
    // 场地列表
    venueList: [],
    // 所有场地选项列表
    allVenueOptions: [],
    // 场地媒体分类映射
    venueClassMap: {},
    // 加载状态
    loading: false,
    // 错误信息
    error: null,
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    // 搜索参数
    searchParams: {
      venueName: '',
    },
  }),
  persist: false,
  getters: {
    // 获取场地列表
    getVenueList: state => state.venueList,

    // 获取场地总数
    getVenueCount: state => (state.venueList || []).length,

    // 获取有效场地列表（排除已删除的）
    getValidVenueList: state => {
      return (state.venueList || []).filter(venue => !venue.isDelete)
    },

    // 获取所有场地选项
    getAllVenueOptions: state => state.allVenueOptions,

    // 获取分页信息
    getPagination: state => state.pagination,

    // 获取搜索参数
    getSearchParams: state => state.searchParams,

    // 获取场地媒体分类映射
    getVenueClassMap: state => state.venueClassMap,
  },
  actions: {
    // 设置搜索参数
    setSearchParams(params) {
      this.searchParams = { ...this.searchParams, ...params }
    },

    // 重置搜索参数
    resetSearchParams() {
      this.searchParams = {
        venueName: '',
      }
    },

    // 获取场地列表
    async fetchVenueList(params = {}) {
      this.loading = true
      this.error = null
      try {
        // 合并搜索参数和分页参数
        const searchParams = {
          pageIndex: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.searchParams,
          ...params,
        }

        const res = await post('/PrVenue/GetList', searchParams)
        if (res.status && res.data) {
          const pageIndex = this.pagination.current
          const pageSize = this.pagination.pageSize
          const { dataList = [], totalCount = 0 } = res.data

          // 判断是否为第一页（重新搜索）还是加载更多
          const isFirstPage = pageIndex === 1
          const newDataList = Array.isArray(dataList) ? dataList : []

          if (isFirstPage) {
            // 第一页：直接替换数据
            this.venueList = newDataList
          } else {
            // 加载更多：追加数据
            this.venueList = [...(this.venueList || []), ...newDataList]
          }

          this.pagination = {
            current: pageIndex,
            pageSize: pageSize,
            total: totalCount,
          }

          return {
            status: true,
            data: {
              list: this.venueList, // 返回完整的列表（包含之前加载的数据）
              currentPageList: newDataList, // 当前页的数据
              total: totalCount,
              pageIndex,
              pageSize,
              isFirstPage,
            },
            message: res.message || '获取场地列表成功',
          }
        } else {
          this.error = res.message || '获取场地列表失败'
          return {
            status: false,
            data: null,
            message: this.error,
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return {
          status: false,
          data: null,
          message: this.error,
        }
      } finally {
        this.loading = false
      }
    },

    // 保存场地信息
    async saveVenue(venueData) {
      try {
        // 处理数据：photos是单张封面图，videos是多张视频
        const submitData = {
          ...venueData,
          // photos字段现在是单张封面图，直接使用
          photos: venueData.photos || '',
          // videos字段保持原有逻辑，支持多张视频
          videos: Array.isArray(venueData.videoList) ? venueData.videoList.join(',') : venueData.videos,
        }

        const res = await post('/PrVenue/Save', submitData)

        if (res.status) {
          return {
            status: true,
            data: res.data || true,
            message: res.message || '保存场地信息成功',
          }
        } else {
          return {
            status: false,
            data: null,
            message: res.message || '保存场地信息失败',
          }
        }
      } catch (error) {
        return {
          status: false,
          data: null,
          message: error.message || '网络错误',
        }
      }
    },

    // 删除场地（软删除）
    async deleteVenue(id) {
      try {
        const res = await get(`/PrVenue/Delete?id=${id}`)

        if (res.status) {
          return {
            status: true,
            data: res.data || true,
            message: res.message || '删除场地成功',
          }
        } else {
          return {
            status: false,
            data: null,
            message: res.message || '删除场地失败',
          }
        }
      } catch (error) {
        return {
          status: false,
          data: null,
          message: error.message || '网络错误',
        }
      }
    },

    // 获取所有场地名称(不分页)
    async getAllVenueNames() {
      try {
        const res = await get('/PrVenue/AllName')

        if (res.status && res.data) {
          this.allVenueOptions = Array.isArray(res.data) ? res.data : []
          return {
            status: true,
            data: this.allVenueOptions,
            message: res.message || '获取场地名称成功',
          }
        } else {
          this.allVenueOptions = []
          return {
            status: false,
            data: [],
            message: res.message || '获取场地名称失败',
          }
        }
      } catch (error) {
        this.allVenueOptions = []
        return {
          status: false,
          data: [],
          message: error.message || '网络错误',
        }
      }
    },

    // 根据ID获取场地详情
    async getVenueById(venueId) {
      try {
        const res = await get('/PrVenue/GetVenueById', { id: venueId })

        if (res.status && res.data?.dataList) {
          return {
            status: true,
            data: res.data?.dataList,
            message: res.message || '获取场地详情成功',
          }
        } else {
          return {
            status: false,
            data: null,
            message: res.message || '获取场地详情失败',
          }
        }
      } catch (error) {
        return {
          status: false,
          data: null,
          message: error.message || '网络错误',
        }
      }
    },

    // 获取场地媒体列表
    async getVenueMediaByVenueId(venueId) {
      try {
        const res = await get('/PrVenue/GetMediaByVenueId', { id: venueId })

        if (res.status && res.data) {
          const mediaList = res.data.dataList || []

          // 更新场地媒体分类映射
          const newVenueClassMap = { ...this.venueClassMap }
          newVenueClassMap[venueId] = mediaList
            .filter(item => !!item?.subclass)
            .map(item => item?.subclass || '')
          this.venueClassMap = newVenueClassMap

          return {
            status: true,
            data: mediaList,
            message: res.message || '获取场地媒体成功',
          }
        } else {
          return {
            status: false,
            data: [],
            message: res.message || '获取场地媒体失败',
          }
        }
      } catch (error) {
        return {
          status: false,
          data: [],
          message: error.message || '网络错误',
        }
      }
    },

    // 保存场地媒体
    async saveVenueMedia(params) {
      try {
        const res = await post('/PrVenue/SaveVenueMedia', params)

        if (res.status) {
          return {
            status: true,
            data: res.data || true,
            message: res.message || '保存场地媒体成功',
          }
        } else {
          return {
            status: false,
            data: null,
            message: res.message || '保存场地媒体失败',
          }
        }
      } catch (error) {
        return {
          status: false,
          data: null,
          message: error.message || '网络错误',
        }
      }
    },

    // 删除场地媒体
    async deleteVenueMedia(id) {
      try {
        const res = await get(`/PrVenue/DeleteVenueMedia?id=${id}`)

        if (res.status && res.data && res.data.status) {
          return {
            status: true,
            data: res.data || true,
            message: res.message || '删除场地媒体成功',
          }
        } else {
          return {
            status: false,
            data: null,
            message: res.message || '删除场地媒体失败',
          }
        }
      } catch (error) {
        return {
          status: false,
          data: null,
          message: error.message || '网络错误',
        }
      }
    },

    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },

    // 重置分页
    resetPagination() {
      this.pagination = {
        current: 1,
        pageSize: 10,
        total: 0,
      }
    },

    // 清空场地列表
    clearVenueList() {
      this.venueList = []
      this.resetPagination()
    },

    // 清空错误信息
    clearError() {
      this.error = null
    },
  },
})