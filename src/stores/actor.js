import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'
let initInfo = {
  // 基础信息
  id: null,
  personId: null,
  personName: '',
  dateOfBirth: null,
  isChildActor: false,
  gender: 1, // 1: 男, 2: 女
  personType: 1, // 1: 个人, 2: 团体
  idNumber: '',
  idCardFrontPhoto: '',
  idCardVersoPhoto: '',
  address: '',
  phone: '',
  eMail: '',

  // 演员特有信息
  stageName: '',
  isInternal: 0, // 0: 否, 1: 是
  height: null,
  weight: null,
  bust: null,
  waist: null,
  hips: null,
  headUrl: '',
  school: '',
  specialty: '',
  actingStyle: '',
  cityName: '',

  // 银行信息
  bankName: '',
  accountName: '',
  accountNumber: '',
  bankInfos: [],

  // 其他信息
  unionId: '',
  fddVerifyStatus: null,
  fddCustomerVerifyUrl: '',
  roleType: null,
  company: '',
  isDelete: false,
  createTime: '',
  updateTime: ''
}
export default defineStore({
  id: 'actor',
  state: () => {
    return {
      actorInfo: { ...initInfo },
      loading: false,
      error: null
    }
  },
  persist: true,
  actions: {
    // 根据UnionId获取演员信息
    async fetchActorInfo() {
      this.loading = true
      this.error = null
      try {
        const res = await get('/PrActors/GetByUnionId')
        if (res.status && res.data && res.data.dataList) {
          // 处理银行信息：从bankInfos数组中取出第一个银行信息
          const processedData = { ...res.data.dataList }
          if (processedData.bankInfos && processedData.bankInfos.length > 0) {
            const firstBankInfo = processedData.bankInfos[0]
            processedData.bankName = firstBankInfo.bankName || ''
            processedData.accountName = firstBankInfo.accountName || ''
            processedData.accountNumber = firstBankInfo.accountNumber || ''
          } else {
            // 如果没有银行信息，设置默认值
            processedData.bankName = ''
            processedData.accountName = ''
            processedData.accountNumber = ''
          }
          let lastData = { ...this.actorInfo, ...processedData }
          this.actorInfo = { ...lastData }
          res.lastData = lastData
          return res
        } else {
          this.actorInfo = { ...initInfo }

          this.error = res.message || '获取演员信息失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 保存演员信息
    async saveActorInfo(actorData) {
      this.loading = true
      this.error = null
      try {
        // 调试输出：确认接收到的数据包含照片字段
        console.log('Store收到的演员数据:', actorData)
        console.log('照片字段检查:', {
          idCardFrontPhoto: actorData.idCardFrontPhoto,
          idCardVersoPhoto: actorData.idCardVersoPhoto,
        })

        // 处理银行信息：将根级别的银行信息格式化为bankInfos数组
        const processedData = { ...actorData, id: actorData.id || 0, personId: actorData.personId || 0 }

        // 如果有银行信息，将其格式化为数组
        if (processedData.bankName || processedData.accountName || processedData.accountNumber) {
          processedData.bankInfos = [{
            bankName: processedData.bankName || '',
            accountName: processedData.accountName || '',
            accountNumber: processedData.accountNumber || ''
          }]
        } else {
          processedData.bankInfos = []
        }

        // 移除根级别的银行信息字段，避免重复
        delete processedData.bankName
        delete processedData.accountName
        delete processedData.accountNumber

        // 调试输出：确认发送到API的最终数据
        console.log('发送到API的最终数据:', processedData)
        console.log('最终数据中的照片字段:', {
          idCardFrontPhoto: processedData.idCardFrontPhoto,
          idCardVersoPhoto: processedData.idCardVersoPhoto,
        })

        const res = await post('/PrActors/SaveActorByUnionId', processedData)
        if (res.status) {
          // 保存成功后更新本地状态
          this.actorInfo = { ...this.actorInfo, ...actorData }
          return res
        } else {
          this.error = res.message || '保存演员信息失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 重置演员信息
    resetActorInfo() {
      this.actorInfo = {
        ...initInfo
      }
      this.error = null
    },

    // 清除错误信息
    clearError() {
      this.error = null
    }
  }
})
