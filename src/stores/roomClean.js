import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
    id: 'roomClean',
    state: () => {
        return {
            communityList: [],
            loading: false,
            error: null,
            updateLoading: false
        }
    },
    persist: false,
    getters: {
        // 待打扫的房间列表（status = 0）
        pendingCleanList: (state) => {
            let result = state.communityList.map(community => ({
                ...community,
                buildings: community.buildings.map(building => ({
                    ...building,
                    rooms: building.rooms.filter(room => room.roomClean?.status === 0)
                })).filter(building => building.rooms.length > 0)
            })).filter(community => community.buildings.length > 0)
            return result
        },

        // 打扫中的房间列表（status = 1）
        cleaningList: (state) => {
            return state.communityList.map(community => ({
                ...community,
                buildings: community.buildings.map(building => ({
                    ...building,
                    rooms: building.rooms.filter(room => room.roomClean?.status === 1)
                })).filter(building => building.rooms.length > 0)
            })).filter(community => community.buildings.length > 0)
        }
    },
    actions: {
        // 获取待保洁房间列表
        async fetchRoomCleanList() {
            this.loading = true
            this.error = null
            try {
                const res = await get('/PrDormRoom/GetWxRoomClean')
                if (res.status && res.data) {
                    // 格式化数据：小区-楼栋-房间 三层结构
                    this.communityList = this.formatCommunityData(res.data?.dataList || [])
                    return res
                } else {
                    this.error = res.message || '获取失败'
                    return res
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return { status: false, message: this.error }
            } finally {
                this.loading = false
            }
        },

        // 格式化小区数据：将房间按楼栋分组
        formatCommunityData(rawData) {
            if (!rawData) return []
            return rawData.map(community => {
                const formattedCommunity = {
                    ...community,
                    buildings: []
                }

                // 按楼栋号分组房间
                const buildingMap = new Map()

                if (community.roomInfos && Array.isArray(community.roomInfos)) {
                    community.roomInfos.forEach(room => {
                        const buildingNumber = room.buildingNumber || 0

                        if (!buildingMap.has(buildingNumber)) {
                            buildingMap.set(buildingNumber, {
                                buildingNumber: buildingNumber,
                                communityId: community.id,
                                communityName: community.communityName,
                                rooms: []
                            })
                        }

                        buildingMap.get(buildingNumber).rooms.push(room)
                    })
                }

                // 转换为数组并排序
                formattedCommunity.buildings = Array.from(buildingMap.values())
                    .sort((a, b) => a.buildingNumber - b.buildingNumber)

                // 删除原来的 roomInfos，因为现在房间信息在 buildings 中
                delete formattedCommunity.roomInfos

                return formattedCommunity
            })
        },

        // 更新房间保洁状态
        async updateCleanStatus(roomCleanId, status) {
            this.updateLoading = true
            this.error = null
            try {
                const res = await post('/PrDormRoom/UpdateCleanStatus', null, {
                    params: { id: roomCleanId, status: status }
                })
                if (res.status) {
                    // 刷新数据
                    await this.fetchRoomCleanList()
                }
                return res
            } catch (error) {
                this.error = error.message || '网络错误'
                return { status: false, message: this.error }
            } finally {
                this.updateLoading = false
            }
        }
    }
})