import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
    id: 'todaySceneVerify',
    state: () => {
        return {
            todaySceneData: null, // 当日销场数据
            loading: false, // 加载状态
            error: null, // 错误信息
            verifyLoading: false, // 销场操作加载状态
            scenePlanList: [], // 大计划场次列表
            planLoading: false, // 大计划加载状态
            addScenesLoading: false, // 批量添加场次加载状态
        }
    },
    persist: false,
    getters: {
        // 获取当日销场数据
        getTodaySceneData: (state) => state.todaySceneData,

        // 获取当日场景列表
        getTodayScenes: (state) => state.todaySceneData?.sceneInfos || [],

        // 检查是否有数据
        hasSceneData: (state) => state.todaySceneData !== null,

        // 检查是否有场景数据
        hasScenes: (state) => {
            const scenes = state.todaySceneData?.sceneInfos || []
            return scenes.length > 0
        },

        // 获取场次统计信息
        getSceneStats: (state) => {
            const scenes = state.todaySceneData?.sceneInfos || []
            const shootableScenes = scenes.filter(scene => scene.planType === 0)
            const verifiedScenes = shootableScenes.filter(scene => scene.verification)

            return {
                total: shootableScenes.length,
                verified: verifiedScenes.length,
                unverified: shootableScenes.length - verifiedScenes.length,
                verificationRate: shootableScenes.length > 0
                    ? Math.round((verifiedScenes.length / shootableScenes.length) * 100)
                    : 0
            }
        },

        // 获取加载状态
        isLoading: (state) => state.loading,

        // 获取销场操作加载状态
        isVerifyLoading: (state) => state.verifyLoading,

        // 获取错误信息
        getError: (state) => state.error,

        // 获取大计划场次列表
        getScenePlanList: (state) => state.scenePlanList,

        // 获取大计划加载状态
        isPlanLoading: (state) => state.planLoading,

        // 获取批量添加场次加载状态
        isAddScenesLoading: (state) => state.addScenesLoading,

        // 获取可选择的场次（只包含场次明细，排除已存在的场次）
        getSelectableScenes: (state) => {
            const existingSceneNumbers = new Set(
                (state.todaySceneData?.sceneInfos || [])
                    .filter(scene => scene.planType === 0)
                    .map(scene => scene.sceneNumber)
            )

            return state.scenePlanList.filter(scene =>
                scene.planType === 0 && !existingSceneNumbers.has(scene.sceneNumber)
            )
        },
    },

    actions: {
        // 获取当日销场数据
        async fetchTodaySceneData(productionId, personType) {
            this.loading = true
            this.error = null
            try {
                const res = await get('/PrProductions/GetWxSingleSceneCall', {
                    productionId,
                    personType
                })

                if (res.status && res.data?.dataList) {
                    this.todaySceneData = res.data.dataList
                    // 添加天气信息
                    this.todaySceneData.weatherStr = res.data.weatherStr
                    return {
                        status: true,
                        data: this.todaySceneData,
                        message: res.message || '获取当日销场数据成功'
                    }
                } else {
                    this.error = res.message || '获取当日销场数据失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.loading = false
            }
        },

        // 销场操作 - 与 sceneCall 保持一致的逻辑
        async saveSceneVerification(personType, verification) {
            this.verifyLoading = true
            this.error = null
            try {
                const res = await post('/PrProductions/SaveWxSceneVerification', {
                    personType,
                    verification
                })

                if (res.status) {
                    return {
                        status: true,
                        data: res.data,
                        message: res.message || '销场成功'
                    }
                } else {
                    this.error = res.message || '销场失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.verifyLoading = false
            }
        },

        // 删除销场记录 - 与 sceneCall 保持一致的逻辑
        async deleteSceneVerification(verificationId, productionId, personType) {
            this.verifyLoading = true
            this.error = null
            try {
                const res = await get('/PrProductions/DeleteWxSceneVerification', {
                    id: verificationId,
                    productionId,
                    personType
                })

                if (res.status) {
                    return {
                        status: true,
                        data: res.data,
                        message: res.message || '删除销场记录成功'
                    }
                } else {
                    this.error = res.message || '删除销场记录失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.verifyLoading = false
            }
        },

        // 删除场次
        async deleteScene(sceneId, productionId, personType) {
            this.verifyLoading = true
            try {
                const res = await get('/PrProductions/DeleteWxSceneCallInfo', {
                    id: sceneId,
                })

                if (res.status) {
                    // 删除场次成功后重新获取数据
                    await this.fetchTodaySceneData(productionId, personType)
                    return {
                        status: true,
                        message: res.message || '删除场次成功'
                    }
                } else {
                    return {
                        status: false,
                        message: res.message || '删除场次失败'
                    }
                }
            } catch (error) {
                return {
                    status: false,
                    message: error.message || '删除场次操作失败'
                }
            } finally {
                this.verifyLoading = false
            }
        },

        // 重置数据
        resetTodaySceneData() {
            this.todaySceneData = null
            this.error = null
        },

        // 清除错误
        clearError() {
            this.error = null
        },

        // 获取大计划场次列表
        async fetchScenePlan(productionId) {
            this.planLoading = true
            this.error = null
            try {
                const res = await get('/PrProductions/GetScenePlan', {
                    productionId
                })

                if (res.status && res.data?.dataList) {
                    // 如果返回的是数组，直接使用；如果是对象，取其中的数组字段
                    this.scenePlanList = res.data?.dataList
                    return {
                        status: true,
                        data: this.scenePlanList,
                        message: res.message || '获取大计划成功'
                    }
                } else {
                    this.error = res.message || '获取大计划失败'
                    return {
                        status: false,
                        data: null,
                        message: this.error
                    }
                }
            } catch (error) {
                this.error = error.message || '网络错误'
                return {
                    status: false,
                    data: null,
                    message: this.error
                }
            } finally {
                this.planLoading = false
            }
        },

        // 批量添加场次到通告单（支持添加和排序修改）
        async addScenesToCall(callId, scenes, isUpdate = false) {
            this.addScenesLoading = true
            try {
                // 构造保存数据
                const infos = scenes.map((scene, index) => {
                    const sceneData = {
                        callId: callId,
                        sort: scene.sort !== undefined ? scene.sort : (index + 1),
                        planType: scene.planType,
                        sceneNumber: scene.sceneNumber,
                        atmosphere: scene.atmosphere,
                        locationType: scene.locationType,
                        pageNumber: scene.pageNumber,
                        lineNumber: scene.lineNumber,
                        shootingLocation: scene.shootingLocation,
                        scriptLocation: scene.scriptLocation,
                        scene: scene.scene,
                        mainContent: scene.mainContent,
                        costumeMakeupTip: scene.costumeMakeupTip,
                        mainActors: scene.mainActors,
                        groupExtraActors: scene.groupExtraActors,
                        specialActors: scene.specialActors,
                        remark: scene.remark,
                        consultation: scene.consultation || '',
                    }

                    // 如果场次已存在（有id），则包含id字段用于更新
                    if (scene.id) {
                        sceneData.id = scene.id
                    }

                    return sceneData
                })

                const res = await post('/PrProductions/AddWxSceneCallInfo', {
                    callId: callId,
                    infos: infos
                })

                if (res.status) {
                    const operationType = isUpdate ? '更新' : '添加'
                    return {
                        status: true,
                        message: res.message || `批量${operationType}场次成功`
                    }
                } else {
                    const operationType = isUpdate ? '更新' : '添加'
                    return {
                        status: false,
                        message: res.message || `批量${operationType}场次失败`
                    }
                }
            } catch (error) {
                const operationType = isUpdate ? '更新' : '添加'
                return {
                    status: false,
                    message: error.message || `批量${operationType}场次操作失败`
                }
            } finally {
                this.addScenesLoading = false
            }
        },

        // 重置大计划数据
        resetScenePlan() {
            this.scenePlanList = []
            this.error = null
        }
    }
})
