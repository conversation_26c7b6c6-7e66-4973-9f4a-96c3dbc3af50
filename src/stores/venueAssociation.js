import { defineStore } from 'pinia'
import { get as requestGet, post } from '@/utils/request'

export default defineStore('venueAssociation', {
  state: () => ({
    // 项目列表
    projectList: [],
    // 场景数据（原始一维数据）
    sceneData: [],
    // 处理后的场景数据（二维结构：项目ID -> 主场景 -> 子场景数组）
    processedSceneData: {},
    // 加载状态
    loading: false,
    // 错误信息
    error: null,
    // 关联操作加载状态
    associating: false,
  }),
  persist: false,
  getters: {
    // 获取项目列表
    getProjectList: state => state.projectList,

    // 获取项目总数
    getProjectCount: state => (state.projectList || []).length,

    // 获取指定项目的场景数据
    getProjectScenes: state => (projectId) => {
      return state.processedSceneData[projectId] || {}
    },

    // 获取指定项目的主场景列表
    getMainScenes: state => (projectId) => {
      const scenes = state.processedSceneData[projectId] || {}
      return Object.keys(scenes)
    },

    // 获取指定项目和主场景的子场景列表
    getSubScenes: state => (projectId, mainScene) => {
      const scenes = state.processedSceneData[projectId] || {}
      return scenes[mainScene] || []
    },

    // 检查是否有项目数据
    hasProjects: state => {
      return state.projectList && state.projectList.length > 0
    },

    // 检查指定项目是否有场景数据
    hasScenes: state => (projectId) => {
      const scenes = state.processedSceneData[projectId] || {}
      return Object.keys(scenes).length > 0
    },
  },
  actions: {
    // 获取项目列表
    async fetchProjectList(personType) {
      this.loading = true
      this.error = null
      try {
        const res = await requestGet('/PrProductions/WxProductions', { isAll: true, personType })

        if (res.status && res.data?.dataList) {
          this.projectList = res.data?.dataList || []
          return {
            status: true,
            data: this.projectList,
            message: res.message || '获取项目列表成功',
          }
        } else {
          this.error = res.message || '获取项目列表失败'
          this.projectList = []
          return {
            status: false,
            data: [],
            message: this.error,
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        this.projectList = []
        return {
          status: false,
          data: [],
          message: this.error,
        }
      } finally {
        this.loading = false
      }
    },

    // 获取项目场景数据
    async fetchProjectScenes(productionId) {
      this.loading = true
      this.error = null
      try {
        const res = await requestGet('/PrProductions/GetScenePlanVenue', { productionId })

        if (res.status && res.data?.dataList) {
          const sceneList = res.data.dataList || []
          this.sceneData = sceneList

          // 处理成二维数据结构
          this.processSceneData(productionId, sceneList)

          return {
            status: true,
            data: sceneList,
            message: res.message || '获取场景数据成功',
          }
        } else {
          this.error = res.message || '获取场景数据失败'
          return {
            status: false,
            data: [],
            message: this.error,
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return {
          status: false,
          data: [],
          message: this.error,
        }
      } finally {
        this.loading = false
      }
    },

    // 处理场景数据为二维结构
    processSceneData(productionId, scenes) {
      const processed = {}

      scenes.forEach(scene => {
        const mainVenue = scene.mainVenue || '默认主场景'
        const venue = scene.venue || '默认子场景'

        if (!processed[mainVenue]) {
          processed[mainVenue] = []
        }

        // 避免重复添加相同的子场景
        if (!processed[mainVenue].includes(venue)) {
          processed[mainVenue].push(venue)
        }
      })

      // 更新处理后的场景数据
      this.processedSceneData[productionId] = processed
    },

    // 保存场地关联信息
    async saveVenueAssociation(associationData) {
      this.associating = true
      this.error = null
      try {
        const res = await post('/PrVenue/SaveProductionVenueInfo', associationData)

        if (res.status) {
          return {
            status: true,
            data: res.data || true,
            message: res.message || '关联成功',
          }
        } else {
          this.error = res.message || '关联失败'
          return {
            status: false,
            data: null,
            message: this.error,
          }
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return {
          status: false,
          data: null,
          message: this.error,
        }
      } finally {
        this.associating = false
      }
    },

    // 重置项目列表
    resetProjectList() {
      this.projectList = []
    },

    // 重置场景数据
    resetSceneData() {
      this.sceneData = []
      this.processedSceneData = {}
    },

    // 重置所有数据
    resetAll() {
      this.projectList = []
      this.sceneData = []
      this.processedSceneData = {}
      this.error = null
    },

    // 清除错误信息
    clearError() {
      this.error = null
    },

    // 根据项目ID获取项目信息
    getProjectById(projectId) {
      return this.projectList.find(project => project.id === projectId) || null
    },

    // 检查项目是否已加载场景数据
    hasLoadedScenes(projectId) {
      return !!this.processedSceneData[projectId]
    },
  },
})
