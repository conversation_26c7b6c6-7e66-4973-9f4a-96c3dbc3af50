import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'
const initStaffInfo = {
  // 基础信息
  id: null,
  personName: '',
  jobNumber: '',
  dateOfBirth: null,
  isChildActor: false,
  gender: 1, // 1: 男, 2: 女
  personType: 1, // 1: 个人, 2: 团体
  idNumber: '',
  idCardFrontPhoto: '',
  idCardVersoPhoto: '',
  address: '',
  phone: '',
  eMail: '',
  roleType: null,
  company: '',
  isInternal: 0, // 0: 否, 1: 是

  // 银行信息
  bankName: '',
  accountName: '',
  accountNumber: '',
  bankInfos: [],

  // 其他信息
  unionId: '',
  fddVerifyStatus: null,
  fddCustomerVerifyUrl: '',
  isDelete: false,
  createTime: '',
  updateTime: '',
  creator: '',
  lastModifier: ''
}
export default defineStore({
  id: 'staff',
  state: () => {
    return {
      staffInfo: { ...initStaffInfo },
      loading: false,
      error: null
    }
  },
  persist: true,
  actions: {
    // 根据UnionId获取工作人员信息
    async fetchStaffInfo() {
      this.loading = true
      this.error = null
      try {
        const res = await get('/PrPerson/GetByUnionId')
        if (res.status && res.data && res.data.dataList) {
          // 处理银行信息：从bankInfos数组中取出第一个银行信息
          // 兼容不同的响应格式：可能是 res.data 或 res.data.dataList
          const rawData = res.data.dataList
          const processedData = { ...rawData }
          if (processedData.bankInfos && processedData.bankInfos.length > 0) {
            const firstBankInfo = processedData.bankInfos[0]
            processedData.bankName = firstBankInfo.bankName || ''
            processedData.accountName = firstBankInfo.accountName || ''
            processedData.accountNumber = firstBankInfo.accountNumber || ''
          } else {
            // 如果没有银行信息，设置默认值
            processedData.bankName = ''
            processedData.accountName = ''
            processedData.accountNumber = ''
          }
          let lastData = { ...this.staffInfo, ...processedData }
          this.staffInfo = { ...lastData }
          res.lastData = lastData
          return res
        } else {
          this.staffInfo = { ...initStaffInfo }
          this.error = res.message || '获取工作人员信息失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 保存工作人员信息
    async saveStaffInfo(staffData) {
      this.loading = true
      this.error = null
      try {
        // 处理银行信息：将根级别的银行信息格式化为bankInfos数组
        const processedData = { ...staffData, id: staffData.id || 0 }

        // 如果有银行信息，将其格式化为数组
        if (processedData.bankName || processedData.accountName || processedData.accountNumber) {
          processedData.bankInfos = [{
            bankName: processedData.bankName || '',
            accountName: processedData.accountName || '',
            accountNumber: processedData.accountNumber || ''
          }]
        } else {
          processedData.bankInfos = []
        }

        // 移除根级别的银行信息字段，避免重复
        delete processedData.bankName
        delete processedData.accountName
        delete processedData.accountNumber

        const res = await post('/PrPerson/SaveByUnionId', processedData)
        if (res.status) {
          // 保存成功后更新本地状态
          this.staffInfo = { ...this.staffInfo, ...staffData }
          return res
        } else {
          this.error = res.message || '保存工作人员信息失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 重置工作人员信息
    resetStaffInfo() {
      this.staffInfo = {
        ...initStaffInfo
      }
      this.error = null
    },

    // 清除错误信息
    clearError() {
      this.error = null
    }
  }
})
