import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
  id: 'login',
  state: () => {
    return {
      userInfo: {}, // 用户基本信息（不包含敏感信息如accessToken）
      currentRole: '', // 当前选中的角色枚举值 (actor, staff, scout)
    }
  },
  persist: true,
  getters: {
    // 获取用户信息（过滤敏感信息）
    getUserInfo: (state) => {
      const { accessToken, ...userInfo } = state.userInfo
      return userInfo
    },
    // 检查是否已登录
    isLoggedIn: (state) => {
      return !!state.userInfo?.account && !!localStorage.getItem('token')
    },
    // 获取用户账号
    getUserAccount: (state) => {
      return state.userInfo?.account || ''
    },
    // 获取用户ID
    getUserId: (state) => {
      return state.userInfo?.userId || ''
    },
    // 获取当前角色
    getCurrentRole: (state) => {
      return state.currentRole
    },
    // 获取角色信息
    getRoleInfo: (state) => {
      const roleMap = {
        actor: { name: '演员', code: 'ACTOR' },
        staff: { name: '工作人员', code: 'STAFF' },
        scout: { name: '勘探人员', code: 'SCOUT' }
      }
      return roleMap[state.currentRole] || { name: '未选择角色', code: '' }
    },
    // 获取当前用户的人员类型（用于API调用）
    getCurrentPersonType: (state) => {
      return state.currentRole === 'actor' ? 1 : 0 // 1演员，0人员
    }
  },
  actions: {
    // 初始化用户信息（从localStorage恢复）
    initUserInfo() {
      const storedUserInfo = localStorage.getItem('USER_INFO')
      if (storedUserInfo) {
        try {
          const parsedUserInfo = JSON.parse(storedUserInfo)
          // 过滤掉accessToken，只保存到store中
          const { accessToken, ...userInfo } = parsedUserInfo
          this.userInfo = userInfo
        } catch (error) {
          console.error('解析用户信息失败:', error)
          this.userInfo = {}
        }
      }
    },

    // 设置用户信息
    setUserInfo(userData) {
      // 过滤掉accessToken，只保存到store中
      const { accessToken, ...userInfo } = userData
      this.userInfo = userInfo

      // localStorage中保存完整信息（包含accessToken）
      localStorage.setItem('USER_INFO', JSON.stringify(userData))

      // 单独保存token和userId
      if (accessToken) {
        localStorage.setItem('token', accessToken)
      }
      if (userData.userId) {
        localStorage.setItem('userId', userData.userId)
      }
    },

    // 设置当前角色
    setCurrentRole(role) {
      this.currentRole = role
      // 同时保存到sessionStorage
      sessionStorage.setItem('selectedRole', role)
    },

    // 初始化角色信息（从sessionStorage恢复）
    initRole() {
      const storedRole = sessionStorage.getItem('selectedRole')
      if (storedRole) {
        this.currentRole = storedRole
      }
    },

    // 清除角色信息
    clearRole() {
      this.currentRole = ''
      sessionStorage.removeItem('selectedRole')
    },


    // 获取执行登录
    async fetchLogin(params) {
      const res = await get('/Home/PersonLogin', params).catch(() => { })
      if (res.status && res.data) {
        // 使用新的setUserInfo方法
        this.setUserInfo(res.data)
      }
      return res
    },

    // 发送邮箱验证码
    async fetchSendCode(params) {
      const res = await get('/Home/SendCode', params).catch(() => { })

      return res
    },

    // 验证邮箱验证码
    async fetchVerifyCode(params) {
      const res = await get('/Home/VerifyCode', params).catch(() => { })

      return res
    },

    //登出
    loginOut() {
      // 清空store中的用户信息和角色信息
      this.userInfo = {}
      this.currentRole = ''

      // 清空localStorage中的相关信息
      localStorage.removeItem('token')
      localStorage.removeItem('USER_INFO')
      localStorage.removeItem('userId')
      localStorage.removeItem('unionid')

      // 清空sessionStorage中的角色信息
      sessionStorage.removeItem('selectedRole')
    },

    // 微信快捷登录
    async wxLogin(params) {
      const { status } = await get('/Home/WxLogin', params)

      return status
    },

    async fetchReBind(values) {
      const res = await post('/Home/WxBind', values)

      return res?.status
    },

    async uploadImg(params) {
      const res = await post('/PrActors/UploadFile', params)
      console.log('===res', res)
      if (res?.data?.statusCode === 1) {
        return res?.data?.statusMsg
      }
    },
  },
})
