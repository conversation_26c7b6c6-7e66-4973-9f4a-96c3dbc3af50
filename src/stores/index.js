import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
  id: 'index',
  state: () => {
    return {
      data: {},
      cosAuthConfig: {},
    }
  },
  persist: true,
  actions: {
    // 获取初始数据
    async fetchInitData() {
      const { status, data } = await post('', {}).catch(() => { })
      if (!status) {
        return false
      }
      this.data = data
    },
    async getCosAuth() {
      const { data } = await post('api/Cos/QCloudCredential')

      this.cosAuthConfig = data

      if (Object.keys(data).length) {
        return data
      }

      return this.cosAuthConfig
    },
    async bindInvite(code) {
      const { status } = await post('Invitation/CreateInvit', { invitCode: code })

      return status
    },
    async getCSFacebookUrl() {
      const { data } = await get('/Home/GetCSUrl')

      return data?.statusMsg || 'https://www.facebook.com/Mpr.official/'
    },
    async getServiceQRCodeUrl() {
      const { data } = await get('/home/<USER>')

      return data?.statusMsg || ''
    },
  },
})
