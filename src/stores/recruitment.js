import { defineStore } from 'pinia'
import { get, post } from '@/utils/request'

export default defineStore({
  id: 'recruitment',
  state: () => ({
    // 招募项目列表
    projectList: [],
    // 当前选择的项目信息
    selectedProject: null,
    // 加载状态
    loading: false,
    // 错误信息
    error: null,
  }),
  persist: true,
  getters: {
    // 获取当前选择的项目
    getCurrentProject: state => state.selectedProject,

    // 获取招募项目列表（包含衍生数据）
    getProjectList: state => {
      return state.projectList.map(project => ({
        ...project,
        // 添加合并后的招募信息衍生数据
        mergedRecruitmentInfo: state.getMergedRecruitmentInfo(project.personRecruitment || []),
      }))
    },

    // 根据项目状态过滤项目
    getProjectsByStatus: state => status => {
      return state.projectList.filter(project => project.status === status)
    },

    // 合并同类招募信息的数量
    getMergedRecruitmentInfo: () => recruitmentList => {
      if (!recruitmentList || !Array.isArray(recruitmentList)) return []

      // 按角色类型和父类型分组
      const groupedMap = new Map()

      recruitmentList.forEach(recruitment => {
        const key = `${recruitment.roleType}-${recruitment.parentType}`
        if (groupedMap.has(key)) {
          // 如果已存在相同角色类型，累加人数
          const existing = groupedMap.get(key)
          existing.personCount += recruitment.personCount || 0
          existing.recruitmentIds.push(recruitment.id)
        } else {
          // 新的角色类型
          groupedMap.set(key, {
            roleType: recruitment.roleType,
            parentType: recruitment.parentType,
            personCount: recruitment.personCount || 0,
            recruitmentIds: [recruitment.id],
            // 保留第一个招募的其他信息作为代表
            gender: recruitment.gender,
            description: recruitment.description,
            salaryMin: recruitment.salaryMin,
            salaryMax: recruitment.salaryMax,
            salaryType: recruitment.salaryType,
            currency: recruitment.currency,
          })
        }
      })

      return Array.from(groupedMap.values())
    },
  },
  actions: {
    // 获取招募项目列表
    async fetchRecruitmentProjects(personType = 1) {
      this.loading = true
      this.error = null
      try {
        const res = await get('/PrProductions/GetRecruitmentProductions', {
          personType,
        })
        if (res.status && res.data && res.data.dataList) {
          this.projectList = Array.isArray(res.data.dataList)
            ? res.data.dataList
            : [res.data.dataList]
          return res
        } else {
          this.projectList = []
          this.error = res.message || '获取招募项目列表失败'
          return res
        }
      } catch (error) {
        this.projectList = []
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 设置当前选择的项目
    setSelectedProject(project) {
      this.selectedProject = project
    },

    // 清除当前选择的项目
    clearSelectedProject() {
      this.selectedProject = null
    },

    // 项目报名
    async applyForProject(applicationData) {
      this.loading = true
      this.error = null
      try {
        const res = await post('/PrProductions/CreateApplication', applicationData)
        if (res.status) {
          return res
        } else {
          this.error = res.message || '报名失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 获取用户的报名记录
    getUserApplications() {
      if (!this.selectedProject?.personApplication) return []
      // 这里可以根据当前用户过滤报名记录
      return this.selectedProject.personApplication
    },

    // 检查用户是否已报名某个角色
    hasAppliedForRole(roleType, parentType) {
      const applications = this.getUserApplications()
      return applications.some(
        app => app.roleType === roleType && app.parentType === parentType && app.status !== 4 // 排除已取消的报名
      )
    },

    // 检查用户是否已报名某个具体招募
    hasAppliedForRecruitment(recruitmentId) {
      const applications = this.getUserApplications()
      return applications.some(
        app => app.recruitmentId === recruitmentId && app.status !== 4 // 排除已取消的报名
      )
    },

    // 清除错误信息
    clearError() {
      this.error = null
    },

    // 取消报名
    async cancelApplication(applicationId) {
      this.loading = true
      this.error = null
      try {
        const res = await get('/PrProductions/CancelApplicationStatus', {
          id: applicationId,
        })
        if (res.status) {
          return res
        } else {
          this.error = res.message || '取消报名失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 获取已报名项目
    async fetchPersonApplications(personType = 1) {
      this.loading = true
      this.error = null
      try {
        const res = await get('/PrProductions/GetPersonApplication', {
          personType,
        })
        if (res.status && res.data && res.data.dataList) {
          // 这里可以存储已报名的项目列表，如果需要的话
          return res
        } else {
          this.error = res.message || '获取已报名项目失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 根据项目ID获取招募项目详情
    async fetchRecruitmentProjectById(productionId, personType = 1) {
      this.loading = true
      this.error = null
      try {
        const res = await get('/PrProductions/GetRecruitmentProductionsById', {
          productionId,
          personType,
        })
        if (res.status && res.data && res.data.dataList) {
          // 设置为当前选择的项目
          this.selectedProject = res.data.dataList
          return res
        } else {
          this.error = res.message || '获取项目详情失败'
          return res
        }
      } catch (error) {
        this.error = error.message || '网络错误'
        return { status: false, message: this.error }
      } finally {
        this.loading = false
      }
    },

    // 重置store状态
    resetState() {
      this.projectList = []
      this.selectedProject = null
      this.loading = false
      this.error = null
    },
  },
})
