import { defineStore } from 'pinia'
import { post, get } from '@/utils/request'
import useLoginStore from './login'

// 演员附加资料相关的store
const useActorProfileStore = defineStore('actorProfile', {
  state: () => ({
    loading: false,
    actorInfo: null,
    mediaInfos: [],
    workInfos: []
  }),
  persist: false,
  getters: {
    // 根据媒体类型获取媒体列表
    getMediaByType: (state) => (mediaType) => {
      return state.mediaInfos.filter(media => media.mediaType === mediaType)
    },

    // 获取排序后的作品列表
    getSortedWorks: (state) => {
      return [...state.workInfos].sort((a, b) => (a.sort || 0) - (b.sort || 0))
    }
  },

  actions: {
    // 获取演员详细信息（包含媒体和作品信息）
    async fetchActorProfile() {
      this.loading = true
      try {
        const loginStore = useLoginStore()
        const unionId = loginStore.getUserInfo?.unionId

        if (!unionId) {
          return { status: false, message: '用户信息不完整' }
        }

        const response = await get('/PrActors/GetByUnionId', { unionId })
        if (response.status && response.data?.dataList) {
          const actorData = response.data.dataList

          // 更新store状态
          this.actorInfo = actorData
          this.mediaInfos = actorData.mediaInfos || []
          this.workInfos = actorData.workInfos || []

          return { status: true, data: actorData }
        } else {
          return { status: false, message: response.message || '获取演员信息失败' }
        }
      } catch (error) {
        console.error('获取演员资料失败:', error)
        return { status: false, message: '网络错误，请重试' }
      } finally {
        this.loading = false
      }
    },

    // 保存演员媒体信息
    async saveActorMedia(mediaData) {
      this.loading = true
      try {
        if (!this.actorInfo?.id) {
          return { status: false, message: '演员信息不完整' }
        }

        const requestData = {
          actorId: this.actorInfo.id,
          actorMediaInfos: [{
            actorId: this.actorInfo.id,
            mediaType: mediaData.mediaType,
            mediaUrl: mediaData.mediaUrl,
            description: mediaData.description || '',
            label: mediaData.label || ''
          }]
        }

        const response = await post('/PrActors/SaveActorMedia', requestData)

        if (response.status) {
          // 重新获取最新数据
          await this.fetchActorProfile()
          return { status: true }
        } else {
          return { status: false, message: response.message || '保存失败' }
        }
      } catch (error) {
        console.error('保存媒体信息失败:', error)
        return { status: false, message: '网络错误，请重试' }
      } finally {
        this.loading = false
      }
    },

    // 删除演员媒体信息
    async deleteActorMedia(mediaId) {
      this.loading = true
      try {
        const response = await get('/PrActors/DeleteActorMedia', { id: mediaId })

        if (response.status) {
          // 重新获取最新数据
          await this.fetchActorProfile()
          return { status: true }
        } else {
          return { status: false, message: response.message || '删除失败' }
        }
      } catch (error) {
        console.error('删除媒体信息失败:', error)
        return { status: false, message: '网络错误，请重试' }
      } finally {
        this.loading = false
      }
    },

    // 保存演员作品信息
    async saveActorWorks(workData) {
      this.loading = true
      try {
        if (!this.actorInfo?.id) {
          return { status: false, message: '演员信息不完整' }
        }

        const requestData = {
          actorId: this.actorInfo.id,
          actorWorkInfos: [{
            id: workData.id || undefined, // 如果有id则为编辑，否则为新增
            actorId: this.actorInfo.id,
            workName: workData.workName,
            isHit: workData.isHit || false,
            roleType: workData.roleType || '',
            roleDescription: workData.roleDescription || '',
            workType: workData.workType || '',
            sort: workData.sort || 1
          }]
        }

        const response = await post('/PrActors/SaveActorWorks', requestData)

        if (response.status) {
          // 重新获取最新数据
          await this.fetchActorProfile()
          return { status: true }
        } else {
          return { status: false, message: response.message || '保存失败' }
        }
      } catch (error) {
        console.error('保存作品信息失败:', error)
        return { status: false, message: '网络错误，请重试' }
      } finally {
        this.loading = false
      }
    },

    // 删除演员作品信息
    async deleteActorWorks(workId) {
      this.loading = true
      try {
        const response = await get('/PrActors/DeleteActorWorks', { id: workId })

        if (response.status) {
          // 重新获取最新数据
          await this.fetchActorProfile()
          return { status: true }
        } else {
          return { status: false, message: response.message || '删除失败' }
        }
      } catch (error) {
        console.error('删除作品信息失败:', error)
        return { status: false, message: '网络错误，请重试' }
      } finally {
        this.loading = false
      }
    },

    // 清空store数据
    clearData() {
      this.actorInfo = null
      this.mediaInfos = []
      this.workInfos = []
    }
  }
})

export default useActorProfileStore
