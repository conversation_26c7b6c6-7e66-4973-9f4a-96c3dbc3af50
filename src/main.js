import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import { useResponsive, useEnv, useOneClick } from '@fe/base'
import { createClient } from '@fe/seal'
import { Quick } from '@fe/seal/vue'
import { createBridge } from '@fe/bridge'
import createPiniaI18n from '@/locales/i18n'
import {
  ActionSheet,
  Tabbar,
  TabbarItem,
  Dialog,
  Popup,
  Overlay,
  Loading,
  Form,
  Field,
  RadioGroup,
  Radio,
  Button,
  Picker,
  Uploader,
  Icon,
  Toast,
  Tag,
  NavBar,
  Image as VanImage,
  ImagePreview,
  Switch,
  Cell,
  CellGroup,
  Space,
  Skeleton,
  SkeletonTitle,
  SkeletonImage,
  SkeletonAvatar,
  SkeletonParagraph,
  Search,
  DropdownMenu,
  DropdownItem,
  Divider,
  Empty,
  Card,
  Grid, GridItem,
  Tabs,
  Tab,
  SwipeCell,
  FloatingBubble,
  Checkbox,
  CheckboxGroup,
  CollapseItem
} from 'vant'
import App from './App.vue'
import './assets/styles/base.scss'
import 'vant/lib/index.css'
import useI18nStore from './stores/i18n'
import router from './router'
import { LANG_OPTIONS } from './consts'
import { language, getOS } from '@/utils/env'

const appName = 'mpr'
const app = createApp(App)
const pinia = createPinia()
const bridge = createBridge({
  mode: process.env.NODE_ENV,
})
// 解析localStorage.USER_INFO并获取account字段
const userInfo = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
const account = userInfo.account || ''
const COMMON_PARAMS = {
  mode: process.env.NODE_ENV,
  app_name: 'mpr',
  app_lang_id: LANG_OPTIONS.find(item => item.id === language())?.value + '',
  $os: getOS(),
  login_id: account,
  device_lang: navigator.language,
  web_name: 'Mpr',
  server_url: `https://log.51changdu.com/api/projectevent?project=${location.href.includes('mpr.cdreader.com') ? 'mpr' : 'mpr_test'
    }&remark=online`,
}
const seal = createClient({
  options: COMMON_PARAMS,
  sensorsSenderOptions: {
    sdk_url: 'https://g.cdreader.com/sensorsdata.v2.js',
  },
})
pinia.use(createPersistedState({ key: id => `${appName}@${id}` }))
app.use(bridge)
app.use(useOneClick())
app.use(pinia)
app.use(seal)

app.use(new Quick(COMMON_PARAMS))

//vant组件
app.use(ActionSheet)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Popup)
app.use(Dialog)
app.use(Overlay)
app.use(Loading)
app.use(Form)
app.use(Field)
app.use(RadioGroup)
app.use(Radio)
app.use(Button)
app.use(Picker)
app.use(Uploader)
app.use(Icon)
app.use(Toast)
app.use(Tag)
app.use(NavBar)
app.use(VanImage)
app.use(ImagePreview)
app.use(Switch)
app.use(Cell)
app.use(CellGroup)
app.use(Space)
app.use(Skeleton)
app.use(SkeletonTitle)
app.use(SkeletonImage)
app.use(SkeletonAvatar)
app.use(SkeletonParagraph)
app.use(Search)
app.use(DropdownMenu)
app.use(DropdownItem)
app.use(Divider)
app.use(Empty)
app.use(Card)
app.use(Grid)
app.use(GridItem)
app.use(Tabs)
app.use(Tab)
app.use(SwipeCell)
app.use(FloatingBubble)
app.use(Checkbox)
app.use(CheckboxGroup)
app.use(CollapseItem)

// i18n
app.use(createPiniaI18n(useEnv().urlLangCode, {}, useI18nStore()))

app.use(router)
useResponsive()
app.mount('#app')
