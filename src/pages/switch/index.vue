<template>
  <div class="bg-primary">
    <van-nav-bar title="选择角色" :border="false" left-arrow @click-left="handleBack" />
    <div class="wrap">
      <div class="form">
        <div class="role-select-container">
          <div class="header">
            <div class="icon">👤</div>
            <div class="title">{{ $t('select_your_role') }}</div>
            <div class="subtitle">{{ $t('select_role_subtitle') }}</div>
          </div>

          <div class="role-list">
            <div
              class="role-item"
              :class="{ active: selectedRole === 'actor' }"
              @click="selectRole('actor')">
              <div class="role-icon">🎭</div>
              <div class="role-name">{{ $t('role_actor') }}</div>
              <div class="role-desc">{{ $t('role_actor_desc') }}</div>
              <div class="check-icon" v-if="selectedRole === 'actor'">✓</div>
            </div>

            <div
              class="role-item"
              :class="{ active: selectedRole === 'staff' }"
              @click="selectRole('staff')">
              <div class="role-icon">👷</div>
              <div class="role-name">{{ $t('role_staff') }}</div>
              <div class="role-desc">{{ $t('role_staff_desc') }}</div>
              <div class="check-icon" v-if="selectedRole === 'staff'">✓</div>
            </div>

            <!-- 勘探人员选项暂时隐藏 -->
            <!-- <div class="role-item" :class="{ active: selectedRole === 'scout' }" @click="selectRole('scout')">
              <div class="role-icon">🔍</div>
              <div class="role-name">{{ $t('role_scout') }}</div>
              <div class="role-desc">{{ $t('role_scout_desc') }}</div>
              <div class="check-icon" v-if="selectedRole === 'scout'">✓</div>
            </div> -->
          </div>

          <van-button
            type="primary"
            v-one-click
            block
            round
            :disabled="!selectedRole"
            @click="handleConfirm">
            确认选择
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, toRefs } from 'vue'
import useI18nStore from '@/stores/i18n'
import useLoginStore from '@/stores/login'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useLoad } from '@/components/loading/useLoad'

const store = useI18nStore()
const { loaded } = toRefs(store)
const loginStore = useLoginStore()
const router = useRouter()
const { t } = useI18n()
const { loading } = useLoad()

// 选中的角色
const selectedRole = ref('')

// 选择角色
const selectRole = role => {
  selectedRole.value = role
}

// 确认选择
const handleConfirm = async () => {
  if (!selectedRole.value) {
    showToast(t('please_select_role'))
    return
  }

  try {
    loading.value = true

    // 保存角色选择到sessionStorage，只存储枚举值
    sessionStorage.setItem('selectedRole', selectedRole.value)

    // 更新store中的角色信息
    loginStore.setCurrentRole(selectedRole.value)

    // showToast(t('role_selection_success') )

    // 跳转到首页
    router.push({
      name: 'layoutHome',
    })
  } catch (error) {
    console.error('角色选择失败:', error)
    showToast(t('selection_failed_retry'))
  } finally {
    loading.value = false
  }
}

// 返回处理
const handleBack = () => {
  router.back()
}

onMounted(() => {
  // 初始化用户信息（从localStorage恢复到store）
  loginStore.initUserInfo()

  // 检查是否有用户信息，如果没有则跳转到登录页
  if (!loginStore.isLoggedIn) {
    router.push('/login')
    return
  }
})
</script>

<style scoped lang="scss">
.title {
  line-height: 24px;
  text-align: center;
}

.wrap {
  width: 100%;
  background: #fff;
  border-radius: 16px 16px 0px 0px;
  flex: 1;
}

.form {
  background-color: #fff;
  border-radius: 16px;
  padding: 32px 24px;
  position: relative;
}

.role-select-container {
  .header {
    text-align: center;
    margin-bottom: 16px;

    .icon {
      font-size: 24px;
      margin-bottom: 4px;
    }

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin-bottom: 8px;
    }

    .subtitle {
      color: #777;
    }
  }

  .role-list {
    margin-bottom: 32px;

    .role-item {
      position: relative;
      padding: 16px 24px;
      margin-bottom: 16px;
      border: 1px solid #e5e5e5;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        border-color: #ff8124;
        background: linear-gradient(
          135deg,
          rgba(255, 129, 36, 0.1) 0%,
          rgba(255, 107, 0, 0.1) 100%
        );
      }

      .role-icon {
        font-size: 24px;
        text-align: center;
        margin-bottom: 8px;
      }

      .role-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        text-align: center;
        margin-bottom: 10px;
      }

      .role-desc {
        color: #666;
        text-align: center;
        line-height: 1.5;
      }

      .check-icon {
        position: absolute;
        top: 12px;
        right: 12px;
        width: 20px;
        height: 20px;
        background: #ff8124;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
    }
  }
}
</style>
