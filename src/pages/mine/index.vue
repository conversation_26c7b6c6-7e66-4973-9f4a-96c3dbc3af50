<template>
  <div>
    <!-- 简化头部 -->
    <div class="header">
      <van-skeleton avatar avatar-size="54" round title row="1" v-if="!isLoaded" />
      <div v-else class="user-info">
        <div class="avatar"><van-icon size="32px" name="contact-o" /></div>
        <div class="info">
          <div class="name">{{ userInfo?.nickName }}</div>
          <van-tag plain size="large" type="primary">{{ currentRoleInfo.name }}</van-tag>
        </div>
      </div>
    </div>

    <!-- 功能分组 -->
    <van-space direction="vertical" fill :size="16">
      <!-- 个人信息组 -->
      <van-cell-group inset>
        <van-cell title="个人信息" icon="contact-o" is-link @click="goToPersonalInfo" :loading="buttonStates.personal" />
        <van-cell title="实名状态" icon="award-o" is-link :value="realNameStatus.icon + ' ' + realNameStatus.text"
          @click="goToRealName" :loading="buttonStates.realName" />
        <van-cell v-if="currentRole === 'actor' && accountInfo?.personId" title="才艺展示" icon="medal-o" is-link
          @click="goToActorProfile" :loading="buttonStates.actorProfile" />
      </van-cell-group>

      <!-- 项目业务组 -->
      <van-cell-group inset v-if="accountInfo?.personId">
        <van-cell title="我的报名" icon="completed-o" is-link @click="goToApply" :loading="buttonStates.apply" />
        <van-cell title="合同管理" icon="description-o" is-link @click="goToContract" :loading="buttonStates.contract" />
        <van-cell v-if="hasVenueManagementPermission" title="场地管理" icon="shop-o" is-link @click="goToVenueManagement"
          :loading="buttonStates.venue" />
      </van-cell-group>

      <!-- 系统操作组 -->
      <van-cell-group inset>
        <van-cell title="切换角色" icon="exchange" is-link @click="changeRole" :loading="buttonStates.changeRole" />
        <van-cell title="退出登录" icon="revoke" is-link @click="confirmLogout" :loading="buttonStates.logout" />
      </van-cell-group>
    </van-space>
    <van-action-sheet v-model:show="showLogoutDialog" :actions="actions" cancel-text="取消" close-on-click-action
      description="退出后不会删除任何历史数据，下次登录继续使用本账号。" @cancel="showLogoutDialog = false" @select="logout" />
  </div>
</template>

<script setup>
import { onMounted, computed, ref, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'
import { showToast } from 'vant'
import { IS_IOS, VENUE_MANAGEMENT_ALLOWED_ROLES } from '@/consts/index'

const router = useRouter()
const { t } = useI18n()
const loginStore = useLoginStore()
const homeStore = useHomeStore()

// 页面加载状态
const isLoaded = ref(false)
const showLogoutDialog = ref(false)

// 按钮加载状态
const buttonStates = reactive({
  personal: false,
  realName: false,
  actorProfile: false,
  recruitment: false,
  apply: false,
  contract: false,
  venue: false,
  changeRole: false,
  logout: false,
})

const actions = [{ name: '退出登录', color: '#ff6600' }]

// 从store获取用户信息
const userInfo = computed(() => loginStore.getUserInfo)

// 从store获取当前角色
const currentRole = computed(() => loginStore.getCurrentRole)

// 从store获取当前角色信息
const currentRoleInfo = computed(() => loginStore.getRoleInfo)

// 从store获取账户信息
const accountInfo = computed(() => homeStore.accountInfo)

// 获取实名状态信息
const realNameStatus = computed(() => {
  const fddVerifyStatus = accountInfo.value?.fddVerifyStatus
  switch (fddVerifyStatus) {
    case 0:
      return { text: '未激活', icon: '⚪' }
    case 1:
      return { text: '未认证', icon: '❌' }
    case 2:
      return { text: '审核通过', icon: '✅' }
    case 3:
      return { text: '待审核', icon: '⏳' }
    case 4:
      return { text: '审核不通过', icon: '❌' }
    default:
      return { text: '未知状态', icon: '❓' }
  }
})

// 检查是否有场地管理权限
const hasVenueManagementPermission = computed(() => {
  // 只有工作人员角色才能访问场地管理
  if (currentRole.value !== 'staff') {
    return false
  }

  // 获取当前用户的角色类型
  const currentRoleType = accountInfo.value?.roleType
  if (!currentRoleType) {
    return false
  }

  // 检查角色类型是否在允许的列表中
  return VENUE_MANAGEMENT_ALLOWED_ROLES.includes(currentRoleType)
})

// 页面初始化（只处理页面特有逻辑）
onMounted(async () => {
  // 页面加载动画
  await nextTick()
  setTimeout(() => {
    isLoaded.value = true
  }, 100)
})

// 跳转到个人信息页面
const goToPersonalInfo = async () => {
  if (buttonStates.personal) return
  buttonStates.personal = true

  try {
    if (currentRole.value === 'staff') {
      router.push('/staffInfo')
    } else if (currentRole.value === 'actor') {
      router.push('/actor')
    } else {
      showToast('个人信息功能开发中...')
    }
  } catch (error) {
    showToast('跳转失败，请重试')
  } finally {
    buttonStates.personal = false
  }
}

// 跳转到实名状态页面
const goToRealName = async () => {
  if (buttonStates.realName) return
  buttonStates.realName = true

  try {
    const fddVerifyStatus = accountInfo.value?.fddVerifyStatus
    const fddCustomerVerifyUrl = accountInfo.value?.fddCustomerVerifyUrl

    // 首先查询最新的实名状态
    const statusResult = await homeStore.checkRealNameStatus(currentRole.value)

    if (!statusResult.status) {
      showToast(statusResult.message)
      return
    }
    if (statusResult.validResult == 0) {
      showToast('请先完善个人信息后再进行实名认证')
    } else if (statusResult.validResult == 2 && (statusResult?.url || fddCustomerVerifyUrl)) {
      // 如果是ios
      if (IS_IOS) {
        window.location.href = statusResult?.url || fddCustomerVerifyUrl
        return
      }
      window.open(statusResult?.url || fddCustomerVerifyUrl, '_blank')
    } else if (statusResult.validResult != 1) {
      showToast('请联系管理人员进行实名认证')
    }
  } catch (error) {
    console.error('实名状态处理失败:', error)
    showToast('操作失败，请重试')
  } finally {
    buttonStates.realName = false
  }
}

// 跳转到演员附加资料页面
const goToActorProfile = async () => {
  if (buttonStates.actorProfile) return
  buttonStates.actorProfile = true

  try {
    router.push('/talent')
  } catch (error) {
    showToast('跳转失败，请重试')
  } finally {
    buttonStates.actorProfile = false
  }
}

// 跳转到招募项目页面
const goToRecruitment = async () => {
  if (buttonStates.recruitment) return
  buttonStates.recruitment = true

  try {
    router.push('/recruitmentList')
  } catch (error) {
    showToast('跳转失败，请重试')
  } finally {
    buttonStates.recruitment = false
  }
}

// 跳转到我的报名页面
const goToApply = async () => {
  if (buttonStates.apply) return
  buttonStates.apply = true

  try {
    router.push('/apply')
  } catch (error) {
    showToast('跳转失败，请重试')
  } finally {
    buttonStates.apply = false
  }
}

// 跳转到合同管理页面
const goToContract = async () => {
  if (buttonStates.contract) return
  buttonStates.contract = true

  try {
    router.push('/contracts')
  } catch (error) {
    showToast('跳转失败，请重试')
  } finally {
    buttonStates.contract = false
  }
}

// 跳转到场地管理页面
const goToVenueManagement = async () => {
  if (buttonStates.venue) return
  buttonStates.venue = true

  try {
    // 检查权限
    if (!hasVenueManagementPermission.value) {
      showToast('您没有权限访问场地管理功能')
      return
    }

    router.push('/venue/list')
  } catch (error) {
    showToast('跳转失败，请重试')
  } finally {
    buttonStates.venue = false
  }
}

// 切换角色
const changeRole = async () => {
  if (buttonStates.changeRole) return
  buttonStates.changeRole = true

  try {
    router.push('/switch')
  } catch (error) {
    showToast('切换失败，请重试')
    buttonStates.changeRole = false
  }
}

// 确认退出登录
const confirmLogout = () => {
  showLogoutDialog.value = true
}

// 退出登录
const logout = async () => {
  showLogoutDialog.value = false
  buttonStates.logout = true

  try {
    loginStore.loginOut()
    sessionStorage.removeItem('selectedRole')
    router.push({ name: 'logoutSuccess' })
  } catch (error) {
    showToast('退出失败，请重试')
    buttonStates.logout = false
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.header {
  background: #fff;
  margin-bottom: 24px;
  padding: 16px 16px 16px 24px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: $primary-color;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #fff;
}

.info {
  flex: 1;
}

.name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}
</style>
