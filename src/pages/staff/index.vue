<template>
  <div class="bg-primary">
    <van-nav-bar title="个人信息" :border="false" left-arrow @click-left="handleBack" />
    <div class="form">
      <van-form @submit="handleSave" ref="formRef">
        <!-- 基本信息 -->
        <van-cell-group title="基本信息">
          <van-field v-model="formData.personName" :label="$t('person_name')"
            :placeholder="$t('please_enter') + $t('person_name')"
            :rules="[{ required: true, message: $t('required_field') }]" left-icon="contact-o" required />
          <van-field v-model="selectedRoleTypeName" :label="$t('role_type')"
            :placeholder="$t('please_select') + $t('role_type')" left-icon="manager-o" is-link readonly
            :rules="[{ required: true, message: $t('required_field') }]" required @click="showRoleTypePicker = true" />
          <van-field :label="'人员类型'" left-icon="friends-o" required>
            <template #input>
              <van-radio-group v-model="formData.personType" direction="horizontal">
                <van-radio :name="1">个人</van-radio>
                <van-radio :name="2">团体</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field v-model="formData.phone" type="tel" :label="$t('phone_number')"
            :placeholder="$t('please_enter') + $t('phone_number')" left-icon="phone-o"
            :rules="[{ required: true, message: $t('required_field') }]" required />
          <van-field v-model="userEmail" type="email" :label="$t('email_address')"
            :placeholder="$t('please_enter') + $t('email_address')" left-icon="envelop-o" required />

          <van-field v-model="formData.address" :label="$t('contact_address')"
            :placeholder="$t('please_enter') + $t('contact_address')" left-icon="location-o" />
          <van-field v-model="formData.company" :label="$t('company')" :placeholder="$t('please_enter') + $t('company')"
            left-icon="wap-home-o" />
        </van-cell-group>

        <van-cell-group title="身份证">
          <van-field v-model="formData.idNumber" :label="$t('id_number')"
            :placeholder="$t('please_enter') + $t('id_number')" left-icon="idcard"
            :rules="[{ required: true, message: $t('required_field') }]" required @input="handleIdNumberInput"
            @blur="handleIdNumberBlur" />
          <van-field v-model="formData.dateOfBirth" type="number" :label="$t('birth_year')"
            :placeholder="$t('please_enter') + $t('birth_year')" left-icon="calendar-o" :min="1900"
            :max="new Date().getFullYear()" />
          <van-field :label="$t('gender')" left-icon="flower-o" required>
            <template #input>
              <van-radio-group v-model="formData.gender" direction="horizontal">
                <van-radio :name="1">{{ $t('male') }}</van-radio>
                <van-radio :name="2">{{ $t('female') }}</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field :label="'身份证正面'" required>
            <template #input>
              <Upload v-model="formData.idCardFrontPhoto" :multiple="false" :max-count="1"
                @change="handleIdCardFrontChange" />
            </template>
          </van-field>
          <van-field :label="'身份证反面'" required>
            <template #input>
              <Upload v-model="formData.idCardVersoPhoto" :multiple="false" :max-count="1"
                @change="handleIdCardVersoChange" />
            </template>
          </van-field>
        </van-cell-group>

        <!-- 银行信息 -->
        <van-cell-group title="银行卡">
          <van-field v-model="formData.bankName" :label="$t('bank_name')"
            :placeholder="$t('please_enter') + $t('bank_name')" left-icon="shop-o" required />

          <van-field v-model="formData.accountName" :label="$t('account_name')"
            :placeholder="$t('please_enter') + $t('account_name')" left-icon="paid" required />

          <van-field v-model="formData.accountNumber" :label="$t('account_number')"
            :placeholder="$t('please_enter') + $t('account_number')" left-icon="credit-pay"
            :rules="[{ required: true, message: $t('required_field') }]" required />
        </van-cell-group>

        <!-- 操作按钮 -->
        <van-space direction="vertical" fill class="buttons">
          <van-button native-type="submit" type="primary" :loading="loading" :disabled="loading" size="large" round>
            {{ loading ? '保存中' : '保存' }}
          </van-button>
          <van-button type="default" size="large" round @click="handleBack">取消</van-button>
        </van-space>
      </van-form>
    </div>

    <!-- 角色类型选择器 -->
    <van-popup v-model:show="showRoleTypePicker" position="bottom" :style="{ height: '40%' }">
      <van-picker :columns="roleTypeColumns" @confirm="onRoleTypeConfirm" @cancel="showRoleTypePicker = false"
        :title="$t('please_select') + $t('role_type')" />
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, onMounted, computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { showToast } from 'vant'
import useStaffStore from '@/stores/staff'
import useLoginStore from '@/stores/login'
import Upload from '@/components/Upload.vue'
import { validateIdCard, parseIdCard } from '@/utils/idCard'
import { ROLE_TYPE_CONFIG } from '@/consts/index'
import dayjs from 'dayjs'

const router = useRouter()
const { t } = useI18n()
const staffStore = useStaffStore()
const loginStore = useLoginStore()
const formRef = ref()

// 原始身份证照片（用于回显逻辑）
const originIdCardFrontPhoto = ref('')
const originIdCardVersoPhoto = ref('')

// 角色类型选择器状态
const showRoleTypePicker = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  personName: '',
  jobNumber: '',
  dateOfBirth: null,
  isChildActor: false,
  gender: 1,
  personType: 1, // 1: 个人, 2: 团体
  idNumber: '',
  idCardFrontPhoto: '',
  idCardVersoPhoto: '',
  address: '',
  phone: '',
  eMail: '',
  roleType: null,
  company: '',
  isInternal: 0,
  bankName: '',
  accountName: '',
  accountNumber: '',
  bankInfos: [],
  unionId: '',
  fddVerifyStatus: null,
  fddCustomerVerifyUrl: '',
  isDelete: false,
})

// 加载状态
const loading = computed(() => staffStore.loading)

// 用户邮箱（如果没有值则可编辑）
const userEmail = ref('')

// 初始化邮箱值
const initUserEmail = () => {
  const loginEmail = loginStore.getUserInfo?.email
  // 只有在真正有邮箱值时才设置，避免设置空字符串
  userEmail.value = loginEmail && loginEmail.trim() !== '' ? loginEmail : ''
}

// 角色类型选项（排除演员角色，按sort排序）
const roleTypeColumns = computed(() => {
  return Object.entries(ROLE_TYPE_CONFIG)
    .sort(([, a], [, b]) => a.sort - b.sort) // 按sort字段排序
    .map(([value, config]) => ({
      text: config.label,
      value: Number(value),
      disable: !!config?.disable,
    }))
})

// 当前选中的角色类型名称
const selectedRoleTypeName = computed(() => {
  if (!formData.roleType) return ''
  return ROLE_TYPE_CONFIG[formData.roleType]?.label || ''
})

// 角色类型选择确认
const onRoleTypeConfirm = ({ selectedOptions }) => {
  formData.roleType = selectedOptions[0].value
  showRoleTypePicker.value = false
}

// 数据清理函数：处理数字字段的空值问题
const cleanFormData = data => {
  const cleaned = { ...data }

  // 数字字段列表
  const numberFields = ['dateOfBirth']

  // 将空字符串转换为 null
  numberFields.forEach(field => {
    if (cleaned[field] === '' || cleaned[field] === undefined) {
      cleaned[field] = null
    } else if (cleaned[field] !== null) {
      // 确保数字字段是数字类型
      const numValue = Number(cleaned[field])
      cleaned[field] = isNaN(numValue) ? null : numValue
    }
  })

  return cleaned
}

// 保存工作人员信息
const handleSave = async () => {
  try {
    // 验证必填字段
    if (!formData.personName) {
      showToast('请填写姓名')
      return
    }

    if (!formData.roleType) {
      showToast('请选择角色类型')
      return
    }

    if (!formData.personType) {
      showToast('请选择人员类型')
      return
    }

    if (!formData.idNumber) {
      showToast('请填写身份证号码')
      return
    }

    if (!formData.phone) {
      showToast('请填写手机号码')
      return
    }

    // 验证身份证格式
    if (!validateIdCard(formData.idNumber)) {
      showToast('身份证号码格式不正确')
      return
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.phone)) {
      showToast('请输入正确的手机号码')
      return
    }
    if (!formData.idCardFrontPhoto) {
      showToast('请上传身份证正面照')
      return
    }
    if (!formData.idCardVersoPhoto) {
      showToast('请上传身份证反面照')
      return
    }
    if (!formData.bankName) {
      showToast('请填写银行卡开户行名称')
      return
    }
    if (!formData.accountName) {
      showToast('请填写银行卡账户名称')
      return
    }
    if (!formData.accountNumber) {
      showToast('请填写银行卡号')
      return
    }

    // 在提交前将邮件字段赋值到表单数据并清理数据
    const submitData = cleanFormData({
      ...formData,
      eMail: userEmail.value,
    })

    // 身份证照片处理：如果表单中没有新值，则使用原始值
    submitData.idCardFrontPhoto = formData.idCardFrontPhoto || originIdCardFrontPhoto.value
    submitData.idCardVersoPhoto = formData.idCardVersoPhoto || originIdCardVersoPhoto.value

    console.log('提交的工作人员表单数据:', submitData)
    console.log('邮件地址:', submitData.eMail)
    console.log('身份证正面照片:', submitData.idCardFrontPhoto)
    console.log('身份证反面照片:', submitData.idCardVersoPhoto)
    console.log('数字字段处理:', {
      dateOfBirth: submitData.dateOfBirth,
    })
    // 当前时间
    submitData.createTime = dayjs().format('YYYY-MM-DD')
    submitData.updateTime = dayjs().format('YYYY-MM-DD')
    delete submitData.creator
    delete submitData.lastModifier
    const result = await staffStore.saveStaffInfo(submitData)
    if (result.status) {
      showToast(t('save_success'))
      // 可以选择返回上一页或刷新数据
      router.back()
    } else {
      showToast(result.message || t('save_failed'))
    }
  } catch (error) {
    showToast(t('save_failed'))
  }
}

// 取消操作
const handleBack = () => {
  router.back()
}

// 身份证正面照片上传处理
const handleIdCardFrontChange = value => {
  console.log('身份证正面照片:', value)
  formData.idCardFrontPhoto = value
}

// 身份证反面照片上传处理
const handleIdCardVersoChange = value => {
  console.log('身份证反面照片:', value)
  formData.idCardVersoPhoto = value
}

// 身份证号码输入处理
const handleIdNumberInput = value => {
  // 格式化输入（可选，如果需要自动格式化）
  // formData.idNumber = formatIdCardInput(value)
}

// 身份证号码失焦处理（联动其他字段）
const handleIdNumberBlur = () => {
  const idNumber = formData.idNumber.replace(/\s/g, '') // 移除空格

  if (!idNumber) {
    return
  }

  // 验证身份证号码
  if (!validateIdCard(idNumber)) {
    showToast('身份证号码格式不正确')
    return
  }

  // 解析身份证信息
  const idCardInfo = parseIdCard(idNumber)
  if (!idCardInfo) {
    showToast('身份证号码解析失败')
    return
  }

  // 自动填充生日年份
  if (idCardInfo.birth && !formData.dateOfBirth) {
    formData.dateOfBirth = idCardInfo.birth.year
  }

  // 自动填充性别
  if (idCardInfo.gender && !formData.gender) {
    formData.gender = idCardInfo.gender
  }

  // 判断是否为未成年
  if (idCardInfo.isMinor !== null) {
    formData.isChildActor = idCardInfo.isMinor
  }
}

// 加载工作人员信息
const loadStaffInfo = async () => {
  try {
    const result = await staffStore.fetchStaffInfo()
    if (result.status) {
      // 将获取到的数据填充到表单中，使用store中已处理的数据
      console.log('staffStore.staffInfo=>', staffStore.staffInfo)
      console.log('result.lastData=>', result.lastData)

      // 保存原始身份证照片，用于提交时的回显逻辑
      originIdCardFrontPhoto.value =
        result?.lastData?.idCardFrontPhoto || staffStore.staffInfo?.idCardFrontPhoto
      originIdCardVersoPhoto.value =
        result?.lastData?.idCardVersoPhoto || staffStore.staffInfo?.idCardVersoPhoto

      // 使用 Object.assign 来更新 reactive 对象
      Object.assign(formData, result.lastData || staffStore.staffInfo)
      userEmail.value = formData.eMail || userEmail.value

      // 默认不展示身份证照片（设为空字符串）
      formData.idCardFrontPhoto = ''
      formData.idCardVersoPhoto = ''

      console.log('formData=>', formData)
    } else if (result.message) {
      showToast(result.message)
    }
  } catch (error) {
    showToast(t('load_failed'))
  }
}

// 页面初始化
onMounted(() => {
  // 检查登录状态
  if (!localStorage.getItem('token')) {
    router.push('/login')
    return
  }

  // 初始化用户信息（确保能获取到email等信息）
  loginStore.initUserInfo()

  // 初始化邮箱值
  initUserEmail()

  // 加载工作人员信息
  loadStaffInfo()
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.form {
  background: #fff;
  border-radius: 16px 16px 0px 0px;
  padding-top: 12px;

  :deep(.van-cell-group__title) {
    color: #000;
    font-weight: bold;
  }
}

.buttons {
  padding: 36px 24px;
}
</style>
