<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar title="合同管理" :border="false" left-arrow @click-left="goBack" />

    <!-- 内容区域 -->
    <div class="body">
      <van-loading v-if="contractLoading" color="#ff6600" size="36" vertical>加载中...</van-loading>
      <van-empty v-else-if="contractList.length === 0" description="暂无数据" />

      <!-- 合同列表 -->
      <div v-else class="contract-list">
        <Card
          v-for="(contract, index) in contractList"
          @click.stop="viewContract(contract)"
          :key="contract.id || index"
          @click="handleContractClick(contract)">
          <template #header>
            {{ contract.contractName || '合同' }}
          </template>

          <div class="contract-info">
            <div class="info-row" v-if="contract.novelName">
              <span class="label">拍摄项目</span>
              <span class="value">{{ contract.novelName }}</span>
            </div>
            <div class="info-row" v-if="contract.contractNumb">
              <span class="label">合同编号</span>
              <span class="value">{{ contract.contractNumb }}</span>
            </div>
            <div class="info-row" v-if="contract.partyB">
              <span class="label">乙方</span>
              <span class="value">{{ contract.partyB }}</span>
            </div>
            <div class="info-row" v-if="contract.partyADate">
              <span class="label">签署日期</span>
              <span class="value">{{ formatContractDate(contract.partyADate) }}</span>
            </div>
            <div class="info-row" v-if="contract.signTime">
              <span class="label">签订完毕时间</span>
              <span class="value">{{ formatContractDate(contract.signTime) }}</span>
            </div>
            <div class="info-row">
              <span class="label">合同状态</span>
              <span class="value">
                <van-tag :type="getContractStatusClass(contract.status)" plain>{{
                  getContractStatusText(contract.status)
                }}</van-tag>
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import useLoginStore from '@/stores/login'
import useContractStore from '@/stores/contract'
import { showToast } from 'vant'
import Card from '@/components/Card.vue'

import {
  getContractUrl,
  getContractStatusText,
  getContractStatusClass,
  formatContractDate,
  canViewContract,
} from '@/types/contract'
import { IS_IOS } from '@/consts/index'

const router = useRouter()
const loginStore = useLoginStore()
const contractStore = useContractStore()

// 从store获取数据
const contractList = computed(() => contractStore.validContractList)
const contractLoading = computed(() => contractStore.loading)
const contractError = computed(() => contractStore.error)
const currentRole = computed(() => loginStore.getCurrentRole)

// 页面初始化
onMounted(async () => {
  await loadContractList()
})

// 加载合同列表
const loadContractList = async () => {
  try {
    const result = await contractStore.fetchContractList(currentRole.value)
    if (!result.status) {
      showToast(result.message)
    }
  } catch (error) {
    console.error('加载合同列表失败:', error)
    showToast('加载失败，请重试')
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 注意：getContractUrl 函数已从 @/types/contract 导入，这里不需要重复定义

// 处理合同点击
const handleContractClick = contract => {
  // 点击合同项直接查看合同
  viewContract(contract)
}

// 查看合同
const viewContract = contract => {
  const contractUrl = getContractUrl(contract)
  if (!contractUrl) {
    showToast('该合同暂无可用链接')
    return
  }

  try {
    // 在新窗口中打开合同链接
    if (IS_IOS) {
      window.location.href = contractUrl || contractUrl
    } else {
      window.open(contractUrl, '_blank', 'noopener,noreferrer')
    }
    showToast('正在打开合同...')
  } catch (error) {
    console.error('打开合同失败:', error)
    showToast('打开合同失败，请重试')
  }
}

// 注意：复制功能已移除，现在使用查看功能

// 注意：状态处理和格式化函数已从 @/types/contract 导入
</script>

<style scoped lang="scss">
.page {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background: #f5f5f5;
}

// 导航栏样式已移除，使用统一的 NavBar 组件

.body {
  flex: 1;
  padding: 16px 12px;
}

.contract-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contract-info {
  .info-row {
    display: flex;
    margin-bottom: 16px;

    .label {
      color: #666;
    }

    .value {
      flex: 1;
      text-align: right;
    }
  }
}
</style>
