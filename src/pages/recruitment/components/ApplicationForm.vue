<template>
  <van-popup
    v-model:show="showModal"
    position="bottom"
    round
    :style="{ height: '70%' }"
    closeable
    close-icon-position="top-right">
    <div>
      <div class="form-header">
        <strong>报名申请</strong>
      </div>

      <van-form @submit="handleSubmit" ref="formRef">
        <van-cell-group>
          <van-field label="拍摄项目" :model-value="project?.productionName" readonly />
          <van-field
            label="申请岗位"
            :model-value="getRoleTypeText(recruitment?.roleType, recruitment?.parentType)"
            readonly />
          <van-field
            v-model="formData.expectedSalary"
            type="number"
            label="期望薪资"
            placeholder="请输入期望薪资"
            :rules="[{ required: false }]">
            <template #right-icon>
              <span>{{ getCurrencySymbol(recruitment?.currency) }}</span>
            </template>
          </van-field>
          <van-field
            v-model="formData.introduction"
            type="textarea"
            label="个人介绍"
            placeholder="请简单介绍一下自己的经历和优势"
            rows="3"
            maxlength="500"
            show-word-limit
            :rules="[{ required: true, message: '请填写个人介绍' }]" />

          <van-field name="uploader" label="附件上传">
            <template #input>
              <van-uploader
                v-model="uploadFiles"
                multiple
                :max-count="5"
                :max-size="50 * 1024 * 1024"
                :after-read="handleFileUpload"
                :before-delete="handleFileDelete"
                accept="*"
                upload-text="选择文件">
              </van-uploader>
            </template>
          </van-field>
        </van-cell-group>
        <!-- 提交按钮 -->
        <div class="form-actions">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="submitting"
            :disabled="submitting">
            {{ submitting ? '提交中...' : '提交申请' }}
          </van-button>
        </div>
      </van-form>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import useRecruitmentStore from '@/stores/recruitment'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'
import { showToast } from 'vant'
import { useLoad } from '@/components/loading/useLoad'
import { getRoleTypeText } from '@/consts/index'
import { API, UPLOAD_API } from '@/utils/env'
// Vant组件已在main.js中全局注册

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  recruitment: {
    type: Object,
    default: null,
  },
  project: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:show', 'success'])

const router = useRouter()
const recruitmentStore = useRecruitmentStore()
const loginStore = useLoginStore()
const homeStore = useHomeStore()
const { open: openLoading, close: closeLoading } = useLoad()

// 表单引用
const formRef = ref(null)

// 弹窗显示状态
const showModal = computed({
  get: () => props.show,
  set: value => emit('update:show', value),
})

// 表单数据
const formData = ref({
  introduction: '',
  expectedSalary: '',
  introductionUrls: '',
})

// 上传文件列表
const uploadFiles = ref([])

// 提交状态
const submitting = ref(false)

// 监听弹窗显示状态，重置表单
watch(
  () => props.show,
  newVal => {
    if (newVal) {
      resetForm()
    }
  }
)

// 重置表单
const resetForm = () => {
  formData.value = {
    introduction: '',
    expectedSalary: '',
    introductionUrls: '',
  }
  uploadFiles.value = []
  submitting.value = false
}

// 获取角色类型文本（从常量文件导入）

// 获取货币符号
const getCurrencySymbol = currency => {
  const currencyMap = {
    1: '¥',
    2: '$',
    3: '¥',
  }
  return currencyMap[currency] || '¥'
}

// 获取文件图标
const getFileIcon = type => {
  if (!type) return '📄'

  if (type.includes('image')) return '🖼️'
  if (type.includes('video')) return '🎬'
  if (type.includes('audio')) return '🎵'
  if (type.includes('pdf')) return '📕'
  if (type.includes('word') || type.includes('doc')) return '📘'
  if (type.includes('excel') || type.includes('sheet')) return '📗'
  return '📄'
}

// 获取文件名（截取显示）
const getFileName = name => {
  if (!name) return '未知文件'
  if (name.length > 20) {
    return name.substring(0, 17) + '...'
  }
  return name
}

// 处理文件上传
const handleFileUpload = async files => {
  // 开启全局loading
  openLoading()

  try {
    console.log('上传文件:', files)

    // 处理单个文件或多个文件
    const fileArray = Array.isArray(files) ? files : [files]

    // 逐个上传文件
    const uploadPromises = fileArray.map(async file => {
      try {
        // 调用真实的上传接口
        const uploadedUrl = await uploadFileToServer(file.file)

        // 更新文件对象，添加完整的URL
        file.url = uploadedUrl.startsWith('http') ? uploadedUrl : API + uploadedUrl

        return file
      } catch (error) {
        console.error('单个文件上传失败:', error)
        // 单个文件失败时，从上传列表中移除
        const index = uploadFiles.value.findIndex(item => item === file)
        if (index > -1) {
          uploadFiles.value.splice(index, 1)
        }
        throw error
      }
    })

    // 等待所有文件上传完成
    await Promise.all(uploadPromises)

    showToast(`${fileArray.length > 1 ? '所有文件' : '文件'}上传成功`)

    return Array.isArray(files) ? fileArray : fileArray[0]
  } catch (error) {
    console.error('文件上传失败:', error)
    showToast(error.message || '文件上传失败，请重试')
    return false
  } finally {
    // 无论成功失败都关闭loading
    closeLoading()
  }
}

// 处理文件删除
const handleFileDelete = file => {
  console.log('删除文件:', file)
  return true
}

// 处理表单提交
const handleSubmit = async () => {
  try {
    submitting.value = true

    // 获取当前用户信息
    const userInfo = loginStore.getUserInfo
    const accountInfo = homeStore.accountInfo

    if (!userInfo || !accountInfo?.personId) {
      showToast('用户信息不完整，请先完善个人信息')
      // 关闭弹窗并跳转到home页面
      showModal.value = false
      setTimeout(() => {
        router.push({ name: 'mine' })
      }, 1500) // 延迟1.5秒让用户看到提示信息
      return
    }

    // 构建文件URL字符串（多文件逗号分割）
    const fileUrls = uploadFiles.value
      .filter(file => file.url)
      .map(file => {
        // 如果是完整URL则直接使用，否则去掉API前缀保存相对路径
        return file.url.startsWith('http')
          ? file.url.startsWith(API)
            ? file.url.replace(API, '')
            : file.url
          : file.url
      })
      .join(',')

    // 构建报名数据
    const applicationData = {
      productionId: props.project?.id,
      parentType: props.recruitment?.parentType,
      roleType: props.recruitment?.roleType,
      recruitmentId: props.recruitment?.id, // 新增招募ID字段
      introduction: formData.value.introduction,
      introductionUrls: fileUrls,
      expectedSalary: formData.value.expectedSalary
        ? parseFloat(formData.value.expectedSalary)
        : null,
    }

    console.log('提交报名数据:', applicationData)

    // 调用报名接口
    const result = await recruitmentStore.applyForProject(applicationData)

    if (result.status) {
      showToast('报名成功！')
      emit('success')
      showModal.value = false
    } else {
      showToast(result.message || '报名失败，请重试')
    }
  } catch (error) {
    console.error('报名提交失败:', error)
    showToast('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 真实的文件上传接口
const uploadFileToServer = async file => {
  try {
    const formData = new FormData()
    formData.append('filename', file) // 确保这里的字段名 'filename' 与后端期望的匹配

    console.log('Authorization token:', localStorage.getItem('token') || 'No Token')

    const response = await fetch(API + UPLOAD_API, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${localStorage.getItem('token') || ''}`,
        Area: 'Changdu',
        AreaInterface: 'cn',
      },
      body: formData,
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('上传失败响应:', errorText)
      throw new Error(`网络错误: ${response.status} - ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    console.log('上传响应数据:', data)

    // 假设后端返回的数据结构是 { code: 311, data: { data: [{ url: '...' }] } }
    if (
      data.code == 311 &&
      data.data?.data &&
      Array.isArray(data.data.data) &&
      data.data.data.length > 0
    ) {
      return data.data.data[0].url
    }

    throw new Error(data.msg || data.message || '服务器返回异常数据')
  } catch (error) {
    console.error('上传过程中发生错误:', error)
    throw error
  }
}
</script>

<style scoped lang="scss">
.form-header {
  padding: 16px 10px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.form-actions {
  margin-top: 36px;
  padding: 0 12px;
}
</style>
