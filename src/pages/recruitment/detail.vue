<template>
  <div class="recruitment-detail-container">
    <!-- 导航栏 -->
    <van-nav-bar
      :title="project?.productionName || '项目详情'"
      left-arrow
      @click-left="handleBack" />

    <div class="detail-content" v-if="project">
      <!-- 项目基本信息 -->
      <Card>
        <template #header>
          <span class="title-3">{{ project.productionName }}</span>
        </template>
        <div class="project-info-grid">
          <div class="info-card" v-if="project.startDate">
            <div class="info-label">开机日期</div>
            <div class="info-value">{{ formatDate(project.startDate) }}</div>
          </div>
          <div class="info-card" v-if="project.endDate">
            <div class="info-label">杀青日期</div>
            <div class="info-value">{{ formatDate(project.endDate) }}</div>
          </div>
        </div>
        <template #footer v-if="project.description">
          <p>{{ project.description }}</p>
        </template>
      </Card>

      <!-- 招募信息列表 -->
      <div
        class="recruitment-list"
        v-if="project.personRecruitment && project.personRecruitment.length > 0">
        <span class="section-title">招募角色</span>
        <Card v-for="recruitment in project.personRecruitment" :key="recruitment.id">
          <template #header>
            <div class="space-between">
              <van-space :size="2">
                <span
                  >{{ getRoleTypeText(recruitment.roleType, recruitment.parentType) }} *
                  {{ recruitment.personCount }}</span
                >
                <van-divider vertical />
                <span>{{ getGenderText(recruitment.gender) }}</span>
              </van-space>
              <van-button
                size="small"
                :type="hasApplied(recruitment.id) ? 'success' : 'primary'"
                plain
                :disabled="hasApplied(recruitment.id)"
                @click="showApplicationForm(recruitment)">
                {{ hasApplied(recruitment.id) ? '已报名' : '立即报名' }}
              </van-button>
            </div>
          </template>

          <div class="recruitment-details">
            <div class="space-between" v-if="recruitment.description">
              <span>介绍</span>
              <span class="detail-value">{{ recruitment.description }}</span>
            </div>

            <div class="space-between" v-if="recruitment.salaryMin || recruitment.salaryMax">
              <span>报酬</span>
              <span class="detail-value salary">
                {{
                  formatSalary(
                    recruitment.salaryMin,
                    recruitment.salaryMax,
                    recruitment.salaryType,
                    recruitment.currency
                  )
                }}
              </span>
            </div>

            <div class="space-between" v-if="recruitment.specialRequirements">
              <span class="detail-label">特殊要求</span>
              <span class="detail-value">{{ recruitment.specialRequirements }}</span>
            </div>
          </div>
          <template #footer v-if="recruitment.isAccommodation || recruitment.isMeal">
            <van-space>
              <van-tag type="success" plain size="large" v-if="recruitment.isAccommodation"
                >包住宿</van-tag
              >
              <van-tag type="success" plain size="large" v-if="recruitment.isMeal">包餐食</van-tag>
            </van-space>
          </template>
        </Card>
      </div>

      <!-- 空状态 -->
      <div class="empty-recruitment" v-else>
        <div class="empty-icon">📋</div>
        <div class="empty-text">暂无招募信息</div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading" v-if="!project && !error">
      <van-loading size="36px" color="#ff6600" vertical>加载中...</van-loading>
    </div>

    <!-- 报名表单弹窗 -->
    <ApplicationForm
      v-model:show="showFormModal"
      :recruitment="selectedRecruitment"
      :project="project"
      @success="handleApplicationSuccess" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import useRecruitmentStore from '@/stores/recruitment'
import useLoginStore from '@/stores/login'
import { showToast } from 'vant'
import ApplicationForm from './components/ApplicationForm.vue'
import { getRoleTypeText } from '@/consts/index'
import Card from '@/components/Card.vue'
// Vant组件已在main.js中全局注册

const router = useRouter()
const route = useRoute()
const recruitmentStore = useRecruitmentStore()
const loginStore = useLoginStore()

// 显示报名表单
const showFormModal = ref(false)
const selectedRecruitment = ref(null)

// 从store获取项目信息
const project = computed(() => recruitmentStore.getCurrentProject)
const error = ref(null)

// 初始化页面数据
const initPageData = async () => {
  const projectId = route.params.id
  if (!projectId) {
    error.value = '缺少项目ID参数'
    return
  }

  try {
    // 根据当前角色确定personType
    const personType = loginStore.getCurrentPersonType

    // 获取项目详情
    const res = await recruitmentStore.fetchRecruitmentProjectById(projectId, personType)
    if (!res.status) {
      error.value = res.message || '获取项目信息失败'
    }
  } catch (err) {
    console.error('获取项目详情失败:', err)
    error.value = err.message || '网络错误'
  }
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 显示报名表单
const showApplicationForm = recruitment => {
  selectedRecruitment.value = recruitment
  showFormModal.value = true
}

// 处理报名成功
const handleApplicationSuccess = () => {
  showFormModal.value = false

  // 显示成功提示
  showToast('报名成功！')

  // 延迟后退到上一页
  router.back()
}

// 检查是否已报名
const hasApplied = recruitmentId => {
  return recruitmentStore.hasAppliedForRecruitment(recruitmentId)
}

// 格式化日期
const formatDate = dateString => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取角色类型文本（从常量文件导入）

// 获取性别文本
const getGenderText = gender => {
  const genderMap = {
    1: '男',
    2: '女',
    3: '不限',
  }
  return genderMap[gender] || '不限'
}

// 获取货币类型文本
const getCurrencyText = currency => {
  const currencyMap = {
    1: '人民币',
    2: '美元',
    3: '日元',
  }
  return currencyMap[currency] || '未知货币'
}

// 格式化薪资
const formatSalary = (min, max, salaryType, currency) => {
  const currencySymbol = { 1: '¥', 2: '$', 3: '¥' }[currency] || '¥'
  const typeText = salaryType === 1 ? '/日' : salaryType === 2 ? '总价' : ''

  if (min && max) {
    return `${currencySymbol}${min} - ${currencySymbol}${max}${typeText}`
  } else if (min) {
    return `${currencySymbol}${min}${typeText}起`
  } else if (max) {
    return `最高${currencySymbol}${max}${typeText}`
  }
  return '面议'
}

// 页面挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<style scoped lang="scss">
.recruitment-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.detail-content {
  padding: 16px 12px;
}

.project-basic-info {
  background: #fff;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.project-title {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  margin: 0;
  flex: 1;
}

.project-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.info-card {
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 6px;
}

.info-label {
  color: #666;
  margin-bottom: 8px;
}

.info-value {
  font-weight: 600;
}

.project-description {
  p {
    font-size: 32px;
    line-height: 1.6;
    color: #666;
    margin: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-top: 24px;
}

.recruitment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.recruitment-item {
  background: #fff;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.recruitment-details {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.detail-row {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-value {
  flex: 1;
  line-height: 2.4;
  text-align: right;

  &.salary {
    color: #ff8124;
    font-weight: 600;
  }
}

.loading {
  padding-top: 36px;
}
</style>
