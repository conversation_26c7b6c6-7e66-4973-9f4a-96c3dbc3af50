<template>
  <div>
    <van-nav-bar title="畅读招募" :border="false"> </van-nav-bar>
    <!-- 项目列表 -->
    <van-space direction="vertical" class="list" fill v-if="!loading && projectList.length > 0">
      <Card v-for="project in projectList" :key="project.id" @click="goToProjectDetail(project)">
        <template v-slot:header>
          <div class="title-3">{{ project.productionName }}</div>
        </template>

        <div class="project-info">
          <div class="info-item" v-if="project.startDate">
            <span class="label">开机日期</span>
            <strong>{{ formatDate(project.startDate) }}</strong>
          </div>
          <div class="info-item" v-if="project.endDate">
            <span class="label">杀青日期</span>
            <strong>{{ formatDate(project.endDate) }}</strong>
          </div>
        </div>

        <div class="project-description" v-if="project.description">
          {{ project.description }}
        </div>
        <van-space
          block
          wrap
          v-if="project.mergedRecruitmentInfo && project.mergedRecruitmentInfo.length > 0">
          <van-tag
            plain
            type="primary"
            size="large"
            v-for="recruitment in project.mergedRecruitmentInfo"
            :key="`${recruitment.roleType}-${recruitment.parentType}`"
            >{{ getRoleTypeText(recruitment.roleType, recruitment.parentType) }} *
            {{ recruitment.personCount }}</van-tag
          >
        </van-space>
      </Card>
    </van-space>
    <van-empty v-if="!loading && projectList.length === 0" description="暂无数据" />
    <van-loading v-if="loading" class="loading" size="36px" color="#ff6600" vertical
      >加载中...</van-loading
    >
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Card from '@/components/Card.vue'
import useRecruitmentStore from '@/stores/recruitment'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'
import { showToast } from 'vant'
import { getRoleTypeText } from '@/consts/index'

const router = useRouter()
const route = useRoute()
const recruitmentStore = useRecruitmentStore()
const loginStore = useLoginStore()
const homeStore = useHomeStore()

// 判断是否在布局页面中
const isInLayout = computed(() => route.name === 'layoutHome')
// 从store获取账户信息
const accountInfo = computed(() => homeStore.accountInfo)
// 当前人员类型 (0人员，1演员)
const currentPersonType = ref(1)

// 从store获取数据
const projectList = computed(() => recruitmentStore.getProjectList)
const loading = computed(() => recruitmentStore.loading)

// 返回上一页
const handleBack = () => {
  router.back()
}

// 人员类型已根据用户角色固定，无需切换

// 加载项目列表
const loadProjectList = async () => {
  try {
    await recruitmentStore.fetchRecruitmentProjects(currentPersonType.value)
  } catch (error) {
    console.error('加载项目列表失败:', error)
    showToast('加载失败，请重试')
  }
}

// 跳转到项目详情
const goToProjectDetail = project => {
  if (accountInfo?.value?.personId) {
    // 设置选中的项目
    recruitmentStore.setSelectedProject(project)
    // 跳转到详情页
    router.push({
      name: 'recruitmentDetail',
      params: { id: project.id },
    })
  } else {
    showToast('请先去个人中心完善个人信息')
  }
}

// 格式化日期
const formatDate = dateString => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取角色类型文本（从常量文件导入）

// 页面初始化
onMounted(async () => {
  // 根据当前角色设置默认的人员类型
  currentPersonType.value = loginStore.getCurrentPersonType

  await loadProjectList()
})
</script>

<style scoped lang="scss">
.list {
  padding: 1rem;
}

.hr {
  margin-block: 0.8rem;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.project-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  color: #666;
  margin-right: 10px;
  min-width: 180px;
}

.project-description {
  background: #f8f9fa;
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 8px;
  line-height: 1.6;
  color: #666;
}

.loading {
  padding-top: 36px;
}
</style>
