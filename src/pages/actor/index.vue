<template>
  <div class="bg-primary">
    <van-nav-bar :title="$t('actor_info')" :border="false" left-arrow @click-left="handleBack" />

    <div class="form">
      <van-form @submit="handleSave" ref="formRef">
        <!-- 基本信息 -->
        <van-cell-group title="基本信息">
          <van-field v-model="formData.personName" :label="$t('person_name')"
            :placeholder="$t('please_enter') + $t('person_name')"
            :rules="[{ required: true, message: $t('required_field') }]" left-icon="user-o" required />
          <van-field v-model="formData.stageName" :label="$t('stage_name')"
            :placeholder="$t('please_enter') + $t('stage_name')" left-icon="manager-o" />
          <van-field :label="'人员类型'" left-icon="friends-o" required>
            <template #input>
              <van-radio-group v-model="formData.personType" direction="horizontal">
                <van-radio :name="1">个人</van-radio>
                <van-radio :name="2">团体</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field v-model="formData.phone" type="tel" :label="$t('phone_number')"
            :placeholder="$t('please_enter') + $t('phone_number')" left-icon="phone-o"
            :rules="[{ required: true, message: $t('required_field') }]" required />
          <van-field v-model="userEmail" type="email" :label="$t('email_address')"
            :placeholder="$t('please_enter') + $t('email_address')" left-icon="envelop-o" required />
          <van-field v-model="formData.address" :label="$t('contact_address')"
            :placeholder="$t('please_enter') + $t('contact_address')" left-icon="wap-home-o" />
          <van-field v-model="formData.cityName" :label="$t('city_name')"
            :placeholder="$t('please_enter') + $t('city_name')" left-icon="location-o" />
        </van-cell-group>
        <van-cell-group title="身份证">
          <van-field v-model="formData.idNumber" :label="$t('id_number')"
            :placeholder="$t('please_enter') + $t('id_number')" left-icon="idcard"
            :rules="[{ required: true, message: $t('required_field') }]" required @input="handleIdNumberInput"
            @blur="handleIdNumberBlur" />
          <van-field v-model="formData.dateOfBirth" type="number" :label="$t('birth_year')"
            :placeholder="$t('please_enter') + $t('birth_year')" left-icon="calendar-o" :min="1900"
            :max="new Date().getFullYear()" />
          <van-field label="是否儿童" left-icon="flower-o">
            <template #input>
              <van-radio-group v-model="formData.isChildActor" direction="horizontal">
                <van-radio :name="false">{{ $t('no') }}</van-radio>
                <van-radio :name="true">{{ $t('yes') }}</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field :label="$t('gender')" left-icon="gem-o" required>
            <template #input>
              <van-radio-group v-model="formData.gender" direction="horizontal">
                <van-radio :name="1">{{ $t('male') }}</van-radio>
                <van-radio :name="2">{{ $t('female') }}</van-radio>
              </van-radio-group>
            </template>
          </van-field>
          <van-field :label="'身份证正面'" required>
            <template #input>
              <Upload v-model="formData.idCardFrontPhoto" :multiple="false" :max-count="1"
                @change="handleIdCardFrontChange" />
            </template>
          </van-field>
          <van-field :label="'身份证反面'" required>
            <template #input>
              <Upload v-model="formData.idCardVersoPhoto" :multiple="false" :max-count="1"
                @change="handleIdCardVersoChange" />
            </template>
          </van-field>
        </van-cell-group>

        <van-cell-group title="演员信息">
          <van-field v-model="formData.height" type="number" :label="$t('height')"
            :placeholder="$t('please_enter') + $t('height')" left-icon="contact-o" />
          <van-field v-model="formData.weight" type="number" :label="$t('weight')"
            :placeholder="$t('please_enter') + $t('weight')" left-icon="contact-o" />
          <van-field v-model="formData.bust" type="number" :label="$t('bust')"
            :placeholder="$t('please_enter') + $t('bust')" left-icon="contact-o" />
          <van-field v-model="formData.waist" type="number" :label="$t('waist')"
            :placeholder="$t('please_enter') + $t('waist')" left-icon="contact-o" />
          <van-field v-model="formData.hips" type="number" :label="$t('hips')"
            :placeholder="$t('please_enter') + $t('hips')" left-icon="contact-o" />
          <van-field v-model="formData.school" :label="$t('school')" :placeholder="$t('please_enter') + $t('school')"
            left-icon="medal-o" />
          <van-field v-model="formData.specialty" :label="$t('specialty')" placeholder="多个特长使用逗号分割"
            left-icon="star-o" />
          <van-field v-model="formData.actingStyle" :label="$t('acting_style')" placeholder="多个风格使用逗号分割"
            left-icon="like-o" />
        </van-cell-group>

        <!-- 银行信息 -->
        <van-cell-group title="银行卡">
          <van-field v-model="formData.bankName" :label="$t('bank_name')"
            :placeholder="$t('please_enter') + $t('bank_name')" left-icon="shop-o" required />
          <van-field v-model="formData.accountName" :label="$t('account_name')"
            :placeholder="$t('please_enter') + $t('account_name')" left-icon="paid" required />
          <van-field v-model="formData.accountNumber" :label="$t('account_number')"
            :placeholder="$t('please_enter') + $t('account_number')" left-icon="credit-pay"
            :rules="[{ required: true, message: $t('required_field') }]" required />
        </van-cell-group>

        <!-- 操作按钮 -->
        <van-space direction="vertical" fill class="buttons">
          <van-button native-type="submit" :loading="loading" :disabled="loading" size="large" round class="save-btn"
            @click="handleSave" color="#ff6b00">
            {{ loading ? $t('saving') : $t('save') }}
          </van-button>

          <van-button type="default" size="large" round class="cancel-btn" @click="handleBack">
            {{ $t('cancel') }}
          </van-button>
        </van-space>
      </van-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { showToast } from 'vant'
import useActorStore from '@/stores/actor'
import useLoginStore from '@/stores/login'
import Upload from '@/components/Upload.vue'
import { validateIdCard, parseIdCard, formatIdCardInput } from '@/utils/idCard'

const router = useRouter()
const { t } = useI18n()
const actorStore = useActorStore()
const loginStore = useLoginStore()
const formRef = ref()

const originIdCardFrontPhoto = ref('')
const originIdCardVersoPhoto = ref('')
// 表单数据
const formData = reactive({
  id: null,
  personId: null,
  personName: '',
  stageName: '',
  dateOfBirth: null,
  isChildActor: false,
  gender: 1,
  personType: 1, // 1: 个人, 2: 团体
  idNumber: '',
  idCardFrontPhoto: '',
  idCardVersoPhoto: '',
  address: '',
  phone: '',
  eMail: '',
  roleType: 9,
  height: null,
  weight: null,
  bust: null,
  waist: null,
  hips: null,
  headUrl: '',
  school: '',
  specialty: '',
  actingStyle: '',
  cityName: '',
  bankName: '',
  accountName: '',
  accountNumber: '',
  bankInfos: [],
  unionId: '',
  isInternal: 0,
})

// 加载状态
const loading = computed(() => actorStore.loading)

// 用户邮箱（如果没有值则可编辑）
const userEmail = ref('')

// 初始化邮箱值
const initUserEmail = () => {
  const loginEmail = loginStore.getUserInfo?.email
  // 只有在真正有邮箱值时才设置，避免设置空字符串
  userEmail.value = loginEmail && loginEmail.trim() !== '' ? loginEmail : ''
}

// 数据清理函数：处理数字字段的空值问题
const cleanFormData = data => {
  const cleaned = { ...data }

  // 数字字段列表
  const numberFields = ['dateOfBirth', 'height', 'weight', 'bust', 'waist', 'hips']

  // 将空字符串转换为 null
  numberFields.forEach(field => {
    if (cleaned[field] === '' || cleaned[field] === undefined) {
      cleaned[field] = null
    } else if (cleaned[field] !== null) {
      // 确保数字字段是数字类型
      const numValue = Number(cleaned[field])
      cleaned[field] = isNaN(numValue) ? null : numValue
    }
  })

  return cleaned
}

// 保存演员信息
const handleSave = async () => {
  try {
    // 验证必填字段
    if (!formData.personName) {
      showToast('请填写姓名')
      return
    }

    if (!formData.personType) {
      showToast('请选择人员类型')
      return
    }

    if (!formData.idNumber) {
      showToast('请填写身份证号码')
      return
    }

    if (!formData.phone) {
      showToast('请填写手机号码')
      return
    }

    if (!userEmail.value) {
      showToast('请填写邮箱')
      return
    }
    if (!formData.idCardFrontPhoto) {
      showToast('请上传身份证正面照')
      return
    }
    if (!formData.idCardVersoPhoto) {
      showToast('请上传身份证反面照')
      return
    }
    if (!formData.bankName) {
      showToast('请填写银行卡开户行名称')
      return
    }
    if (!formData.accountName) {
      showToast('请填写银行卡账户名称')
      return
    }
    if (!formData.accountNumber) {
      showToast('请填写银行卡号')
      return
    }


    // 验证身份证格式
    if (!validateIdCard(formData.idNumber)) {
      showToast('身份证号码格式不正确')
      return
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(formData.phone)) {
      showToast('请输入正确的手机号码')
      return
    }

    // 在提交前将邮件字段赋值到表单数据并清理数据
    const submitData = cleanFormData({
      ...formData,
      eMail: userEmail.value,
    })
    submitData.idCardFrontPhoto = formData.idCardFrontPhoto || originIdCardFrontPhoto.value
    submitData.idCardVersoPhoto = formData.idCardVersoPhoto || originIdCardVersoPhoto.value

    // 调试输出：确认提交的数据包含照片字段和邮件
    console.log('提交的表单数据:', submitData)
    console.log('身份证正面照片:', submitData.idCardFrontPhoto)
    console.log('身份证反面照片:', submitData.idCardVersoPhoto)
    console.log('邮件地址:', submitData.eMail)
    console.log('数字字段处理:', {
      dateOfBirth: submitData.dateOfBirth,
      height: submitData.height,
      weight: submitData.weight,
      bust: submitData.bust,
      waist: submitData.waist,
      hips: submitData.hips,
    })

    const result = await actorStore.saveActorInfo(submitData)
    if (result.status) {
      showToast(t('save_success'))
      // 可以选择返回上一页或刷新数据
      router.back()
    } else {
      showToast(result.message || t('save_failed'))
    }
  } catch (error) {
    showToast(t('save_failed'))
  }
}

// 取消操作
const handleBack = () => {
  router.back()
}

// 身份证正面照片上传处理
const handleIdCardFrontChange = value => {
  console.log('身份证正面照片:', value)
  formData.idCardFrontPhoto = value
}

// 身份证反面照片上传处理
const handleIdCardVersoChange = value => {
  console.log('身份证反面照片:', value)
  formData.idCardVersoPhoto = value
}

// 身份证号码输入处理
const handleIdNumberInput = value => {
  // 格式化输入（可选，如果需要自动格式化）
  // formData.idNumber = formatIdCardInput(value)
}

// 身份证号码失焦处理（联动其他字段）
const handleIdNumberBlur = () => {
  const idNumber = formData.idNumber.replace(/\s/g, '') // 移除空格

  if (!idNumber) {
    return
  }

  // 验证身份证号码
  if (!validateIdCard(idNumber)) {
    showToast('身份证号码格式不正确')
    return
  }

  // 解析身份证信息
  const idCardInfo = parseIdCard(idNumber)
  if (!idCardInfo) {
    showToast('身份证号码解析失败')
    return
  }

  // 自动填充生日年份
  if (idCardInfo.birth && !formData.dateOfBirth) {
    formData.dateOfBirth = idCardInfo.birth.year
  }

  // 自动填充性别
  if (idCardInfo.gender && !formData.gender) {
    formData.gender = idCardInfo.gender
  }

  // 判断是否为未成年演员
  if (idCardInfo.isMinor !== null) {
    formData.isChildActor = idCardInfo.isMinor
  }
}

// 加载演员信息
const loadActorInfo = async () => {
  try {
    const result = await actorStore.fetchActorInfo()
    if (result.status) {
      // 将获取到的数据填充到表单中，使用store中已处理的数据

      originIdCardFrontPhoto.value = result?.lastData?.idCardFrontPhoto
      originIdCardVersoPhoto.value = result?.lastData?.idCardVersoPhoto
      // 使用 Object.assign 来更新 reactive 对象
      Object.assign(formData, result.lastData || actorStore.actorInfo)
      userEmail.value = formData.eMail || userEmail.value
      formData.idCardFrontPhoto = ''
      formData.idCardVersoPhoto = ''
    } else if (result.message) {
      showToast(result.message)
    }
  } catch (error) {
    showToast(t('load_failed'))
  }
}

// 页面初始化
onMounted(() => {
  // 检查登录状态
  if (!localStorage.getItem('token')) {
    router.push('/login')
    return
  }

  // 初始化用户信息（确保能获取到email等信息）
  loginStore.initUserInfo()

  // 初始化邮箱值
  initUserEmail()

  // 加载演员信息
  loadActorInfo()
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.form {
  background: #fff;
  border-radius: 16px 16px 0px 0px;
  padding-top: 12px;

  :deep(.van-cell-group__title) {
    color: #000;
    font-weight: bold;
  }
}

.buttons {
  padding: 36px 24px;
}
</style>
