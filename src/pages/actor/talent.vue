<template>
  <div class="bg-primary">
    <van-nav-bar title="才艺展示" :border="false" left-arrow @click-left="handleBack" />
    <div class="actor-profile-page">
      <div class="profile-container">
        <!-- 左侧资料类目导航 -->
        <div class="category-sidebar">
          <div class="category-list">
            <div
              v-for="category in mediaCategories"
              :key="category.value"
              class="category-item"
              :class="{ active: selectedCategory === category.value }"
              @click="selectCategory(category.value)">
              <van-icon :name="category.icon" class="category-icon" />
              <span class="category-label">{{ category.label }}</span>
              <van-badge
                v-if="categoryCounts[category.value] > 0"
                :content="categoryCounts[category.value]"
                class="category-badge" />
            </div>

            <!-- 代表作品 -->
            <div
              class="category-item"
              :class="{ active: selectedCategory === 'works' }"
              @click="selectCategory('works')">
              <van-icon name="star-o" class="category-icon" />
              <span class="category-label">代表作品</span>
              <van-badge
                v-if="workInfos.length > 0"
                :content="workInfos.length"
                class="category-badge" />
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="content-area">
          <div class="content-header">
            <strong class="title-3">{{ currentCategoryLabel }}</strong>
            <van-button type="primary" size="small" icon="plus" @click="handleAddMedia" plain>
              添加{{ currentCategoryLabel }}
            </van-button>
          </div>

          <div class="content-body">
            <!-- 媒体内容展示 -->
            <div v-if="selectedCategory !== 'works'" class="media-grid">
              <div v-if="currentMediaList.length === 0" class="empty-state">
                <van-empty description="暂无数据" />
              </div>
              <div v-else class="media-items">
                <!-- 图片类型 -->
                <template v-if="currentCategoryConfig?.component === 'image'">
                  <template v-for="media in currentMediaList" :key="media.id">
                    <div
                      v-for="(url, urlIndex) in getUrlArray(media.mediaUrl)"
                      :key="`${media.id}-${urlIndex}`"
                      class="media-item image-item">
                      <van-image
                        :src="getFullUrl(url)"
                        fit="cover"
                        position="center"
                        class="media-image"
                        radius="6"
                        @click="previewImages(media.mediaUrl, urlIndex)"
                        :show-error="true"
                        :show-loading="true"
                        error-icon="photo-fail"
                        loading-icon="photo" />
                      <div class="media-actions">
                        <van-icon
                          name="delete-o"
                          @click="handleDeleteSingleUrl(media, url.trim())" />
                      </div>
                    </div>
                  </template>
                </template>

                <!-- 视频类型 -->
                <template v-if="currentCategoryConfig?.component === 'video'">
                  <template v-for="media in currentMediaList" :key="media.id">
                    <div
                      v-for="(url, urlIndex) in getUrlArray(media.mediaUrl)"
                      :key="`${media.id}-${urlIndex}`"
                      class="media-item video-item">
                      <VideoThumbnail
                        :video-url="url.trim()"
                        width="100%"
                        @click="handleVideoClick(url.trim(), urlIndex)" />
                      <div class="media-actions">
                        <van-icon
                          name="delete-o"
                          @click="handleDeleteSingleUrl(media, url.trim())" />
                      </div>
                      <div v-if="media.label" class="media-label">{{ media.label }}</div>
                    </div>
                  </template>
                </template>

                <!-- 文件类型 -->
                <template v-if="currentCategoryConfig?.component === 'file'">
                  <div
                    v-for="media in currentMediaList"
                    :key="media.id"
                    class="media-item file-item">
                    <div class="file-info" @click="downloadFile(media.mediaUrl)">
                      <van-icon name="description" class="file-icon" />
                      <span class="file-name">{{ media.description || '简历' }}</span>
                    </div>
                    <div class="media-actions">
                      <van-icon name="delete-o" @click="deleteMedia(media.id)" />
                    </div>
                  </div>
                </template>

                <!-- 报价类型 -->
                <template v-if="currentCategoryConfig?.component === 'inputNumber'">
                  <div
                    v-for="media in currentMediaList"
                    :key="media.id"
                    class="media-item price-item">
                    <div class="price-info">
                      <span class="price-amount">{{ formatPrice(media.mediaUrl) }}</span>
                      <span v-if="media.description" class="price-desc">{{
                        media.description
                      }}</span>
                    </div>
                    <div class="media-actions">
                      <van-icon name="delete-o" @click="deleteMedia(media.id)" />
                    </div>
                  </div>
                </template>
              </div>
            </div>

            <!-- 代表作品展示 -->
            <div v-else class="works-list">
              <div v-if="workInfos.length === 0" class="empty-state">
                <van-empty description="暂无代表作品" />
              </div>
              <div v-else class="work-items">
                <div v-for="work in sortedWorkInfos" :key="work.id" class="work-item">
                  <div class="work-content">
                    <div class="work-title">
                      <van-icon v-if="work.isHit" name="fire-o" color="#ff4444" />
                      <span class="work-name">《{{ work.workName }}》</span>
                      <van-tag v-if="work.workType" size="small">{{ work.workType }}</van-tag>
                    </div>
                    <div v-if="work.roleType" class="work-role">
                      {{ getRoleTypeName(work.roleType) }}
                    </div>
                    <div v-if="work.roleDescription" class="work-desc">
                      {{ work.roleDescription }}
                    </div>
                  </div>
                  <div class="work-actions">
                    <van-icon name="edit" @click="editWork(work)" />
                    <van-icon name="delete-o" @click="deleteWork(work.id)" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加媒体弹窗 -->
    <van-popup
      v-model:show="showAddModal"
      position="bottom"
      closeable
      close-icon-position="top-right"
      :style="{ height: '60%' }"
      round>
      <div class="add-modal">
        <div class="modal-header">
          <strong class="title-3">添加{{ currentCategoryLabel }}</strong>
        </div>
        <van-form @submit="handleSaveMedia">
          <!-- 文件上传 -->
          <van-field v-if="currentCategoryConfig?.component !== 'inputNumber'" label="选择文件">
            <template #input>
              <Upload
                v-model="mediaForm.mediaUrl"
                :accept="currentCategoryConfig?.accept"
                :multiple="currentCategoryConfig?.multiple"
                :max-count="currentCategoryConfig?.maxCount"
                :max-size="(currentCategoryConfig?.maxSize || 20) * 1024 * 1024"
                output-format="comma" />
            </template>
          </van-field>

          <!-- 报价输入 -->
          <van-field
            v-if="currentCategoryConfig?.component === 'inputNumber'"
            v-model="mediaForm.mediaUrl"
            type="number"
            label="报价金额"
            placeholder="请输入报价金额"
            :rules="[{ required: true, message: '请输入报价金额' }]">
            <template #right-icon>
              <span>元</span>
            </template>
          </van-field>

          <!-- 描述 -->

          <!-- 标签 -->
          <van-space direction="vertical" fill :size="24">
            <van-field
              v-model="mediaForm.description"
              label="描述"
              placeholder="请输入描述（可选）"
              type="textarea"
              rows="3" />
            <van-button type="primary" block round native-type="submit" :loading="loading">
              保存
            </van-button>
          </van-space>
        </van-form>
      </div>
    </van-popup>

    <!-- 添加作品弹窗 -->
    <van-popup
      v-model:show="showWorkModal"
      position="bottom"
      closeable
      close-icon-position="top-right"
      :style="{ height: '80%' }"
      round>
      <div class="add-modal">
        <div class="modal-header">
          <h3>{{ editingWork ? '编辑' : '添加' }}代表作品</h3>
        </div>
        <van-form @submit="handleSaveWork">
          <van-field
            v-model="workForm.workName"
            label="作品名称"
            placeholder="请输入作品名称"
            :rules="[{ required: true, message: '请输入作品名称' }]" />

          <van-field label="是否爆款">
            <template #input>
              <van-switch v-model="workForm.isHit" />
            </template>
          </van-field>

          <van-field
            v-model="selectedRoleTypeName"
            label="角色类型"
            placeholder="请选择角色类型"
            readonly
            is-link
            @click="showRoleTypePicker = true" />

          <van-field v-model="workForm.workType" label="作品类型" placeholder="如：武侠、现代等" />

          <van-field
            v-model="workForm.roleDescription"
            label="角色描述"
            placeholder="请输入角色描述"
            type="textarea"
            rows="3" />

          <van-button round type="primary" native-type="submit" :loading="loading" block>
            保存
          </van-button>
        </van-form>
      </div>
    </van-popup>

    <!-- 视频预览 -->
    <van-image-preview
      v-model:show="showVideoPreview"
      :images="currentVideoUrls"
      :start-position="currentVideoIndex"
      :close-on-click-image="false">
      <template #image="{ src }">
        <video v-if="showVideoPreview" style="width: 100%; max-height: 80vh" controls autoplay>
          <source :src="src" />
          您的浏览器不支持视频播放
        </video>
      </template>
    </van-image-preview>

    <!-- 角色类型选择器 -->
    <van-popup v-model:show="showRoleTypePicker" position="bottom" :style="{ height: '40%' }">
      <van-picker
        :columns="roleTypeColumns"
        @confirm="onRoleTypeConfirm"
        @cancel="showRoleTypePicker = false"
        title="请选择角色类型" />
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, ref, computed, watch, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showImagePreview, showConfirmDialog } from 'vant'
import { showToast } from 'vant'

import { MEDIA_TYPE_OPTIONS, ACTOR_ROLE_TYPE_CONFIG, ACTOR_ROLE_TYPE_OPTIONS } from '@/consts/index'
import { API } from '@/utils/env'
import useActorProfileStore from '@/stores/actorProfile'
import Upload from '@/components/Upload.vue'
import VideoThumbnail from '@/components/VideoThumbnail.vue'

const router = useRouter()
const actorProfileStore = useActorProfileStore()

// 响应式数据
const selectedCategory = ref(1) // 默认选择近照
const showAddModal = ref(false)
const showWorkModal = ref(false)
const editingWork = ref(null)

// 视频预览相关
const showVideoPreview = ref(false)
const currentVideoUrls = ref([])
const currentVideoIndex = ref(0)

// 角色类型选择器
const showRoleTypePicker = ref(false)

// 预计算的数据
const currentCategoryConfig = ref(null)
const currentCategoryLabel = ref('')
const currentMediaList = ref([])
const categoryCounts = ref({})
const sortedWorkInfos = ref([])

// 表单数据
const mediaForm = reactive({
  mediaUrl: '',
  description: '',
  label: '',
})

const workForm = reactive({
  workName: '',
  isHit: false,
  roleType: null, // 改为数字类型
  roleDescription: '',
  workType: '',
  sort: 1,
})

// 计算属性
const mediaCategories = computed(() => MEDIA_TYPE_OPTIONS)

const loading = computed(() => actorProfileStore.loading)

const mediaInfos = computed(() => actorProfileStore.mediaInfos)

const workInfos = computed(() => actorProfileStore.workInfos)

// 返回上一页
const handleBack = () => {
  router.back()
}

// 数据更新方法
const updateComputedData = () => {
  // 更新当前类目配置
  currentCategoryConfig.value = mediaCategories.value.find(
    cat => cat.value === selectedCategory.value
  )

  // 更新当前类目标签
  if (selectedCategory.value === 'works') {
    currentCategoryLabel.value = '代表作品'
  } else {
    currentCategoryLabel.value = currentCategoryConfig.value?.label || ''
  }

  // 更新当前媒体列表
  currentMediaList.value = mediaInfos.value.filter(
    media => media.mediaType === selectedCategory.value
  )
  console.log('currentMediaList.value=>', currentMediaList.value)

  // 更新类目计数
  const counts = {}
  mediaCategories.value.forEach(category => {
    counts[category.value] = mediaInfos.value.filter(
      media => media.mediaType === category.value
    ).length
  })
  categoryCounts.value = counts

  // 更新排序后的作品列表
  sortedWorkInfos.value = [...workInfos.value].sort((a, b) => (a.sort || 0) - (b.sort || 0))
}

// 角色类型相关计算属性
const roleTypeColumns = computed(() => {
  return ACTOR_ROLE_TYPE_OPTIONS
})

const selectedRoleTypeName = computed(() => {
  if (!workForm.roleType) return ''
  return ACTOR_ROLE_TYPE_CONFIG[workForm.roleType]?.label || ''
})

// 监听数据变化
watch(
  [selectedCategory, mediaInfos, workInfos],
  () => {
    updateComputedData()
  },
  { immediate: true, deep: true }
)

// 方法
const selectCategory = categoryValue => {
  selectedCategory.value = categoryValue
}

const getFullUrl = url => {
  if (!url) return ''
  return url.startsWith('http') ? url : API + url
}

// 解析URL数组 - 处理多个文件URL（用逗号分隔）
const getUrlArray = url => {
  if (!url) return []

  // 获取当前类目配置
  const config = currentCategoryConfig.value

  // 只有图片和视频类型才分割URL，其他类型返回单个URL
  if (config?.component === 'image' || config?.component === 'video') {
    return url
      .split(',')
      .map(u => u.trim())
      .filter(Boolean)
  }

  return [url]
}

const handleAddMedia = () => {
  if (selectedCategory.value === 'works') {
    showWorkModal.value = true
    editingWork.value = null
    resetWorkForm()
  } else {
    showAddModal.value = true
    resetMediaForm()
  }
}

const closeAddModal = () => {
  showAddModal.value = false
  resetMediaForm()
}

const closeWorkModal = () => {
  showWorkModal.value = false
  editingWork.value = null
  resetWorkForm()
}

const resetMediaForm = () => {
  mediaForm.mediaUrl = ''
  mediaForm.description = ''
  mediaForm.label = ''
}

const resetWorkForm = () => {
  workForm.workName = ''
  workForm.isHit = false
  workForm.roleType = null // 改为null
  workForm.roleDescription = ''
  workForm.workType = ''
  workForm.sort = 1
}

// 角色类型选择确认
const onRoleTypeConfirm = ({ selectedValues }) => {
  workForm.roleType = selectedValues[0]
  showRoleTypePicker.value = false
}

// 根据roleType获取角色名称
const getRoleTypeName = roleType => {
  if (!roleType) return ''
  return ACTOR_ROLE_TYPE_CONFIG[roleType]?.label || ''
}

const handleSaveMedia = async () => {
  try {
    // 验证必填字段
    if (!mediaForm.mediaUrl) {
      showToast('请选择文件或输入内容')
      return
    }

    const mediaData = {
      mediaType: selectedCategory.value,
      mediaUrl: Array.isArray(mediaForm.mediaUrl)
        ? mediaForm.mediaUrl.join(',')
        : mediaForm.mediaUrl,
      description: mediaForm.description,
      label: mediaForm.label,
    }

    // 调用store方法保存媒体信息
    const result = await actorProfileStore.saveActorMedia(mediaData)
    if (result.status) {
      showToast('保存成功')
      closeAddModal()
    } else {
      showToast(result.message || '保存失败')
    }
  } catch (error) {
    showToast('保存失败')
  }
}

const handleSaveWork = async () => {
  try {
    if (!workForm.workName) {
      showToast('请输入作品名称')
      return
    }

    const workData = {
      ...workForm,
      sort: editingWork.value ? editingWork.value.sort : workInfos.value.length + 1,
    }

    if (editingWork.value) {
      workData.id = editingWork.value.id
    }

    // 调用store方法保存作品信息
    const result = await actorProfileStore.saveActorWorks(workData)
    if (result.status) {
      showToast(editingWork.value ? '更新成功' : '添加成功')
      closeWorkModal()
    } else {
      showToast(result.message || '保存失败')
    }
  } catch (error) {
    showToast('保存失败')
  }
}

const previewImages = (mediaUrl, startIndex = 0) => {
  const urls = mediaUrl.split(',').map(url => getFullUrl(url.trim()))
  showImagePreview({
    images: urls,
    startPosition: startIndex,
  })
}

// 处理视频点击 - 使用van-image-preview播放
const handleVideoClick = (videoUrl, index = 0) => {
  // 获取当前媒体的所有视频URL
  const currentMedia = currentMediaList.value.find(
    media => media.mediaUrl && media.mediaUrl.includes(videoUrl.replace(API, ''))
  )

  if (currentMedia) {
    const urls = getUrlArray(currentMedia.mediaUrl).map(url => getFullUrl(url.trim()))
    currentVideoUrls.value = urls
    currentVideoIndex.value = urls.findIndex(url => url.includes(videoUrl.replace(API, '')))
    if (currentVideoIndex.value === -1) currentVideoIndex.value = 0
  } else {
    currentVideoUrls.value = [getFullUrl(videoUrl)]
    currentVideoIndex.value = 0
  }

  showVideoPreview.value = true
}

const downloadFile = url => {
  const fullUrl = getFullUrl(url)
  window.open(fullUrl, '_blank')
}

const formatPrice = price => {
  const num = parseFloat(price)
  if (isNaN(num)) return '未设置'
  return num % 1 === 0
    ? num.toLocaleString()
    : num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const editPrice = media => {
  mediaForm.mediaUrl = media.mediaUrl
  mediaForm.description = media.description
  mediaForm.label = media.label
  showAddModal.value = true
}

const editWork = work => {
  editingWork.value = work
  workForm.workName = work.workName
  workForm.isHit = work.isHit || false
  workForm.roleType = work.roleType || null // 改为null
  workForm.roleDescription = work.roleDescription || ''
  workForm.workType = work.workType || ''
  workForm.sort = work.sort || 1
  showWorkModal.value = true
}

// 删除单个URL（参考后台项目特殊处理逻辑）
const handleDeleteSingleUrl = async (media, urlToDelete) => {
  try {
    const currentUrls = media.mediaUrl
      ? media.mediaUrl
          .split(',')
          .map(url => url.trim())
          .filter(Boolean)
      : []
    const isLastUrl = currentUrls.length <= 1

    if (isLastUrl) {
      // 如果只有一个URL，删除整个媒体记录
      await deleteMedia(media.id)
      return
    }

    // 过滤掉要删除的URL
    const newUrls = currentUrls.filter(url => url !== urlToDelete)

    if (newUrls.length === 0) {
      // 如果删除后没有URL了，删除整个媒体记录
      await deleteMedia(media.id)
      return
    }

    // 构建更新的媒体信息，调用保存接口更新
    const updatedMediaData = {
      id: media.id,
      mediaType: media.mediaType,
      mediaUrl: newUrls.join(','),
      description: media.description || '',
      label: media.label || '',
    }

    const result = await actorProfileStore.saveActorMedia(updatedMediaData)
    if (result.status) {
      showToast('删除成功')
    } else {
      showToast(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const deleteMedia = async mediaId => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个文件吗？删除后无法恢复！',
    })

    const result = await actorProfileStore.deleteActorMedia(mediaId)
    if (result.status) {
      showToast('删除成功')
    } else {
      showToast(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const deleteWork = async workId => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: '确定要删除这个代表作品吗？删除后无法恢复！',
    })

    const result = await actorProfileStore.deleteActorWorks(workId)
    if (result.status) {
      showToast('删除成功')
    } else {
      showToast(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

// 加载演员资料数据
const loadActorProfile = async () => {
  const result = await actorProfileStore.fetchActorProfile()
  if (!result.status && result.message) {
    showToast(result.message)
  }
}

// 页面初始化
onMounted(() => {
  // 检查登录状态
  if (!localStorage.getItem('token')) {
    router.push('/login')
    return
  }

  // 加载演员资料数据
  loadActorProfile()
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.actor-profile-page {
  flex: 1;
  width: 100%;
  padding-top: 20px;
  position: relative;
  z-index: 102;
}

.profile-container {
  display: flex;
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  overflow: hidden;
  flex-direction: column;
  height: auto;
}

.category-sidebar {
  background: #f8f9fa;
  width: 100%;
  height: auto;
  max-height: 200px;
  overflow-x: auto;
  overflow-y: hidden;
  border-right: none;
  border-bottom: 1px solid #e9ecef;
}

.category-list {
  display: flex;
  padding: 8px;
  gap: 8px;
  overflow-x: auto;
}

.category-item {
  display: flex;
  flex-direction: column;
  min-width: 70px;
  align-items: center;
  padding: 4px;
  text-align: center;
  border-radius: 6px;
  position: relative;
  border: 2px solid transparent;

  &.active {
    color: #ff6b00;
    border-color: #ff6b00;

    .category-icon {
      color: #ff6b00;
    }
  }
}

.category-icon {
  color: #666;
  margin-right: 0;
  margin-bottom: 4px;
  font-size: 18px;
}

.category-label {
  flex: 1;
}

.category-badge {
  :deep(.van-badge__wrapper) {
    .van-badge {
      background: #ff4444;
      font-size: 24px;
      min-width: 40px;
      height: 40px;
      line-height: 40px;
    }
  }
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e9ecef;
}

.content-body {
  flex: 1;
  padding: 24px 12px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.media-grid {
  .media-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
}

.media-item {
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.media-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.video-item {
  // VideoThumbnail组件已经处理了样式，这里不需要额外样式
  display: flex;
  flex-direction: column;
  align-items: center;
}

.file-item {
  padding: 20px;
  text-align: center;
  border: 1px solid #ccc;
  border-radius: 6px;

  .file-info {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .file-icon {
    font-size: 40px;
    color: #666;
  }

  .file-name {
    font-size: 18px;
    color: #333;
  }
}

.price-item {
  background: white;
  padding: 20px; // 右侧留出更多空间给操作按钮
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  border-radius: 6px;

  .price-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .price-amount {
    font-size: 28px;
    font-weight: bold;
    color: #ff6b00;
    line-height: 1.2;
  }

  .price-desc {
    font-size: 32px;
    color: #666;
    line-height: 1.4;
    word-break: break-word; // 防止长文本溢出
  }
}

.media-actions {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  gap: 12px;
  opacity: 1; // 移动端默认显示

  .van-icon {
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 4px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
  }
}

.media-label {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 28px;
}

.works-list {
  .work-items {
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
}

.work-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.work-content {
  flex: 1;
}

.work-title {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;

  .work-name {
    font-size: 40px;
    font-weight: bold;
    color: #333;
  }

  :deep(.van-tag) {
    font-size: 24px;
  }
}

.work-role {
  font-size: 32px;
  color: #666;
  margin-bottom: 16px;
}

.work-desc {
  font-size: 30px;
  color: #999;
  line-height: 1.5;
}

.work-actions {
  display: flex;
  gap: 20px;
  opacity: 1; // 移动端默认显示
  transition: opacity 0.3s ease;

  .van-icon {
    font-size: 40px;
    color: #666;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #ff6b00;
    }
  }
}

// 弹窗样式
.add-modal {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-inline: 10px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0px;
  border-bottom: 1px solid #e9ecef;
}
</style>
