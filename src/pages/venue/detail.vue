<template>
  <div class="venue-detail-page">
    <!-- 导航栏 -->
    <van-nav-bar :title="venue?.venueName || '场地详情'" :border="false" left-arrow @click-left="goBack" />

    <!-- 加载状态 -->
    <van-loading v-if="loading" color="#ff6600" size="36" vertical class="loading-container">
      加载中...
    </van-loading>

    <!-- 详情内容 -->
    <div v-else-if="venue" class="detail-content">
      <!-- 基本信息 -->
      <Card class="info-card">
        <template #header>
          <div class="card-header">
            <van-icon name="info-o" size="16" />
            <span>基本信息</span>
            <van-button type="primary" size="mini" @click="showAssociateModal" class="associate-btn">
              关联项目
            </van-button>
          </div>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">场地名称</div>
            <div class="info-value">{{ venue.venueName }}</div>
          </div>

          <div class="info-item" v-if="venue.cityName">
            <div class="info-label">所在城市</div>
            <div class="info-value">{{ getCityDisplayText(venue.cityName) }}</div>
          </div>

          <div class="info-item" v-if="suitableTypeText(venue)">
            <div class="info-label">适合类型</div>
            <div class="info-value">{{ suitableTypeText(venue) }}</div>
          </div>

          <div class="info-item" v-if="venue.cost">
            <div class="info-label">拍摄费用</div>
            <div class="info-value cost-value">¥{{ venue.cost }}</div>
          </div>

          <div class="info-item" v-if="venue.contactPerson">
            <div class="info-label">联系人</div>
            <div class="info-value">{{ venue.contactPerson }}</div>
          </div>

          <div class="info-item" v-if="venue.contactPhone">
            <div class="info-label">联系电话</div>
            <div class="info-value phone-value" @click="callPhone(venue.contactPhone)">
              {{ venue.contactPhone }}
              <van-icon name="phone-o" size="14" />
            </div>
          </div>

          <div class="info-item full-width" v-if="venue.address">
            <div class="info-label">详细地址</div>
            <div class="info-value">{{ venue.address }}</div>
          </div>
        </div>
      </Card>

      <!-- 场地照片 -->
      <Card class="media-card">
        <template #header>
          <div class="card-header">
            <van-icon name="photo-o" size="16" />
            <span>场地照片</span>
            <van-button type="primary" size="mini" @click="showUploadModal('photo')" class="upload-btn">
              上传照片
            </van-button>
          </div>
        </template>

        <div v-if="photoCategories.length > 0" class="media-content">
          <div v-for="category in photoCategories" :key="category.subclass" class="media-category">
            <div class="category-title">{{ category.subclass }}</div>
            <div class="photo-grid">
              <div v-for="(url, index) in category.urls" :key="index" class="photo-item"
                @click="previewPhotos(category.urls, index)">
                <van-image :src="getImageUrl(url)" fit="cover" width="100%" height="120" radius="4">
                  <template #error>
                    <div class="image-error">
                      <van-icon name="photo-fail" size="20" />
                    </div>
                  </template>
                </van-image>
                <!-- 封面标识 -->
                <div v-if="venue.photos === url" class="cover-badge">
                  <van-icon name="star" size="12" />
                  <span>封面</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <van-empty v-else description="暂无照片" />
      </Card>

      <!-- 场地视频 -->
      <Card class="media-card">
        <template #header>
          <div class="card-header">
            <van-icon name="video-o" size="16" />
            <span>场地视频</span>
            <van-button type="primary" size="mini" @click="showUploadModal('video')" class="upload-btn">
              上传视频
            </van-button>
          </div>
        </template>

        <div v-if="videoCategories.length > 0" class="media-content">
          <div v-for="category in videoCategories" :key="category.subclass" class="media-category">
            <div class="category-title">{{ category.subclass }}</div>
            <div class="video-grid">
              <VideoThumbnail v-for="(url, index) in category.urls" :key="index" :video-url="getImageUrl(url)"
                :width="160" :height="120" @click="playVideo" class="video-thumbnail-item" />
            </div>
          </div>
        </div>
        <van-empty v-else description="暂无视频" />
      </Card>
    </div>

    <!-- 错误状态 -->
    <van-empty v-else description="场地信息不存在" />

    <!-- 图片预览 -->
    <van-image-preview v-model:show="showImagePreview" :images="previewImageList" :start-position="previewStartIndex"
      @close="showImagePreview = false" />

    <!-- 视频播放弹窗 -->
    <van-popup v-model:show="showVideoPlayer" position="center" :style="{ width: '90%', height: '60%' }" round>
      <div class="video-player-container">
        <div class="video-player-header">
          <span>视频播放</span>
          <van-icon name="cross" size="20" @click="closeVideoPlayer" />
        </div>
        <div class="video-player-content">
          <video v-if="currentVideoUrl" :src="currentVideoUrl" controls autoplay class="video-player">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>
    </van-popup>

    <!-- 上传弹窗 -->
    <van-popup v-model:show="showUploadPopup" position="bottom" :style="{ height: '60%' }" round closeable>
      <div class="upload-container">
        <div class="upload-header">
          <h3>{{ uploadType === 'photo' ? '上传照片' : '上传视频' }}</h3>
        </div>

        <van-form @submit="confirmUpload" ref="uploadFormRef" class="upload-content">
          <van-cell-group>
            <!-- 分类选择 -->
            <van-field v-model="uploadForm.subclass" name="subclass" label="分类名称" placeholder="请输入分类名称"
              :rules="[{ required: true, message: '请输入分类名称' }]" required />

            <!-- 文件上传 -->
            <van-field name="files" label="选择文件" :rules="[{
              required: true,
              message: '请选择文件',
              validator: () => !!uploadForm.files && uploadForm.files.trim() !== ''
            }]" required>
              <template #input>
                <Upload v-model="uploadForm.files" :accept="uploadType === 'photo' ? 'image/*' : 'video/*'"
                  :multiple="true" :max-count="5"
                  :max-size="uploadType === 'photo' ? 20 * 1024 * 1024 : 100 * 1024 * 1024" output-format="comma" />
              </template>
            </van-field>
          </van-cell-group>

          <!-- 操作按钮 -->
          <div class="upload-actions">
            <van-button @click="cancelUpload" size="large" block>
              取消
            </van-button>
            <van-button type="primary" native-type="submit" size="large" block :loading="uploading">
              确认上传
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 关联项目弹窗 -->
    <van-popup v-model:show="showAssociatePopup" position="bottom" :style="{ height: '70%' }" round closeable>
      <div class="associate-container">
        <div class="associate-header">
          <h3>关联项目</h3>
        </div>

        <van-form @submit="confirmAssociate" ref="associateFormRef" class="associate-content">
          <van-cell-group>
            <!-- 当前场地的子分类 -->
            <van-field v-model="associateForm.venueSubclass" name="venueSubclass" label="场地子分类" placeholder="请选择场地子分类"
              :rules="[{ required: true, message: '请选择场地子分类' }]" required readonly @click="handleVenueSubclassClick" />
            <!-- 关联项目选择 -->
            <van-field v-model="associateForm.projectText" name="project" label="关联项目" placeholder="请选择关联项目"
              :rules="[{ required: true, message: '请选择关联项目' }]" required readonly @click="handleProjectClick" />

            <!-- 关联主场景选择 -->
            <van-field v-model="associateForm.mainSceneText" name="mainScene" label="关联主场景" placeholder="请选择关联主场景"
              :rules="[{ required: true, message: '请选择关联主场景' }]" required readonly @click="handleMainSceneClick"
              :disabled="!associateForm.projectId" />

            <!-- 关联子场景选择 -->
            <van-field v-model="associateForm.subSceneText" name="subScene" label="关联子场景" placeholder="请选择关联子场景"
              readonly @click="handleSubSceneClick" required :disabled="!associateForm.mainScene" />


          </van-cell-group>

          <!-- 操作按钮 -->
          <div class="associate-actions">
            <van-button @click="cancelAssociate" size="large" block>
              取消
            </van-button>
            <van-button type="primary" native-type="submit" size="large" block :loading="associating">
              确认关联
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 项目选择器 -->
    <van-popup v-model:show="showProjectPicker" position="bottom" round>
      <van-picker :columns="projectColumns" @confirm="onProjectConfirm" @cancel="showProjectPicker = false" />
    </van-popup>

    <!-- 主场景选择器 -->
    <van-popup v-model:show="showMainScenePicker" position="bottom" round>
      <van-picker :columns="mainSceneColumns" @confirm="onMainSceneConfirm" @cancel="showMainScenePicker = false" />
    </van-popup>

    <!-- 子场景选择器 -->
    <van-popup v-model:show="showSubScenePicker" position="bottom" round>
      <van-picker :columns="subSceneColumns" @confirm="onSubSceneConfirm" @cancel="showSubScenePicker = false" />
    </van-popup>

    <!-- 场地子分类选择器 -->
    <van-popup v-model:show="showVenueSubclassPicker" position="bottom" round>
      <van-picker :columns="venueSubclassColumns" @confirm="onVenueSubclassConfirm"
        @cancel="showVenueSubclassPicker = false" />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import Card from '@/components/Card.vue'
import VideoThumbnail from '@/components/VideoThumbnail.vue'
import Upload from '@/components/Upload.vue'
import useVenueStore from '@/stores/venue'
import useVenueAssociationStore from '@/stores/venueAssociation'
import { ImageApi } from '@/utils/env'
import {
  getVenueSuitableTypeText,
  getCityText
} from '@/consts'
import useLoginStore from '@/stores/login'


const router = useRouter()
const route = useRoute()
const venueStore = useVenueStore()
const venueAssociationStore = useVenueAssociationStore()

// 响应式数据
const venue = ref(null)
const mediaList = ref([])
const loading = ref(false)
const showImagePreview = ref(false)
const previewImageList = ref([])
const previewStartIndex = ref(0)
const showVideoPlayer = ref(false)
const currentVideoUrl = ref('')

// 上传相关数据
const showUploadPopup = ref(false)
const uploadType = ref('photo') // 'photo' | 'video'
const uploading = ref(false)
const uploadFormRef = ref(null)
const uploadForm = reactive({
  subclass: '',
  files: ''
})

// 关联项目相关数据
const showAssociatePopup = ref(false)
const associating = ref(false)
const associateFormRef = ref(null)
const associateForm = reactive({
  projectId: null,
  projectText: '',
  mainScene: '',
  mainSceneText: '',
  subScene: '',
  subSceneText: '',
  venueSubclass: ''
})

// 选择器相关数据
const showProjectPicker = ref(false)
const showMainScenePicker = ref(false)
const showSubScenePicker = ref(false)
const showVenueSubclassPicker = ref(false)

// 计算属性
const photoCategories = computed(() => {
  return mediaList.value
    .filter(m => m.mediaType === 1 && !m.isDelete)
    .map(photo => ({
      subclass: photo.subclass || '默认',
      id: photo.id || 0,
      urls: (photo.mediaUrl || '')
        .split(',')
        .map(url => url.trim())
        .filter(Boolean),
    }))
})

const videoCategories = computed(() => {
  return mediaList.value
    .filter(m => m.mediaType === 2 && !m.isDelete)
    .map(video => ({
      subclass: video.subclass || '默认',
      id: video.id || 0,
      urls: (video.mediaUrl || '')
        .split(',')
        .map(url => url.trim())
        .filter(Boolean),
    }))
})

// 项目选择器列表
const projectColumns = computed(() => {
  return venueAssociationStore.getProjectList.map(project => ({
    text: project.productionName,
    value: project.id
  }))
})

// 主场景选择器列表
const mainSceneColumns = computed(() => {
  const mainScenes = venueAssociationStore.getMainScenes(associateForm.projectId)
  return mainScenes.map(mainScene => ({
    text: mainScene,
    value: mainScene
  }))
})

// 子场景选择器列表
const subSceneColumns = computed(() => {
  const subScenes = venueAssociationStore.getSubScenes(associateForm.projectId, associateForm.mainScene)
  return subScenes.map(subScene => ({
    text: subScene,
    value: subScene
  }))
})

// 场地子分类选择器列表
const venueSubclassColumns = computed(() => {
  const venueId = route.params.id
  const subclasses = venueStore.getVenueClassMap[venueId] || []
  return subclasses.map(subclass => ({
    text: subclass,
    value: subclass
  }))
})
const loginStore = useLoginStore()

// 方法
const goBack = () => {
  router.back()
}

// 获取场地详情
const fetchVenueDetail = async (venueId) => {
  if (!venueId) {
    showToast('场地ID不存在')
    return
  }

  loading.value = true
  try {
    // 使用新的接口获取场地详情
    const result = await venueStore.getVenueById(venueId)
    if (result.status && result.data) {
      venue.value = result.data
      // 获取媒体列表
      await fetchMediaList(venueId)
    } else {
      showToast(result.message || '场地不存在')
    }
  } catch (error) {
    console.error('获取场地详情失败:', error)
    showToast('获取场地详情失败')
  } finally {
    loading.value = false
  }
}

// 获取媒体列表
const fetchMediaList = async (venueId) => {
  try {
    const result = await venueStore.getVenueMediaByVenueId(venueId)
    if (result.status) {
      mediaList.value = result.data || []
    }
  } catch (error) {
    console.error('获取媒体列表失败:', error)
  }
}

// 获取图片URL
const getImageUrl = (url) => {
  if (!url) return ''
  // 确保url是字符串类型
  const urlStr = String(url).trim()
  if (!urlStr) return ''
  return urlStr.startsWith('http') ? urlStr : `${ImageApi}${urlStr}`
}

// 获取适合类型文本
const suitableTypeText = (venue) => {
  if (!venue.suitableType) return ''
  const types = venue.suitableType.split(',').filter(type => type.trim())
  return types.map(type => getVenueSuitableTypeText(type)).join('、')
}

// 获取城市显示文本
const getCityDisplayText = (cityName) => {
  return getCityText(cityName) || cityName
}



// 预览照片
const previewPhotos = (urls, startIndex = 0) => {
  previewImageList.value = urls.map(url => getImageUrl(url))
  previewStartIndex.value = startIndex
  showImagePreview.value = true
}

// 播放视频
const playVideo = (videoData) => {
  // VideoThumbnail组件传递的是对象 { videoUrl, thumbnail }
  const videoUrl = typeof videoData === 'string' ? videoData : videoData?.videoUrl
  currentVideoUrl.value = getImageUrl(videoUrl)
  showVideoPlayer.value = true
}

// 关闭视频播放器
const closeVideoPlayer = () => {
  showVideoPlayer.value = false
  currentVideoUrl.value = ''
}



// 拨打电话
const callPhone = (phone) => {
  window.location.href = `tel:${phone}`
}

// 显示上传弹窗
const showUploadModal = (type) => {
  uploadType.value = type
  resetUploadForm()
  showUploadPopup.value = true
}

// 重置上传表单
const resetUploadForm = () => {
  uploadForm.subclass = ''
  uploadForm.files = ''
  uploadFormRef.value?.resetValidation()
}

// 取消上传
const cancelUpload = () => {
  showUploadPopup.value = false
  resetUploadForm()
}

// 确认上传
const confirmUpload = async () => {
  // 表单验证
  try {
    await uploadFormRef.value?.validate()
  } catch (error) {
    console.log('表单验证失败:', error)
    return
  }

  if (!uploadForm.subclass || !uploadForm.files) {
    showToast('请填写完整信息')
    return
  }

  uploading.value = true
  try {
    const venueId = route.params.id
    const mediaType = uploadType.value === 'photo' ? 1 : 2

    // 构建媒体对象，与后台项目保持一致
    const mediaObject = {
      venueId: parseInt(venueId),
      mediaType,
      subclass: uploadForm.subclass,
      mediaUrl: uploadForm.files,
      isDelete: false
    }

    const result = await venueStore.saveVenueMedia({
      venueId: parseInt(venueId),
      medias: [mediaObject]
    })

    if (result.status) {
      showToast('上传成功')
      showUploadPopup.value = false
      // 重新获取媒体列表
      await fetchMediaList(venueId)
      // 重置表单
      resetUploadForm()
    } else {
      showToast(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    showToast('上传失败')
  } finally {
    uploading.value = false
  }
}

// 显示关联项目弹窗
const showAssociateModal = async () => {
  // 获取项目列表
  const personType = loginStore.getCurrentPersonType

  const result = await venueAssociationStore.fetchProjectList(personType)
  if (!result.status) {
    showToast(result.message || '获取项目列表失败')
    return
  }
  resetAssociateForm()
  showAssociatePopup.value = true
}

// 重置关联表单
const resetAssociateForm = () => {
  associateForm.projectId = null
  associateForm.projectText = ''
  associateForm.mainScene = ''
  associateForm.mainSceneText = ''
  associateForm.subScene = ''
  associateForm.subSceneText = ''
  associateForm.venueSubclass = ''
  associateFormRef.value?.resetValidation()
}

// 取消关联
const cancelAssociate = () => {
  showAssociatePopup.value = false
  resetAssociateForm()
}

// 项目选择确认
const onProjectConfirm = async ({ selectedOptions }) => {
  const selectedProject = selectedOptions[0]
  associateForm.projectId = selectedProject.value
  associateForm.projectText = selectedProject.text

  // 重置场景选择
  associateForm.mainScene = ''
  associateForm.mainSceneText = ''
  associateForm.subScene = ''
  associateForm.subSceneText = ''

  // 获取该项目的场景数据
  const result = await venueAssociationStore.fetchProjectScenes(selectedProject.value)

  if (!result.status) {
    showToast('获取场景数据失败')
  }

  showProjectPicker.value = false
}

// 主场景选择确认
const onMainSceneConfirm = ({ selectedOptions }) => {
  const selectedMainScene = selectedOptions[0]
  associateForm.mainScene = selectedMainScene.value
  associateForm.mainSceneText = selectedMainScene.text

  // 重置子场景选择
  associateForm.subScene = ''
  associateForm.subSceneText = ''

  showMainScenePicker.value = false
}

// 子场景选择确认
const onSubSceneConfirm = ({ selectedOptions }) => {
  const selectedSubScene = selectedOptions[0]
  associateForm.subScene = selectedSubScene.value
  associateForm.subSceneText = selectedSubScene.text

  showSubScenePicker.value = false
}

// 处理场地子分类点击
const handleVenueSubclassClick = () => {
  if (venueSubclassColumns.value.length === 0) {
    showToast('暂无场地子分类数据')
    return
  }
  showVenueSubclassPicker.value = true
}

// 处理项目选择点击
const handleProjectClick = () => {
  if (projectColumns.value.length === 0) {
    showToast('暂无项目数据')
    return
  }
  showProjectPicker.value = true
}

// 处理主场景选择点击
const handleMainSceneClick = () => {
  if (!associateForm.projectId) {
    showToast('请先选择项目')
    return
  }
  if (mainSceneColumns.value.length === 0) {
    showToast('该项目暂无主场景数据')
    return
  }
  showMainScenePicker.value = true
}

// 处理子场景选择点击
const handleSubSceneClick = () => {
  if (!associateForm.mainScene) {
    showToast('请先选择主场景')
    return
  }
  if (subSceneColumns.value.length === 0) {
    showToast('该主场景暂无子场景数据')
    return
  }
  showSubScenePicker.value = true
}

// 场地子分类选择确认
const onVenueSubclassConfirm = ({ selectedOptions }) => {
  const selectedSubclass = selectedOptions[0]
  associateForm.venueSubclass = selectedSubclass.value

  showVenueSubclassPicker.value = false
}

// 确认关联
const confirmAssociate = async () => {
  // 表单验证
  try {
    await associateFormRef.value?.validate()
  } catch (error) {
    console.log('表单验证失败:', error)
    return
  }

  if (!associateForm.projectId || !associateForm.mainScene || !associateForm.venueSubclass) {
    showToast('请填写完整信息')
    return
  }

  associating.value = true
  try {
    const venueId = route.params.id

    // 构建关联数据
    const associationData = {
      productionId: associateForm.projectId,
      venueId: parseInt(venueId),
      subVenueName: associateForm.venueSubclass,
      mainSceneName: associateForm.mainScene,
      subSceneNames: associateForm.subScene ? [associateForm.subScene] : []
    }

    const result = await venueAssociationStore.saveVenueAssociation(associationData)

    if (result.status) {
      showToast('关联成功')
      showAssociatePopup.value = false
      resetAssociateForm()

      // 重新获取场地详情以更新关联项目显示
      await fetchVenueDetail(venueId)
    } else {
      showToast(result.message || '关联失败')
    }
  } catch (error) {
    console.error('关联失败:', error)
    showToast('关联失败')
  } finally {
    associating.value = false
  }
}

// 页面初始化
onMounted(() => {
  const venueId = route.params.id
  if (venueId) {
    fetchVenueDetail(venueId)
  } else {
    showToast('场地ID不存在')
    router.back()
  }
})
</script>

<style scoped lang="scss">
.venue-detail-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.loading-container {
  padding: 60px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.detail-content {
  padding: 0 16px 16px;
}

.info-card,
.media-card {
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;

  .upload-btn,
  .associate-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  .info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;

    &.full-width {
      grid-column: 1 / -1;
    }

    .info-label {
      font-size: 13px;
      color: #666;
      font-weight: 500;
    }

    .info-value {
      font-size: 14px;
      color: #333;
      line-height: 1.4;

      &.cost-value {
        color: #ff6600;
        font-weight: 600;
      }

      &.phone-value {
        color: #1890ff;
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;

        &:active {
          opacity: 0.7;
        }
      }
    }
  }
}

.media-content {
  .media-category {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .category-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 12px;
      padding-bottom: 6px;
      border-bottom: 1px solid #eee;
    }
  }
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;

  .photo-item {
    position: relative;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;

    .image-error {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 120px;
      background: #f5f5f5;
      color: #ccc;
    }

    .cover-badge {
      position: absolute;
      top: 4px;
      right: 4px;
      background: rgba(255, 102, 0, 0.9);
      color: white;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
      display: flex;
      align-items: center;
      gap: 2px;
    }

    &:active {
      opacity: 0.8;
    }
  }
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;

  .video-thumbnail-item {
    border-radius: 6px;
    overflow: hidden;
  }
}

.video-player-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .video-player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 600;

    .van-icon {
      cursor: pointer;
    }
  }

  .video-player-content {
    flex: 1;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;

    .video-player {
      width: 100%;
      height: 100%;
      max-height: 300px;
    }
  }
}

// 上传弹窗样式
.upload-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .upload-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    text-align: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .upload-content {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .upload-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-top: auto;
    }
  }
}

// 关联项目弹窗样式
.associate-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .associate-header {
    padding: 16px;
    border-bottom: 1px solid #eee;
    text-align: center;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .associate-content {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    .associate-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-top: auto;
    }
  }
}
</style>
