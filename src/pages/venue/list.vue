<template>
    <div class="venue-list-page">
        <!-- 导航栏 -->
        <van-nav-bar title="场地管理" :border="false" left-arrow @click-left="goBack">
            <template #right>
                <van-button type="primary" size="small" @click="handleAddVenue" class="add-btn">
                    新增
                </van-button>
            </template>
        </van-nav-bar>

        <!-- 搜索区域 -->
        <div class="search-section">
            <van-search v-model="searchForm.venueName" placeholder="搜索场地名称" @search="handleSearch"
                @clear="handleClearSearch" />
        </div>

        <!-- 场地列表 -->
        <div class="venue-list">
            <van-loading v-if="loading" color="#ff6600" size="36" vertical>加载中...</van-loading>
            <van-empty v-else-if="!venueList || venueList.length === 0" description="暂无场地数据" />

            <div v-else class="list-content">
                <Card v-for="venue in venueList" :key="venue.id" @click="handleVenueClick(venue)" class="venue-card">
                    <template #header>
                        <div class="venue-header">
                            <div class="venue-name">{{ venue.venueName }}</div>
                            <div class="venue-actions">
                                <div class="venue-cost" v-if="venue.cost">
                                    ¥{{ venue.cost }}
                                </div>
                                <van-button v-if="hasVenueManagementPermission" type="primary" size="mini" icon="edit"
                                    @click.stop="handleEditVenue(venue)" class="edit-btn">
                                    编辑
                                </van-button>
                            </div>
                        </div>
                    </template>

                    <div class="venue-content">
                        <!-- 场地图片 -->
                        <div class="venue-image" v-if="venue.photos">
                            <van-image :src="getImageUrl(venue.photos)" fit="cover" width="80" height="80" radius="4"
                                @click.stop="previewImages(venue)">
                                <template #error>
                                    <div class="image-error">
                                        <van-icon name="photo-fail" size="20" />
                                    </div>
                                </template>
                            </van-image>
                        </div>
                        <div class="venue-image-placeholder" v-else>
                            <van-icon name="photo" size="24" color="#ccc" />
                        </div>

                        <!-- 场地信息 -->
                        <div class="venue-info">
                            <div class="info-row" v-if="venue.cityName">
                                <van-icon name="location-o" size="14" color="#999" />
                                <span class="info-text">{{ getCityDisplayText(venue.cityName) }}</span>
                            </div>

                            <div class="info-row" v-if="venue.address">
                                <van-icon name="location" size="14" color="#999" />
                                <span class="info-text">{{ venue.address }}</span>
                            </div>

                            <div class="info-row" v-if="suitableTypeText(venue)">
                                <van-icon name="label-o" size="14" color="#999" />
                                <span class="info-text">{{ suitableTypeText(venue) }}</span>
                            </div>

                            <div class="info-row" v-if="venue.contactPerson">
                                <van-icon name="contact" size="14" color="#999" />
                                <span class="info-text">{{ venue.contactPerson }}</span>
                                <span class="info-text" v-if="venue.contactPhone">{{ venue.contactPhone }}</span>
                            </div>

                            <div class="info-row time-info">
                                <van-icon name="clock-o" size="14" color="#999" />
                                <span class="info-text">
                                    {{ venue.updateTime ? '更新于' : '创建于' }}
                                    {{ formatDate(venue.updateTime || venue.createTime) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </Card>
            </div>
        </div>

        <!-- 分页加载 -->
        <div class="pagination-section" v-if="venueList && venueList.length > 0">
            <van-button v-if="hasMore" @click="loadMore" :loading="loading" type="primary" size="small" block>
                加载更多
            </van-button>
            <div v-else class="no-more-text">没有更多数据了</div>
        </div>



        <!-- 图片预览 -->
        <van-image-preview v-model:show="showImagePreview" :images="previewImageList"
            @close="showImagePreview = false" />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import Card from '@/components/Card.vue'
import useVenueStore from '@/stores/venue'
import { API } from '@/utils/env'
import {
    getVenueSuitableTypeText,
    getCityText,
    VENUE_MANAGEMENT_ALLOWED_ROLES
} from '@/consts'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'

const router = useRouter()
const venueStore = useVenueStore()
const loginStore = useLoginStore()
const homeStore = useHomeStore()

// 响应式数据
const showImagePreview = ref(false)
const previewImageList = ref([])

// 搜索表单
const searchForm = reactive({
    venueName: '',
})

// 计算属性
const venueList = computed(() => venueStore.getValidVenueList)
const loading = computed(() => venueStore.loading)
const pagination = computed(() => venueStore.getPagination)

// 是否还有更多数据
const hasMore = computed(() => {
    const { current, pageSize, total } = pagination.value
    return current * pageSize < total
})

// 检查是否有场地管理权限
const hasVenueManagementPermission = computed(() => {
    // 只有工作人员角色才能访问场地管理
    const currentRole = loginStore.getCurrentRole
    if (currentRole !== 'staff') {
        return false
    }

    // 获取当前用户的角色类型
    const accountInfo = homeStore.accountInfo
    const currentRoleType = accountInfo?.roleType
    if (!currentRoleType) {
        return false
    }

    // 检查角色类型是否在允许的列表中
    return VENUE_MANAGEMENT_ALLOWED_ROLES.includes(currentRoleType)
})

// 方法
const goBack = () => {
    router.back()
}

// 搜索
const handleSearch = async () => {
    await loadVenueList(true)
}

// 清空搜索
const handleClearSearch = () => {
    searchForm.venueName = ''
    handleSearch()
}

// 加载场地列表
const loadVenueList = async (reset = false) => {
    try {

        // 设置搜索参数
        venueStore.setSearchParams({
            venueName: searchForm.venueName,
        })

        // 如果是重置，则重置分页
        if (reset) {
            venueStore.setPagination({ current: 1 })
        }
        const result = await venueStore.fetchVenueList()

        if (!result.status) {
            showToast(result.message || '获取场地列表失败')
        }
    } catch (error) {

        console.error('加载场地列表失败:', error)
        showToast('加载失败，请重试')
    }
}

// 加载更多
const loadMore = async () => {
    if (loading.value || !hasMore.value) return

    try {
        const nextPage = pagination.value.current + 1
        venueStore.setPagination({ current: nextPage })

        const result = await venueStore.fetchVenueList()

        if (!result.status) {
            showToast(result.message || '加载更多失败')
        }
    } catch (error) {
        console.error('加载更多失败:', error)
        showToast('加载失败，请重试')
    }
}

// 点击场地卡片
const handleVenueClick = (venue) => {
    // 跳转到场地详情页面
    router.push(`/venue/detail/${venue.id}`)
}

// 新增场地
const handleAddVenue = () => {
    router.push('/venue/add')
}

// 编辑场地
const handleEditVenue = (venue) => {
    // 检查权限
    if (!hasVenueManagementPermission.value) {
        showToast('您没有权限编辑场地')
        return
    }

    router.push(`/venue/edit/${venue.id}`)
}

// 获取图片URL
const getImageUrl = (photos) => {
    if (!photos) return ''
    const firstPhoto = photos.split(',')[0]
    return firstPhoto.startsWith('http') ? firstPhoto : `${API}${firstPhoto}`
}

// 预览图片
const previewImages = (venue) => {
    if (!venue.photos) return

    const photos = venue.photos.split(',').filter(photo => photo.trim())
    previewImageList.value = photos.map(photo =>
        photo.startsWith('http') ? photo : `${API}${photo}`
    )
    showImagePreview.value = true
}

// 获取适合类型文本
const suitableTypeText = (venue) => {
    if (!venue.suitableType) return ''
    const types = venue.suitableType.split(',').filter(type => type.trim())
    return types.map(type => getVenueSuitableTypeText(type)).join('、')
}

// 获取城市显示文本
const getCityDisplayText = (cityName) => {
    return getCityText(cityName) || cityName
}

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    })
}

// 页面初始化
onMounted(() => {
    loadVenueList(true)
})
</script>

<style scoped lang="scss">
.venue-list-page {
    min-height: 100vh;
    background-color: #f5f5f5;
}

// 新增按钮样式
.add-btn {
    font-size: 14px;
    padding: 4px 12px;
    height: auto;
}

.search-section {
    background: #fff;
    padding: 0;
    margin-bottom: 8px;

    .van-search {
        padding: 12px 16px;
    }
}

.venue-list {
    padding: 0 16px;

    .list-content {
        .venue-card {
            margin-bottom: 12px;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.venue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .venue-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        flex: 1;
    }

    .venue-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .venue-cost {
            font-size: 14px;
            font-weight: 600;
            color: #ff6600;
        }

        .edit-btn {
            font-size: 12px;
            padding: 2px 8px;
            height: auto;
            min-width: auto;
        }
    }
}

.venue-content {
    display: flex;
    gap: 12px;

    .venue-image,
    .venue-image-placeholder {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        border-radius: 4px;
        overflow: hidden;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;

        .image-error {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            color: #ccc;
        }
    }

    .venue-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 6px;

        .info-row {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;

            .info-text {
                color: #666;
                line-height: 1.4;

                &:not(:last-child) {
                    margin-right: 8px;
                }
            }

            &.time-info {
                margin-top: auto;

                .info-text {
                    color: #999;
                    font-size: 12px;
                }
            }
        }
    }
}

.pagination-section {
    padding: 16px;
    text-align: center;

    .no-more-text {
        color: #999;
        font-size: 14px;
        padding: 12px 0;
    }
}

// 加载状态
.van-loading {
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

// 空状态
.van-empty {
    padding: 40px 0;
}



// 响应式适配
@media (max-width: 375px) {
    .venue-content {

        .venue-image,
        .venue-image-placeholder {
            width: 70px;
            height: 70px;
        }
    }

    .venue-header {
        .venue-name {
            font-size: 15px;
        }

        .venue-cost {
            font-size: 13px;
        }
    }

    .venue-info {
        .info-row {
            font-size: 12px;
        }
    }
}
</style>