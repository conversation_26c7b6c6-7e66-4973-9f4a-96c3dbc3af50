<template>
  <div class="venue-add-page">
    <!-- 导航栏 -->
    <van-nav-bar :title="isEdit ? '编辑场地' : '新增场地'" :border="false" left-arrow @click-left="goBack" />

    <!-- 表单内容 -->
    <div class="form-container">
      <van-form @submit="handleSubmit" ref="formRef">
        <van-cell-group>
          <!-- 场地名称 -->
          <van-field v-model="formData.venueName" name="venueName" label="场地名称" placeholder="请输入场地名称" :rules="[
            { required: true, message: '请输入场地名称' },
            { max: 100, message: '场地名称不能超过100个字符' }
          ]" required />

          <!-- 是否内部 -->
          <van-field name="isInternal" label="是否内部" required>
            <template #input>
              <van-radio-group v-model="formData.isInternal" direction="horizontal">
                <van-radio :name="0">否</van-radio>
                <van-radio :name="1">是</van-radio>
              </van-radio-group>
            </template>
          </van-field>

          <!-- 适合类型 -->
          <van-field name="suitableType" label="适合类型" :value="suitableTypeText" is-link readonly
            @click="showSuitableTypePicker = true" />

          <!-- 费用 -->
          <van-field v-model="formData.cost" name="cost" label="费用(元)" placeholder="请输入场地使用费" type="number"
            :formatter="formatCost" :parser="parseCost" />

          <!-- 联系人 -->
          <van-field v-model="formData.contactPerson" name="contactPerson" label="联系人" placeholder="请输入联系人姓名" />

          <!-- 联系电话 -->
          <van-field v-model="formData.contactPhone" name="contactPhone" label="联系电话" placeholder="请输入联系电话"
            type="tel" />

          <!-- 所在城市 -->
          <van-field name="cityName" label="所在城市" :value="cityText" is-link readonly @click="showCityPicker = true" />

          <!-- 详细地址 -->
          <van-field v-model="formData.address" name="address" label="详细地址" placeholder="请输入详细地址" type="textarea"
            autosize :rules="[
              { required: true, message: '请输入详细地址' },
              { max: 200, message: '地址不能超过200个字符' }
            ]" required />
        </van-cell-group>

        <!-- 提交按钮 -->
        <div class="submit-container">
          <van-button type="primary" native-type="submit" size="large" block :loading="submitting">
            {{ isEdit ? '保存修改' : '立即保存' }}
          </van-button>
        </div>
      </van-form>
    </div>

    <!-- 适合类型选择器 -->
    <van-popup v-model:show="showSuitableTypePicker" position="bottom" round>
      <van-picker :columns="suitableTypeColumns" @confirm="onSuitableTypeConfirm"
        @cancel="showSuitableTypePicker = false" />
    </van-popup>

    <!-- 城市选择器 -->
    <van-popup v-model:show="showCityPicker" position="bottom" round>
      <van-picker :columns="cityColumns" @confirm="onCityConfirm" @cancel="showCityPicker = false" />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import useVenueStore from '@/stores/venue'
import {
  VENUE_SUITABLE_TYPE_OPTIONS,
  CHINA_CITY_OPTIONS,
  getVenueSuitableTypeText,
  getCityText
} from '@/consts'

const router = useRouter()
const route = useRoute()
const venueStore = useVenueStore()

// 响应式数据
const formRef = ref(null)
const submitting = ref(false)
const showSuitableTypePicker = ref(false)
const showCityPicker = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  venueName: '',
  isInternal: 0,
  suitableType: '',
  cost: '',
  contactPerson: '',
  contactPhone: '',
  cityName: '',
  address: '',
  mapCoordinate: '',
  photos: '',
})

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 适合类型显示文本
const suitableTypeText = computed(() => {
  if (!formData.suitableType) return '请选择适合类型'
  const types = formData.suitableType.split(',').filter(type => type.trim())
  return types.map(type => getVenueSuitableTypeText(type)).join('、')
})

// 城市显示文本
const cityText = computed(() => {
  if (!formData.cityName) return '请选择所在城市'
  return getCityText(formData.cityName) || formData.cityName
})

// 适合类型选择器列
const suitableTypeColumns = computed(() => {
  return VENUE_SUITABLE_TYPE_OPTIONS.map(option => ({
    text: option.label,
    value: option.value
  }))
})

// 城市选择器列
const cityColumns = computed(() => {
  return CHINA_CITY_OPTIONS.map(option => ({
    text: option.label,
    value: option.value
  }))
})

// 方法
const goBack = async () => {
  // 检查是否有未保存的修改
  const hasChanges = checkFormChanges()
  if (hasChanges) {
    try {
      await showConfirmDialog({
        title: '提示',
        message: '您有未保存的修改，确定要离开吗？',
        confirmButtonText: '离开',
        cancelButtonText: '继续编辑'
      })
      router.back()
    } catch {
      // 用户取消，不做任何操作
    }
  } else {
    router.back()
  }
}

// 检查表单是否有修改
const checkFormChanges = () => {
  // 简单检查，实际项目中可以更精确地比较
  return Object.values(formData).some(value => value !== '' && value !== 0 && value !== null)
}

// 格式化费用显示
const formatCost = (value) => {
  if (!value) return ''
  return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

// 解析费用输入
const parseCost = (value) => {
  return value?.replace(/,/g, '') || ''
}

// 适合类型确认
const onSuitableTypeConfirm = ({ selectedOptions }) => {
  const selectedValues = selectedOptions.map(option => option.value)
  formData.suitableType = selectedValues.join(',')
  showSuitableTypePicker.value = false
}

// 城市确认
const onCityConfirm = ({ selectedOptions }) => {
  formData.cityName = selectedOptions[0]?.value || ''
  showCityPicker.value = false
}

// 获取场地详情（编辑模式）
const fetchVenueDetail = async (venueId) => {
  try {
    // 从store中获取场地详情
    const venueList = venueStore.getVenueList
    const venue = venueList.find(v => v.id == venueId)

    if (venue) {
      // 填充表单数据
      Object.assign(formData, {
        id: venue.id,
        venueName: venue.venueName || '',
        isInternal: venue.isInternal || 0,
        suitableType: venue.suitableType || '',
        cost: venue.cost || '',
        contactPerson: venue.contactPerson || '',
        contactPhone: venue.contactPhone || '',
        cityName: venue.cityName || '',
        address: venue.address || '',
        mapCoordinate: venue.mapCoordinate || '',
        photos: venue.photos || '',
      })
    } else {
      showToast('场地不存在')
      router.back()
    }
  } catch (error) {
    console.error('获取场地详情失败:', error)
    showToast('获取场地详情失败')
    router.back()
  }
}

// 表单提交
const handleSubmit = async () => {
  // 表单验证
  try {
    await formRef.value?.validate()
  } catch (error) {
    console.log('表单验证失败:', error)
    return
  }

  submitting.value = true
  try {
    const submitData = {
      ...formData,
      id: formData.id || 0,
      cost: formData.cost ? Number(formData.cost.replace(/,/g, '')) : 0
    }

    const result = await venueStore.saveVenue(submitData)

    if (result.status) {
      showToast(isEdit.value ? '修改成功' : '添加成功')
      router.back()
    } else {
      showToast(result.message || '操作失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    showToast('保存失败')
  } finally {
    submitting.value = false
  }
}

// 页面初始化
onMounted(() => {
  if (isEdit.value) {
    const venueId = route.params.id
    fetchVenueDetail(venueId)
  }
})
</script>

<style scoped lang="scss">
.venue-add-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.form-container {
  padding: 16px;
}

.submit-container {
  margin-top: 24px;
  padding: 0 16px 24px;
}

// 表单字段样式
:deep(.van-cell) {
  background-color: #fff;

  &:not(:last-child) {
    border-bottom: 1px solid #ebedf0;
  }
}

:deep(.van-field__label) {
  color: #323233;
  font-weight: 500;
}

:deep(.van-field__control) {
  color: #323233;
}

// 单选按钮组样式
:deep(.van-radio-group) {
  .van-radio {
    margin-right: 24px;

    &:last-child {
      margin-right: 0;
    }
  }
}

// 文本域样式
:deep(.van-field--textarea) {
  .van-field__control {
    min-height: 60px;
  }
}

// 选择器弹窗样式
:deep(.van-popup) {
  .van-picker {
    .van-picker__toolbar {
      padding: 16px;
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .form-container {
    padding: 12px;
  }

  .submit-container {
    padding: 0 12px 20px;
  }
}
</style>
