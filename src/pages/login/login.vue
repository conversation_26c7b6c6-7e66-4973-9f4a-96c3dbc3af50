<template>
  <div class="bg-primary">
    <van-nav-bar :title="loaded ? '畅读制片' : ' '" :border="false" />

    <!-- 微信环境检测提示 -->
    <div v-if="!isWeChatEnv" class="wechat-tip-container">
      <div class="content">
        <div class="flex-column-center title">
          <div class="platform"></div>
        </div>
      </div>
      <div class="wrap">
        <div class="form">
          <div class="wechat-tip">
            <img src="@/assets/images/wx.png" alt="微信" class="wechat-icon" />
            <div class="tip-title">{{ $t('open_in_wechat') }}</div>
            <div class="tip-desc">{{ $t('wechat_experience_tip') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 微信登录中 -->
    <div v-else-if="isLoggingIn" class="loading-container">
      <div class="content">
        <div class="flex-column-center title">
          <div class="platform"></div>
        </div>
      </div>
      <div class="wrap">
        <div class="form">
          <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">{{ $t('logging_in') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录失败 -->
    <div v-else-if="loginError" class="error-container">
      <div class="content">
        <div class="flex-column-center title">
          <div class="platform"></div>
        </div>
      </div>
      <div class="wrap">
        <div class="form">
          <div class="error-content">
            <div class="error-icon">⚠️</div>
            <div class="error-title">{{ $t('login_failed') }}</div>
            <div class="error-desc">{{ loginError }}</div>
            <div class="btn-primary retry-btn" @click="retryLogin">
              {{ $t('retry') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, toRefs } from 'vue'
import useI18nStore from '@/stores/i18n'
import useHomeStore from '@/stores/login'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useLoad } from '@/components/loading/useLoad'
import { isWeChat, getQueryParams } from '@/utils'
const store = useI18nStore()
const { loaded } = toRefs(store)
const { fetchLogin, wxLogin } = useHomeStore()
const router = useRouter()
const { t } = useI18n()
const { loading } = useLoad()

// 状态管理
const isWeChatEnv = ref(false)
const isLoggingIn = ref(true)
const loginError = ref('')

// 检测微信环境
const checkWeChatEnvironment = () => {
  isWeChatEnv.value = isWeChat
}

// 微信静默登录
const performWeChatLogin = async () => {
  try {
    isLoggingIn.value = true
    loginError.value = ''

    // 获取URL参数中的微信授权信息和报名参数
    const { unionid, code, state, projectId,joinProjectId, role } = getQueryParams(location.href)

    // 如果有报名相关参数，先存储到sessionStorage
    if (projectId) {
      sessionStorage.setItem('applayProjectId', projectId)
     
    }
    if(joinProjectId){
      sessionStorage.setItem('joinProjectId', joinProjectId)
      
    }
    if ((projectId||joinProjectId)&&role) {
      sessionStorage.setItem('applayRole', role)
    }

    if (!unionid && !code) {
      // 如果没有授权信息，跳转到微信授权页面（不携带报名参数）
      const redirectUrl = encodeURIComponent(location.href.split('?')[0])
      const wxAuthUrl = `https://cdproductionserverwebapi.51changdu.com/Home/WxLogin?redirectUrl=${redirectUrl}`

      window.location.href = wxAuthUrl
      return
    }

    let res
    if (unionid) {
      // 使用unionid进行登录
      res = await fetchLogin({
        unionid: unionid,
        loginType: 'wechat',
      })
    } else if (code) {
      // 使用微信授权码登录
      res = await wxLogin({
        code: code,
        state: state,
      })

      if (res) {
        // 微信登录成功后，获取用户信息
        const userInfoRes = await fetchLogin({
          loginType: 'wechat_callback',
        })
        res = userInfoRes
      }
    }

    if (res && res.status) {
      localStorage.setItem('login', true)

      // 从sessionStorage获取报名相关参数
      const storedProjectId = sessionStorage.getItem('applayProjectId')
      const joinProjectId = sessionStorage.getItem('joinProjectId')
      const storedRole = sessionStorage.getItem('applayRole')

      // 检查是否有报名相关参数
      if ((storedProjectId||joinProjectId)&&storedRole) {
        // 有报名参数，自动设置角色并跳转到项目详情
        const loginStore = useHomeStore()

        // 设置角色（默认演员，也支持传入的角色参数）
        const targetRole = storedRole === 'staff' ? 'staff' : 'actor' // 默认演员，也支持工作人员
        sessionStorage.removeItem('applayRole')

        loginStore.setCurrentRole(targetRole)

        // 跳转到项目详情页
        router.push({
          name: 'layoutHome',
        })
      } else {
        // 没有报名参数，正常跳转到角色选择页面
        router.push({ name: 'switch' })
      }
    } else {
      throw new Error(res?.message || '登录失败')
    }
  } catch (error) {
    console.error('微信登录失败:', error)
    loginError.value = error.message || '登录失败，请重试'
  } finally {
    isLoggingIn.value = false
  }
}

// 重试登录
const retryLogin = () => {
  loginError.value = ''
  performWeChatLogin()
}

onMounted(() => {
  // 检测微信环境
  checkWeChatEnvironment()

  // 如果在微信环境中，自动进行微信登录
  if (
    isWeChatEnv.value ||
    location.host.includes('localhost') ||
    location.host.includes('192.168')
  ) {
    performWeChatLogin()
  } else {
    isLoggingIn.value = false
  }
})
</script>

<style scoped lang="scss">
.content {
  position: relative;
  z-index: 8;
  color: #fff;
}

.title {
  line-height: 74px;
  text-align: center;
}

.platform {
  font-weight: 500;
  font-size: 51px;
  line-height: 74px;
  color: #000000;
}

.wrap {
  width: 100%;
  background: #fff;
  border-radius: 70px 70px 0px 0px;
  flex: 1;
}

.form {
  width: 985px;
  background-color: #fff;
  border-radius: 39px;
  padding: 90px 49px;
  position: relative;
}

// 微信提示样式
.wechat-tip {
  text-align: center;
  padding: 60px 0;
}

.wechat-icon {
  width: 120px;
  height: 120px;
  margin-bottom: 40px;
}

.tip-title {
  font-size: 48px;
  font-weight: 500;
  color: #333;
  margin-bottom: 20px;
}

.tip-desc {
  font-size: 36px;
  color: #666;
  line-height: 1.5;
}

// 加载中样式
.loading-content {
  text-align: center;
  padding: 80px 0;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #935d2a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 42px;
  color: #333;
  font-weight: 500;
}

// 错误样式
.error-content {
  text-align: center;
  padding: 60px 0;
}

.error-icon {
  font-size: 80px;
  margin-bottom: 30px;
}

.error-title {
  font-size: 48px;
  font-weight: 500;
  color: #f63f39;
  margin-bottom: 20px;
}

.error-desc {
  font-size: 36px;
  color: #666;
  margin-bottom: 50px;
  line-height: 1.5;
}

.retry-btn {
  line-height: 137px;
  border-radius: 200px;
  font-weight: 500;
  font-size: 51px;
  color: #ffffff;
  width: 400px;
  margin: 0 auto;
}

.btn-primary {
  background: linear-gradient(135deg, #ff8124 0%, #ff6b00 100%);
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
