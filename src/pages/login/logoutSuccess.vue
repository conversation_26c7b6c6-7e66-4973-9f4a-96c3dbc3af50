<template>
  <div class="logout-success-container">
    <!-- 装饰性背景元素 -->
    <div class="bg-decorations">
      <div class="decoration decoration-1"></div>
      <div class="decoration decoration-2"></div>
      <div class="decoration decoration-3"></div>
    </div>

    <div class="content">
      <div class="success-section">
        <!-- 成功图标 -->
        <div class="success-icon">
          <div class="icon-circle">
            <div class="checkmark">✓</div>
          </div>
        </div>

        <!-- 成功信息 -->
        <div class="success-info">
          <div class="title">退出成功</div>
          <div class="subtitle">您已安全退出登录</div>
          <div class="description">感谢您的使用，期待下次再见！</div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions">
          <button class="action-btn primary" @click="goToLogin" :disabled="isLoading">
            <van-loading v-if="isLoading" size="20px" color="#fff" />
            <span v-else>重新登录</span>
          </button>

          <!-- 微信环境下显示关闭页面按钮 -->
          <button
            v-if="false"
            class="action-btn secondary"
            @click="closePage"
            :disabled="isClosing">
            <van-loading v-if="isClosing" size="20px" color="#666" />
            <span v-else>关闭页面</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useEnv } from '@fe/base'
import { useBridge } from '@fe/bridge'
import { showToast } from 'vant'
import { isWeChat } from '@/utils'

const router = useRouter()
const env = useEnv()
const bridge = useBridge()

// 响应式数据
const isLoading = ref(false)
const isClosing = ref(false)
const isWeChatEnv = ref(false)
const isChangDuEnv = ref(false)

// 检测环境
const checkEnvironment = () => {
  isWeChatEnv.value = isWeChat
  isChangDuEnv.value = env.isChangDu || window.cdbridge || window.webkit
}

// 跳转到登录页
const goToLogin = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    router.push({ name: 'login' })
  } catch (error) {
    console.error('跳转登录页失败:', error)
    showToast('跳转失败，请重试')
  } finally {
    isLoading.value = false
  }
}

// 关闭页面
const closePage = async () => {
  if (isClosing.value) return

  try {
    isClosing.value = true
    window.close()

    // 尝试使用不同的方式关闭页面
  } catch (error) {
    console.error('关闭页面失败:', error)
  } finally {
    isClosing.value = false
  }
}

onMounted(() => {
  checkEnvironment()

  // 页面加载完成后的动画效果
  setTimeout(() => {
    document.querySelector('.success-section')?.classList.add('fade-in')
  }, 100)
})
</script>

<style scoped lang="scss">
.logout-success-container {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  height: 100vh;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
  background: linear-gradient(135deg, #ff8124 0%, #ff6b00 50%, #ff5500 100%);

  // 添加微妙的纹理
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }
}

// 装饰性背景元素
.bg-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;

  .decoration {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;

    &.decoration-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      left: -50px;
      animation-delay: 0s;
    }

    &.decoration-2 {
      width: 150px;
      height: 150px;
      top: 60%;
      right: -30px;
      animation-delay: 2s;
    }

    &.decoration-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  position: relative;
  z-index: 1;
}

.success-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 30px;
  padding: 80px 60px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  max-width: 600px;
  width: 100%;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;

  &.fade-in {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-icon {
  margin-bottom: 40px;

  .icon-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4caf50, #45a049);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;

    .checkmark {
      font-size: 60px;
      color: white;
      font-weight: bold;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.success-info {
  margin-bottom: 50px;

  .title {
    font-size: 48px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
  }

  .subtitle {
    font-size: 32px;
    color: #666;
    margin-bottom: 20px;
  }

  .description {
    font-size: 28px;
    color: #999;
    line-height: 1.5;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;

  .action-btn {
    height: 100px;
    border-radius: 50px;
    font-size: 32px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    &:disabled {
      cursor: not-allowed;
      opacity: 0.7;
    }

    &.primary {
      background: linear-gradient(135deg, #ff8124 0%, #ff6b00 100%);
      color: white;
      box-shadow: 0 8px 25px rgba(255, 129, 36, 0.3);

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(255, 129, 36, 0.4);
      }
    }

    &.secondary {
      background: #f8f9fa;
      color: #666;
      border: 2px solid #e9ecef;

      &:hover:not(:disabled) {
        background: #e9ecef;
        transform: translateY(-1px);
      }
    }
  }
}

.tips {
  .tip-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 26px;
    color: #666;

    .tip-icon {
      font-size: 30px;
    }

    .tip-text {
      line-height: 1.4;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .success-section {
    padding: 60px 40px;
    margin: 20px;
  }

  .success-icon .icon-circle {
    width: 100px;
    height: 100px;

    .checkmark {
      font-size: 50px;
    }
  }

  .success-info {
    .title {
      font-size: 40px;
    }

    .subtitle {
      font-size: 28px;
    }

    .description {
      font-size: 24px;
    }
  }

  .actions .action-btn {
    height: 80px;
    font-size: 28px;
  }

  .tips .tip-item {
    font-size: 24px;

    .tip-icon {
      font-size: 26px;
    }
  }
}
</style>
