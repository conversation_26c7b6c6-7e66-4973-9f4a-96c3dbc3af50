<template>
  <div>
    <div class="divider">{{ $t('koc_quick_other') }}</div>
    <div class="quick-list">
      <a
        v-if="isWeChat"
        :href="`https://cdproductionserverwebapi.51changdu.com/Home/WxLogin?redirectUrl=${encodeURIComponent(
          '' + href.split('?')[0]
        )}`">
        <div
          class="quick-item flex-inline-center"
          :class="{ plain: !isCN }"
          @click="tracker.quickLogin({ loginType: '微信' })">
          <img class="icon" src="@/assets/images/wx.png" alt="" />
          <div>Wechat</div>
        </div>
      </a>
      <div
        v-else
        class="quick-item flex-inline-center"
        :class="{ plain: !isCN }"
        @click="handleQuick">
        <img class="icon" src="@/assets/images/wx.png" alt="" />
        Wechat
      </div>
    </div>
    <div id="qrcode"></div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useEnv } from '@fe/base'
import { isWeChat } from '@/utils'

import tracker from '@/utils/track'

const lang = ref('')
const href = location.href

const props = defineProps({
  isCN: Object,
})

lang.value = localStorage.lang || useEnv().urlLangCode || 'cn'

const emit = defineEmits(['change'])

const handleQuick = () => {
  if (!isWeChat) {
    emit('change', 0)
  }
  tracker.quickLogin({ loginType: '微信' })
}
</script>

<style scoped lang="scss">
.divider {
  font-weight: 500;
  font-size: 42px;
  color: #592300;
  line-height: 60px;
  margin: 180px 0 74px;
  display: flex;
  justify-content: center;
  align-items: center;

  &::before,
  &::after {
    content: '';
    width: 101px;
    height: 4px;
    background: linear-gradient(270deg, #592300 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 5px 5px 5px 5px;
    opacity: 0.59;
    display: inline-block;
    margin-right: 20px;
  }

  &::after {
    transform: rotate(-180deg);
  }
}

.quick-list {
  width: 100%;
  display: flex;
  gap: 136px;
  justify-content: center;
  margin-bottom: 20px;
}

.quick-item {
  display: flex;
  flex-direction: column;
  position: relative;
  // margin: 0 auto;
  font-weight: 400;
  font-size: 36px;
  color: #999999;

  a {
    font-size: 52px;
    color: #999999;
    font-weight: 500;
  }

  .icon {
    width: 110px;
    border-radius: 50%;
    margin-bottom: 18px;
  }
}

.g_id_signin {
  position: absolute;
  left: 0;
  opacity: 0;
}
</style>
