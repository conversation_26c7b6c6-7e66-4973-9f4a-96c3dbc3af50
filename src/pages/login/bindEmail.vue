<template>
  <div class="bg-primary">
    <van-nav-bar title="绑定邮箱" :border="false" left-arrow @click-left="handleBack" />
    <div class="form">
      <van-space direction="vertical" fill>
        <div class="title-1">📧</div>
        <div class="text-secondary">请绑定您的邮箱以完善账户信息</div>
      </van-space>
      <van-cell-group>
        <van-field
          v-model="email"
          type="email"
          :placeholder="$t('enter_email_address')"
          :error-message="emailError"
          @input="emailError = ''"
          left-icon="user-o"
          size="large"
          :disabled="isLoading" />

        <van-field
          v-model="verificationCode"
          :placeholder="$t('enter_verification_code')"
          :error-message="codeError"
          @input="codeError = ''"
          left-icon="envelop-o"
          size="large"
          :disabled="isLoading">
          <template #button>
            <van-button
              type="primary"
              plain
              size="mini"
              :disabled="countdown > 0 || isSendingCode"
              @click="sendVerificationCode">
              <van-loading v-if="isSendingCode" size="20px" color="#fff" />
              <span v-else>{{ countdown > 0 ? `${countdown}s` : '发送验证码' }}</span>
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
      <van-space direction="vertical" fill>
        <van-button type="primary" block round :disable="isBindingEmail" @click="handleBindEmail">
          <van-loading v-if="isBindingEmail" size="24px" color="#fff" />
          <span v-else>绑定邮箱</span>
        </van-button>
        <van-button block round @click="handleSkipBinding">跳过绑定</van-button>
      </van-space>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, toRefs } from 'vue'
import useI18nStore from '@/stores/i18n'
import useLoginStore from '@/stores/login'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { validateEmail } from '@/utils'

const store = useI18nStore()
const { loaded } = toRefs(store)
const loginStore = useLoginStore()
const router = useRouter()
const { t } = useI18n()

// 表单数据
const email = ref('')
const verificationCode = ref('')

// 错误信息
const emailError = ref('')
const codeError = ref('')

// 验证码倒计时
const countdown = ref(0)
let countdownTimer = null

// 加载状态
const isSendingCode = ref(false)
const isBindingEmail = ref(false)

// 计算属性：是否有任何加载状态
const isLoading = computed(() => isSendingCode.value || isBindingEmail.value)

// 从store获取用户信息
const userInfo = computed(() => loginStore.getUserInfo)

// 发送验证码
const sendVerificationCode = async () => {
  // 防双击和倒计时检查
  if (countdown.value > 0 || isSendingCode.value) return

  if (!email.value) {
    emailError.value = t('enter_email_address')
    return
  }

  if (!validateEmail(email.value)) {
    emailError.value = t('invalid_email_format')
    return
  }

  try {
    isSendingCode.value = true

    const res = await loginStore.fetchSendCode({
      email: email.value,
    })

    if (res && res.status) {
      showToast(t('verification_code_sent'))
      startCountdown()
    } else {
      showToast(res?.message || t('send_failed_retry'))
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    showToast(t('send_failed_retry'))
  } finally {
    isSendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 180
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
    }
  }, 1000)
}

// 表单验证
const validateForm = () => {
  let isValid = true

  if (!email.value) {
    emailError.value = t('enter_email_address')
    isValid = false
  } else if (!validateEmail(email.value)) {
    emailError.value = t('invalid_email_format')
    isValid = false
  }

  if (!verificationCode.value) {
    codeError.value = t('enter_verification_code')
    isValid = false
  }

  return isValid
}

// 绑定邮箱
const handleBindEmail = async () => {
  // 防双击检查
  if (isBindingEmail.value) return

  if (!validateForm()) return

  try {
    isBindingEmail.value = true

    // 先验证邮箱验证码
    const verifyRes = await loginStore.fetchVerifyCode({
      email: email.value,
      code: verificationCode.value,
    })

    if (verifyRes && verifyRes.status) {
      showToast(t('email_bind_success'))

      // 更新用户信息，标记已绑定邮箱
      const updatedUserInfo = {
        ...JSON.parse(localStorage.getItem('USER_INFO') || '{}'),
        hasBindEmail: true,
      }

      // 更新store中的用户信息
      loginStore.setUserInfo(updatedUserInfo)

      // 跳转到角色选择页面
      router.push({ name: 'switch' })
    } else {
      showToast(verifyRes?.message || t('bind_failed_retry'))
    }
  } catch (error) {
    console.error('绑定邮箱失败:', error)
    showToast(t('bind_failed_retry'))
  } finally {
    isBindingEmail.value = false
  }
}

// 跳过绑定
const handleSkipBinding = () => {
  try {
    // 更新用户信息，标记已绑定邮箱（跳过状态）
    const updatedUserInfo = {
      ...JSON.parse(localStorage.getItem('USER_INFO') || '{}'),
      hasBindEmail: '0', // 设置为 '0' 表示跳过绑定
    }

    // 更新store中的用户信息
    loginStore.setUserInfo(updatedUserInfo)

    showToast('已跳过邮箱绑定')

    // 跳转到角色选择页面
    router.push({ name: 'switch' })
  } catch (error) {
    console.error('跳过绑定失败:', error)
    showToast('操作失败，请重试')
  }
}

// 返回处理
const handleBack = () => {
  router.back()
}

onMounted(() => {
  // 初始化用户信息（从localStorage恢复到store）
  // loginStore.initUserInfo()

  // 检查是否有用户信息，如果没有则跳转到登录页
  if (!loginStore.isLoggedIn) {
    router.push('/login')
    return
  }
})
</script>

<style scoped lang="scss">
.form {
  display: flex;
  flex-direction: column;
  gap: 36px;
  background: #fff;
  padding: 24px;
  text-align: center;
  border-radius: 16px 16px 0 0;
}
</style>
