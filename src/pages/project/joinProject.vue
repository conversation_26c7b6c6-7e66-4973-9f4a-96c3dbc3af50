<template>
  <div class="bg-primary">
    <van-nav-bar :title="$t('join_project')" :border="false" left-arrow @click-left="handleBack" />

    <div class="join-project-page">
      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading && !projectInfo">
        <van-loading size="24px" vertical>{{ $t('loading') }}...</van-loading>
      </div>

      <!-- 错误状态 -->
      <div class="error-state" v-else-if="error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <van-button type="primary" @click="retryLoad" :loading="loading">
          {{ $t('retry') }}
        </van-button>
      </div>

      <!-- 项目信息 -->
      <div class="project-content" v-else-if="projectInfo">
        <!-- 项目基本信息 -->
        <div class="project-info-card">
          <div class="project-header">
            <h2 class="project-title">{{ projectInfo.productionName }}</h2>
            <!-- <van-tag :type="getStatusTagType(projectInfo.status)" size="medium">
              {{ getProjectStatusText }}
            </van-tag> -->
          </div>

          <div class="project-details">
            <div class="detail-item" v-if="projectInfo.secondProductionCode">
              <span class="label">{{ $t('project_code') }}:</span>
              <span class="value">{{ projectInfo.secondProductionCode }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.startDate">
              <span class="label">{{ $t('start_date') }}:</span>
              <span class="value">{{ formatDate(projectInfo.startDate) }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.endDate">
              <span class="label">{{ $t('end_date') }}:</span>
              <span class="value">{{ formatDate(projectInfo.endDate) }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.cityName">
              <span class="label">{{ $t('city') }}:</span>
              <span class="value">{{ projectInfo.cityName }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.description">
              <span class="label">{{ $t('description') }}:</span>
              <span class="value description">{{ projectInfo.description }}</span>
            </div>
          </div>
        </div>

        <!-- 未加入项目时显示报名表单 -->
        <div class="join-section" v-if="!hasJoinedProject">
          <div class="join-tip">
            <div class="tip-icon">🎬</div>
            <div class="tip-text">{{ $t('join_project_tip') }}</div>
          </div>

          <!-- 报名表单 -->
          <div class="join-form">
            <van-form @submit="handleJoinProject">
              <van-field
                :model-value="getRoleTypeText(formData.roleType)"
                name="roleType"
                :label="$t('role_type')"
                :placeholder="$t('please_select_role_type')"
                readonly
                is-link
                @click="showRoleTypePicker = true"
                :rules="[{ required: true, message: $t('please_select_role_type') }]"
              />

              <van-button
                type="primary"
                size="large"
                native-type="submit"
                :loading="joining"
                class="join-btn"
              >
                {{ $t('join_project') }}
              </van-button>
            </van-form>
          </div>
        </div>

        <!-- 已加入项目时显示成功状态和房间信息 -->
        <div class="joined-section" v-else>
          <div class="success-tip">
            <div class="success-icon">✅</div>
            <div class="success-text">{{ $t('join_project_success_tip') }}</div>
          </div>

          <!-- 房间信息 -->
          <div class="room-info-card" v-if="projectRoomInfo">
            <div class="room-header">
              <span class="room-icon">🏠</span>
              <span class="room-title">{{ $t('room_info') }}</span>
            </div>

            <div class="room-details">
          
              <div class="detail-item" v-if="projectRoomInfo.communityName">
                <span class="label">{{ $t('community') }}:</span>
                <span class="value">{{ projectRoomInfo.communityName }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.communityArea">
                <span class="label">{{ $t('room_address') }}:</span>
                <span class="value">{{ projectRoomInfo.communityArea }}</span>
              </div> 


              <div class="detail-item" v-if="projectRoomInfo.buildingNumber && projectRoomInfo.roomNumber">
                <span class="label">{{ $t('room_number') }}:</span>
                <span class="value">{{ projectRoomInfo.buildingNumber }}栋{{ projectRoomInfo.roomNumber }}号</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.roomPosition">
                <span class="label">{{ $t('room_position') }}:</span>
                <span class="value">{{ projectRoomInfo.roomPosition }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.roomType">
                <span class="label">{{ $t('room_type') }}:</span>
                <span class="value">{{ getRoomTypeText(projectRoomInfo.roomType) }}</span>
              </div>
<!-- 
              <div class="detail-item" v-if="projectRoomInfo.roomLevel">
                <span class="label">{{ $t('room_level') }}:</span>
                <span class="value">{{ getRoomLevelText(projectRoomInfo.roomLevel) }}</span>
              </div> -->

              <div class="detail-item" v-if="projectRoomInfo.startTime">
                <span class="label">{{ $t('check_in_time') }}:</span>
                <span class="value">{{ projectRoomInfo.startTime }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.endTime">
                <span class="label">{{ $t('check_out_time') }}:</span>
                <span class="value">{{ projectRoomInfo.endTime }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.mainDoorPassword">
                <span class="label">{{ $t('main_door_password') }}:</span>
                <span class="value password">{{ projectRoomInfo.mainDoorPassword }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.innerDoorPassword">
                <span class="label">{{ $t('inner_door_password') }}:</span>
                <span class="value password">{{ projectRoomInfo.innerDoorPassword }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.broadbandAccount">
                <span class="label">{{ $t('broadband_account') }}:</span>
                <span class="value">{{ projectRoomInfo.broadbandAccount }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.broadbandPassword">
                <span class="label">{{ $t('broadband_password') }}:</span>
                <span class="value password">{{ projectRoomInfo.broadbandPassword }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.equipments">
                <span class="label">{{ $t('equipments') }}:</span>
                <span class="value">{{ projectRoomInfo.equipments }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.remark">
                <span class="label">{{ $t('remark') }}:</span>
                <span class="value">{{ projectRoomInfo.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 角色类型选择器 -->
    <van-popup v-model:show="showRoleTypePicker" position="bottom">
      <van-picker
        :columns="roleTypeOptions"
        @confirm="onRoleTypeConfirm"
        @cancel="showRoleTypePicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { showToast } from 'vant'
import useProjectStore from '@/stores/joinProject'
import useLoginStore from '@/stores/login'
import { ACTOR_ROLE_TYPE_OPTIONS, ROLE_TYPE_OPTIONS } from '@/consts/index'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const projectStore = useProjectStore()
const loginStore = useLoginStore()

// 页面状态
const loading = ref(false)
const joining = ref(false)
const error = ref('')

// 表单数据
const formData = ref({
  roleType: ''
})

// 角色类型选择器状态
const showRoleTypePicker = ref(false)

// 从store获取数据
const projectInfo = computed(() => projectStore.getProjectInfo)
const hasJoinedProject = computed(() => projectStore.getHasJoinedProject)
const projectRoomInfo = computed(() => projectStore.getProjectRoomInfo)

// 获取项目状态文本
const getProjectStatusText = computed(() => projectStore.getProjectStatusText)

// 根据当前角色获取角色类型选项
const roleTypeOptions = computed(() => {
  const currentRole = loginStore.getCurrentRole
  if (currentRole === 'actor') {
    return ACTOR_ROLE_TYPE_OPTIONS.map(option => ({
      text: option.text,
      value: option.value
    }))
  } else if (currentRole === 'staff') {
    return ROLE_TYPE_OPTIONS.filter(option => !option.disable).map(option => ({
      text: option.text,
      value: option.value
    }))
  }
  return []
})

// 获取角色类型显示文本
const getRoleTypeText = (roleType) => {
  const currentRole = loginStore.getCurrentRole
  if (currentRole === 'actor') {
    const option = ACTOR_ROLE_TYPE_OPTIONS.find(opt => opt.value === roleType)
    return option?.text || ''
  } else if (currentRole === 'staff') {
    const option = ROLE_TYPE_OPTIONS.find(opt => opt.value === roleType)
    return option?.text || ''
  }
  return ''
}

// 获取当前用户的人员类型（已移至 loginStore）
// const getCurrentPersonType = () => {
//   const currentRole = loginStore.getCurrentRole
//   return currentRole === 'actor' ? 1 : 0 // 1演员，0人员
// }

// 角色类型选择器确认
const onRoleTypeConfirm = ({ selectedOptions }) => {
  const selectedOption = selectedOptions[0]
  formData.value.roleType = selectedOption.value
  showRoleTypePicker.value = false
}

// 初始化页面数据
const initPageData = async () => {
  const projectId = route.params.id
  if (!projectId) {
    error.value = t('missing_project_id')
    return
  }

  loading.value = true
  error.value = ''

  try {
    const personType = loginStore.getCurrentPersonType

    // 获取项目详情
    const projectRes = await projectStore.fetchProjectById(Number(projectId))
    if (!projectRes.status) {
      error.value = projectRes.message || t('fetch_project_failed')
      return
    }

    // 检查是否已加入项目
    const joinRes = await projectStore.checkHasJoinedProject(Number(projectId), personType)
    if (!joinRes.status) {
      console.warn('检查加入状态失败:', joinRes.message)
    }

    // 如果已加入项目，获取房间信息
    if (projectStore.getHasJoinedProject) {
      const roomRes = await projectStore.fetchProjectRoomInfo(Number(projectId), personType)
      if (!roomRes.status) {
        console.warn('获取房间信息失败:', roomRes.message)
      }
    }

    // 如果已加入项目，获取房间信息
    if (projectStore.getHasJoinedProject) {
      const roomRes = await projectStore.fetchProjectRoomInfo(Number(projectId), personType)
      if (!roomRes.status) {
        console.warn('获取房间信息失败:', roomRes.message)
      }
    }
  } catch (err) {
    console.error('初始化页面数据失败:', err)
    error.value = err.message || t('network_error')
  } finally {
    loading.value = false
  }
}

// 加入项目
const handleJoinProject = async () => {
  const projectId = route.params.id
  if (!projectId) {
    showToast(t('missing_project_id'))
    return
  }

  // 验证表单
  if (!formData.value.roleType) {
    showToast(t('please_select_role_type'))
    return
  }

  joining.value = true
  try {
    const personType = loginStore.getCurrentPersonType

    const res = await projectStore.joinProject(Number(projectId), personType, formData.value.roleType)
    if (res.status) {
      showToast(t('join_project_success'))
      // 加入成功后获取房间信息
      const roomRes = await projectStore.fetchProjectRoomInfo(Number(projectId), personType)
      if (!roomRes.status) {
        console.warn('获取房间信息失败:', roomRes.message)
      }
    } else {

      showToast(res.message || t('join_project_failed'))
    }
  } catch (err) {

    console.error('加入项目失败:', err)
    showToast(t('join_project_failed'))
  } finally {
    joining.value = false
  }
}

// 重试加载
const retryLoad = () => {
  initPageData()
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    0: 'default', 1: 'primary', 2: 'success', 3: 'warning',
    4: 'primary', 5: 'primary', 6: 'warning', 7: 'danger', 8: 'warning'
  }
  return typeMap[status] || 'default'
}

// 获取房间类型文本
const getRoomTypeText = (roomType) => {
  const typeMap = {
    1: t('single_bed_room'),
    2: t('double_bed_room')
  }
  return typeMap[roomType] || t('unknown_room_type')
}

// 获取房间等级文本
const getRoomLevelText = (roomLevel) => {
  const levelMap = {
    1: t('excellent'),
    2: t('good'),
    3: t('average')
  }
  return levelMap[roomLevel] || t('unknown_level')
}

// 页面初始化
onMounted(() => {
  // 检查登录状态
  if (!localStorage.getItem('token')) {
    router.push('/login')
    return
  }

  // 重置store状态
  projectStore.resetProjectInfo()

  // 初始化页面数据
  initPageData()
})
</script>

<style lang="scss" scoped>
.join-project-page {
  padding: 16px;
  min-height: calc(100vh - 46px);
  background: #f5f5f5;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-state {
  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-text {
    color: #666;
    margin-bottom: 24px;
    font-size: 14px;
  }
}

.project-content {
  .project-info-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .project-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .project-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
        flex: 1;
        margin-right: 12px;
      }
    }

    .project-details {
      .detail-item {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;

          &.description {
            line-height: 1.5;
          }
        }
      }
    }
  }

  .join-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .join-tip {
      margin-bottom: 24px;

      .tip-icon {
        font-size: 48px;
        margin-bottom: 12px;
      }

      .tip-text {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .join-form {
      text-align: left;

      .van-field {
        margin-bottom: 16px;
        border-radius: 8px;
        background: #f8f9fa;
      }
    }

    .join-btn {
      width: 100%;
      height: 48px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 600;
      margin-top: 16px;
    }
  }

  .joined-section {
    .success-tip {
      background: white;
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 16px;

      .success-icon {
        font-size: 48px;
        margin-bottom: 12px;
      }

      .success-text {
        color: #52c41a;
        font-size: 16px;
        font-weight: 600;
        line-height: 1.5;
      }
    }

    .room-info-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .room-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .room-icon {
          font-size: 18px;
          margin-right: 8px;
        }

        .room-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .room-details {
        .detail-item {
          display: flex;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #666;
            font-size: 14px;
            min-width: 100px;
            flex-shrink: 0;
          }

          .value {
            color: #333;
            font-size: 14px;
            flex: 1;

            &.password {
              font-family: 'Courier New', monospace;
              background: #f5f5f5;
              padding: 2px 6px;
              border-radius: 4px;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}
</style>