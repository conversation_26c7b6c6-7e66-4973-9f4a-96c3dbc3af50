<template>
  <div class="my-projects">
    <!-- 导航栏 -->
    <van-nav-bar
      title="我的项目"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      加载中...
    </van-loading>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败">
        <van-button round type="primary" @click="retryLoad">
          重新加载
        </van-button>
      </van-empty>
    </div>

    <!-- 无项目状态 -->
    <div v-else-if="!myProjectStore.hasProjects" class="empty-container">
      <van-empty description="暂无参与的项目" />
    </div>

    <!-- 项目内容 -->
    <div v-else class="project-content">
      <!-- 当前项目信息区域 -->
      <div class="project-info-section">
        <Card v-if="currentProject">
          <template #header>
            <div class="project-header">
              <span class="title-3">{{ currentProject.productionName }}</span>
              <van-icon
                v-if="myProjectStore.getProjectList.length > 1"
                name="exchange"
                class="switch-icon"
                @click="showProjectPicker = true"
              />
            </div>
          </template>
          <div class="project-info-grid">
            <div class="info-card" v-if="currentProject.startDate">
              <div class="info-label">开机时间</div>
              <div class="info-value">{{ formatDate(currentProject.startDate) }}</div>
            </div>
            <div class="info-card" v-if="currentProject.endDate">
              <div class="info-label">杀青时间</div>
              <div class="info-value">{{ formatDate(currentProject.endDate) }}</div>
            </div>
          </div>
          <template #footer v-if="currentProject.description">
            <p class="project-description">{{ currentProject.description }}</p>
          </template>
        </Card>
      </div>

      <!-- 功能入口区域 -->
      <div class="entry-section">
        <van-grid :column-num="2" >
          <!-- 住宿入口 -->
          <van-grid-item
            icon="home-o"
            text="住宿"
            @click="handleAccommodationClick"
          />
          <!-- 今日通告单入口 -->
          <van-grid-item
            icon="calendar-o"
            text="通告单"
            @click="handleTodaySceneCallClick"
          />
          <!-- 销场管理入口 -->
       
          <!-- 当日销场入口 - 只有特定角色的工作人员才能看到 -->
          <van-grid-item
            v-if="hasSceneVerifyPermission"
            icon="checked"
            text="销场"
            @click="handleTodaySceneVerifyClick"
          />
        </van-grid>
      </div>
    </div>

    <!-- 项目选择器弹窗 -->
    <van-popup v-model:show="showProjectPicker" position="bottom">
      <van-picker
        :columns="projectPickerColumns"
        @confirm="onProjectPickerConfirm"
        @cancel="showProjectPicker = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { showToast } from 'vant'
import useMyProjectStore from '@/stores/myProject'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'
import { SCENE_VERIFY_ALLOWED_ROLES } from '@/consts/index'
import Card from '@/components/Card.vue'

const router = useRouter()
const { t } = useI18n()
const myProjectStore = useMyProjectStore()
const loginStore = useLoginStore()
const homeStore = useHomeStore()

// 页面状态
const loading = ref(false)
const error = ref('')
const showProjectPicker = ref(false)

// 计算属性
const currentProject = computed(() => myProjectStore.getCurrentProject)
const currentRole = computed(() => loginStore.getCurrentRole)
const accountInfo = computed(() => homeStore.accountInfo)

// 检查是否有当日销场权限
const hasSceneVerifyPermission = computed(() => {
  // 只有工作人员才有权限
  if (currentRole.value !== 'staff') {
    return false
  }

  // 检查工作人员的角色类型是否在允许的列表中
  const userRoleType = accountInfo.value?.roleType
  return userRoleType && SCENE_VERIFY_ALLOWED_ROLES.includes(userRoleType)
})

// 项目选择器列数据
const projectPickerColumns = computed(() => {
  return myProjectStore.getProjectList.map(project => ({
    text: project.productionName,
    value: project.id,
    project: project
  }))
})

// 获取项目描述
const getProjectDescription = (project) => {
  const parts = []
  if (project.description) {
    parts.push(project.description)
  }
  if (project.cityName) {
    parts.push(`地点：${project.cityName}`)
  }
  if (project.totalEpisodes) {
    parts.push(`总集数：${project.totalEpisodes}`)
  }
  return parts.join(' | ') || '暂无描述'
}

// 获取项目缩略图
const getProjectThumb = (project) => {
  // 这里可以根据项目类型或其他字段返回默认图片
  return null
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 初始化页面数据
const initPageData = async () => {
  loading.value = true
  error.value = ''
  try {
    const personType = loginStore.getCurrentPersonType
    const res = await myProjectStore.fetchMyProjects(personType)
    if (!res.status) {
      error.value = res.message || '获取项目列表失败'
    }
  } catch (err) {
    console.error('初始化页面数据失败:', err)
    error.value = err.message || '网络错误'
  } finally {
    loading.value = false
  }
}

// 项目选择器确认
const onProjectPickerConfirm = ({ selectedOptions }) => {
  const selectedOption = selectedOptions[0]
  if (selectedOption?.project) {
    myProjectStore.setCurrentProject(selectedOption.project)
    showToast('项目切换成功')
  }
  showProjectPicker.value = false
}

// 住宿点击处理
const handleAccommodationClick = async () => {
  if (!currentProject.value) {
    showToast('请先选择项目')
    return
  }

  // 跳转到项目住宿页面
  router.push({
    name: 'projectAccommodation',
    params: {
      projectId: currentProject.value.id
    }
  })
}

// 销场点击处理
const handleMarketClick = async () => {
  if (!currentProject.value) {
    showToast('请先选择项目')
    return
  }

  // 跳转到项目销场页面
  router.push({
    name: 'sceneCall',
    params: {
      projectId: currentProject.value.id
    }
  })
}

// 今日通告单点击处理
const handleTodaySceneCallClick = async () => {
  if (!currentProject.value) {
    showToast('请先选择项目')
    return
  }

  // 跳转到今日通告单页面
  router.push({
    name: 'todaySceneCall',
    params: {
      projectId: currentProject.value.id
    }
  })
}

// 当日销场点击处理
const handleTodaySceneVerifyClick = async () => {
  if (!currentProject.value) {
    showToast('请先选择项目')
    return
  }

  // 跳转到当日销场页面
  router.push({
    name: 'todaySceneVerify',
    params: {
      projectId: currentProject.value.id
    }
  })
}

// 重试加载
const retryLoad = () => {
  initPageData()
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 页面挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.my-projects {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container,
.empty-container {
  padding: 40px 20px;
}

.project-content {
  padding: 16px;
}

.project-info-section {
  margin-bottom: 24px;
}

.project-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.switch-icon {
  font-size: 18px;
  color: #1989fa;
  cursor: pointer;
  padding: 4px;
}

.project-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 16px 0;
}

.info-card {
  text-align: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
}

.info-label {
  font-size: 12px;
  color: #969799;
  margin-bottom: 4px;
}

.info-value {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.project-description {
  margin: 0;
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
}

.title-3 {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
}

.entry-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
}

.entry-section .van-grid-item {
  background: #f7f8fa;
  border-radius: 8px;
}

.entry-section .van-grid-item:active {
  background: #ebedf0;
}
</style>