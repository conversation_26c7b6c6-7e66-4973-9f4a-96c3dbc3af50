<template>
  <div class="scene-call-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="销场管理"
      left-arrow
      @click-left="handleBack"
    />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      加载中...
    </van-loading>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败">
        <van-button round type="primary" @click="retryLoad">
          重新加载
        </van-button>
      </van-empty>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="sceneCallList.length === 0" class="empty-container">
      <van-empty description="暂无通告单数据" />
    </div>

    <!-- 通告单内容 -->
    <div v-else class="scene-call-content">
      <!-- 日期标签页 -->
      <van-tabs
        v-model:active="activeTab"
        sticky
        color="#ff6600"
        title-active-color="#ff6600"
        title-inactive-color="#969799"
      >
        <van-tab
          v-for="(sceneCall, index) in sceneCallList"
          :key="sceneCall.id || index"
          :title="getTabTitle(sceneCall)"
        >
          <!-- 场次列表 -->
          <div class="scene-list">
            <div v-if="!sceneCall.sceneInfos || sceneCall.sceneInfos.length === 0" class="empty-scenes">
              <van-empty description="暂无场次数据" />
            </div>
            <div v-else>
              <van-swipe-cell
                v-for="sceneInfo in sceneCall.sceneInfos"
                :key="sceneInfo.id"
                class="scene-item-wrapper"
              >
                <!-- 场次内容 -->
                <div class="scene-item" :class="getSceneItemClass(sceneInfo)">
                  <!-- 场次基本信息 -->
                  <div class="scene-header"  v-if="sceneInfo.planType === 0">
                    <div class="scene-number">
                      {{ sceneInfo.sceneNumber || '未知场次' }}
                    </div>
                    <div class="scene-status">
                      <van-tag
                        v-if="sceneInfo.planType === 0"
                        :type="sceneInfo.verification ? 'success' : 'warning'"
                        size="large"
                      >
                        {{ sceneInfo.verification ? '已销场' : '未销场' }}
                      </van-tag>
                      <van-tag
                        v-else-if="sceneInfo.planType === 1"
                        type="primary"
                        size="large"
                      >
                        场次备注
                      </van-tag>
                      <van-tag
                        v-else-if="sceneInfo.planType === 2"
                        type="default"
                        size="large"
                      >
                        转场
                      </van-tag>
                    </div>
                  </div>

                  <!-- 场次详细信息 - 仅 planType = 0 显示 -->
                  <div class="scene-details" v-if="sceneInfo.planType === 0">
                    <div class="detail-row" v-if="sceneInfo.atmosphere">
                      <span class="label">气氛：</span>
                      <span class="value">{{ sceneInfo.atmosphereStr || getAtmosphereText(sceneInfo.atmosphere) }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.locationType">
                      <span class="label">内/外景：</span>
                      <span class="value">{{ sceneInfo.locationTypeStr || getLocationTypeText(sceneInfo.locationType) }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.shootingLocation">
                      <span class="label">拍摄场景：</span>
                      <span class="value">{{ sceneInfo.shootingLocation }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.scriptLocation">
                      <span class="label">剧本场景：</span>
                      <span class="value">{{ sceneInfo.scriptLocation }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.mainContent">
                      <span class="label">主要内容：</span>
                      <span class="value">{{ sceneInfo.mainContent }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.pageNumber">
                      <span class="label">页数：</span>
                      <span class="value">{{ sceneInfo.pageNumber }}</span>
                    </div>
                  </div>

                  <!-- 备注信息 - planType = 1/2 只显示备注 -->
                  <div class="scene-remark" v-if="sceneInfo.planType === 1 || sceneInfo.planType === 2">
                    <div class="remark-content">
                      {{ sceneInfo.remark ||sceneInfo.planType === 1 ? '分割' : "转场" }}
                    </div>
                  </div>

                  <!-- 销场信息 - 仅 planType = 0 且已销场时显示 -->
                  <div class="verification-info" v-if="sceneInfo.planType === 0 && sceneInfo.verification">
                    <div class="verification-header">
                      <van-icon name="passed" color="#07c160" />
                      <span class="verification-title">销场信息</span>
                    </div>
                    <div class="verification-details">
                      <div class="detail-row" v-if="sceneInfo.verification.creator">
                        <span class="label">销场人：</span>
                        <span class="value">{{ sceneInfo.verification.creator }}</span>
                      </div>
                      <div class="detail-row" v-if="sceneInfo.verification.createTime">
                        <span class="label">销场时间：</span>
                        <span class="value">{{ formatDateTime(sceneInfo.verification.createTime) }}</span>
                      </div>
                      <div class="detail-row" v-if="sceneInfo.verification.remark">
                        <span class="label">备注：</span>
                        <span class="value">{{ sceneInfo.verification.remark }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 滑动操作按钮 - 仅 planType = 0 的场次支持销场操作 -->
                <template #right v-if="sceneInfo.planType === 0">
                  <van-button
                    v-if="!sceneInfo.verification"
                    square
                    type="primary"
                    text="销场"
                    class="swipe-action-btn"
                    @click="handleVerifyScene(sceneCall, sceneInfo)"
                  />
                  <van-button
                    v-else
                    square
                    type="danger"
                    text="取消销场"
                    class="swipe-action-btn"
                    @click="handleCancelVerification(sceneCall, sceneInfo)"
                  />
                </template>
              </van-swipe-cell>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>

    <!-- 销场确认弹窗 -->
    <van-popup v-model:show="showVerifyDialog" position="bottom" round>
      <div class="verify-dialog">
        <div class="dialog-header">
          <h3>确认销场</h3>
        </div>
        <div class="dialog-content">
          <div class="scene-info">
            <p><strong>场次：</strong>{{ selectedScene?.sceneNumber }}</p>
            <p v-if="selectedScene?.shootingLocation">
              <strong>拍摄场景：</strong>{{ selectedScene.shootingLocation }}
            </p>
          </div>
          <van-field
            v-model="verifyRemark"
            type="textarea"
            label="备注"
            placeholder="请输入销场备注（可选）"
            rows="3"
            maxlength="200"
            show-word-limit
          />
        </div>
        <div class="dialog-actions">
          <van-button block @click="showVerifyDialog = false">取消</van-button>
          <van-button block type="primary" @click="confirmVerifyScene" :loading="verifyLoading">
            确认销场
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import useSceneCallStore from '@/stores/sceneCall'
import useLoginStore from '@/stores/login'

const router = useRouter()
const route = useRoute()
const sceneCallStore = useSceneCallStore()
const loginStore = useLoginStore()

// 响应式数据
const activeTab = ref(0)
const showVerifyDialog = ref(false)
const selectedScene = ref(null)
const selectedSceneCall = ref(null)
const verifyRemark = ref('')
const verifyLoading = ref(false)

// 计算属性
const loading = computed(() => sceneCallStore.loading)
const error = computed(() => sceneCallStore.error)
const sceneCallList = computed(() => sceneCallStore.sceneCallList)
const currentRole = computed(() => loginStore.getCurrentRole)

// 获取项目ID
const projectId = computed(() => route.params.projectId)

// 获取标签页标题
const getTabTitle = (sceneCall) => {
  if (sceneCall.dayNumber) {
    return `第${sceneCall.dayNumber}天`
  }
  if (sceneCall.createTime) {
    return dayjs(sceneCall.createTime).format('MM-DD')
  }
  return '通告单'
}

// 获取场次项样式类
const getSceneItemClass = (sceneInfo) => {
  const classes = []
  if (sceneInfo.planType === 0) {
    // 可拍摄场次
    classes.push('scene-shootable')
    if (sceneInfo.verification) {
      classes.push('scene-verified')
    }
  } else if (sceneInfo.planType === 1) {
    // 场次备注
    classes.push('scene-remark')
  } else if (sceneInfo.planType === 2) {
    // 转场
    classes.push('scene-transfer')
  }
  return classes
}

// 获取气氛文本
const getAtmosphereText = (atmosphere) => {
  const atmosphereMap = {
    1: '日',
    2: '夜',
    3: '日转夜',
    4: '夜转日'
  }
  return atmosphereMap[atmosphere] || '未知'
}

// 获取内外景文本
const getLocationTypeText = (locationType) => {
  const locationMap = {
    1: '内景',
    2: '外景'
  }
  return locationMap[locationType] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 处理销场操作
const handleVerifyScene = (sceneCall, sceneInfo) => {
  selectedSceneCall.value = sceneCall
  selectedScene.value = sceneInfo
  verifyRemark.value = ''
  showVerifyDialog.value = true
}

// 确认销场
const confirmVerifyScene = async () => {
  if (!selectedScene.value || !selectedSceneCall.value) {
    showToast('请选择要销场的场次')
    return
  }

  verifyLoading.value = true
  try {
    // 构建销场记录数据
    const verification = {
      productionId: parseInt(projectId.value),
      sceneNumber: selectedScene.value.sceneNumber,
      callId: selectedSceneCall.value.id,
      remark: verifyRemark.value || ''
    }

    // 根据当前角色确定 personType
    const personType = currentRole.value === 'actor' ? 1 : 0

    // 调用销场接口
    const result = await sceneCallStore.saveSceneVerification(personType, verification)

    if (result.status) {
      showToast('销场成功')
      showVerifyDialog.value = false
      selectedScene.value = null
      selectedSceneCall.value = null
      verifyRemark.value = ''

      // 刷新数据
      await sceneCallStore.fetchSceneCall(parseInt(projectId.value), personType)
    } else {
      showToast(result.message || '销场失败')
    }
  } catch (error) {
    console.error('销场失败:', error)
    showToast('销场失败，请重试')
  } finally {
    verifyLoading.value = false
  }
}

// 处理取消销场操作
const handleCancelVerification = async (sceneCall, sceneInfo) => {
  if (!sceneInfo.verification || !sceneInfo.verification.id) {
    showToast('未找到销场记录')
    return
  }

  try {
    await showConfirmDialog({
      title: '确认取消销场',
      message: `确定要取消场次"${sceneInfo.sceneNumber}"的销场记录吗？`,
      confirmButtonText: '确认取消',
      cancelButtonText: '保持销场'
    })

    // 根据当前角色确定 personType
    const personType = currentRole.value === 'actor' ? 1 : 0

    // 调用取消销场接口
    const result = await sceneCallStore.deleteSceneVerification(
      sceneInfo.verification.id,
      parseInt(projectId.value),
      personType
    )

    if (result.status) {
      showToast('取消销场成功')

      // 刷新数据
      await sceneCallStore.fetchSceneCall(parseInt(projectId.value), personType)
    } else {
      showToast(result.message || '取消销场失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消销场失败:', error)
      showToast('取消销场失败，请重试')
    }
  }
}

// 初始化页面数据
const initPageData = async () => {
  if (!projectId.value) {
    showToast('项目ID不能为空')
    router.back()
    return
  }

  try {
    // 根据当前角色确定 personType
    const personType = currentRole.value === 'actor' ? 1 : 0

    // 获取通告单数据
    await sceneCallStore.fetchSceneCall(parseInt(projectId.value), personType)
  } catch (error) {
    console.error('加载通告单数据失败:', error)
    showToast('加载失败，请重试')
  }
}

// 重试加载
const retryLoad = () => {
  initPageData()
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 页面挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.scene-call-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container,
.empty-container {
  padding: 40px 20px;
}

.scene-call-content {
  padding-bottom: 20px;
}

.scene-list {
  padding: 16px;
}

.empty-scenes {
  padding: 40px 20px;
}

.scene-item-wrapper {
  margin-bottom: 12px;
}

.scene-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ddd;
}

.scene-item.scene-shootable {
  border-left-color: #ff6600;
}

.scene-item.scene-verified {
  border-left-color: #07c160;
  background-color: #f7fff7;
}

.scene-item.scene-remark {
  border-left-color: #1989fa;
}

.scene-item.scene-transfer {
  border-left-color: #969799;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.scene-number {
  font-size: 18px;
  font-weight: bold;
  color: #323233;
}

.scene-status {
  flex-shrink: 0;
}

.scene-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #969799;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #323233;
  flex: 1;
  word-break: break-all;
}

.scene-remark {
  margin-top: 8px;
}

.remark-content {
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
  background-color: #f2f3f5;
  padding: 12px;
  border-radius: 6px;
}

.verification-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
}

.verification-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.verification-title {
  margin-left: 6px;
  font-size: 14px;
  font-weight: bold;
  color: #07c160;
}

.verification-details {
  font-size: 13px;
}

.swipe-action-btn {
  height: 100%;
  border-radius: 0;
}

/* 销场确认弹窗样式 */
.verify-dialog {
  padding: 20px;
}

.dialog-header {
  text-align: center;
  margin-bottom: 20px;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: #323233;
}

.dialog-content {
  margin-bottom: 20px;
}

.scene-info {
  background-color: #f7f8fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.scene-info p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #646566;
}

.scene-info p:last-child {
  margin-bottom: 0;
}

.dialog-actions {
  display: flex;
  gap: 12px;
}

.dialog-actions .van-button {
  flex: 1;
}

/* 标签页样式调整 */
:deep(.van-tabs__nav) {
  background-color: white;
}

:deep(.van-tabs__content) {
  background-color: #f7f8fa;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .scene-item {
    padding: 12px;
  }

  .scene-number {
    font-size: 16px;
  }

  .detail-row {
    font-size: 13px;
  }

  .label {
    min-width: 70px;
  }
}
</style>