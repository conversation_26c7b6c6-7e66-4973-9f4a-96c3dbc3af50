<template>
  <div class="today-scene-verify-page">
    <!-- 导航栏 -->
    <van-nav-bar title="当日销场" left-arrow @click-left="handleBack" />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      加载中...
    </van-loading>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败">
        <van-button round type="primary" @click="retryLoad">
          重新加载
        </van-button>
      </van-empty>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="!hasSceneData" class="empty-container">
      <van-empty description="今日暂无销场数据" />
    </div>

    <!-- 销场内容 -->
    <div v-else class="scene-verify-content">
      <!-- 销场统计 -->
      <van-cell-group class="stats-section">
        <div class="section-title">销场统计</div>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ sceneStats.total }}</div>
            <div class="stat-label">总场次</div>
          </div>
          <div class="stat-item verified">
            <div class="stat-number">{{ sceneStats.verified }}</div>
            <div class="stat-label">已销场</div>
          </div>
          <div class="stat-item unverified">
            <div class="stat-number">{{ sceneStats.unverified }}</div>
            <div class="stat-label">未销场</div>
          </div>
          <div class="stat-item rate">
            <div class="stat-number">{{ sceneStats.verificationRate }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
      </van-cell-group>

      <!-- 场次列表 -->
      <div class="scene-list">
        <div class="section-title">场次列表</div>
        <div v-if="!hasScenes" class="empty-scenes">
          <van-empty description="暂无场次数据" />
        </div>
        <div v-else>
          <van-swipe-cell v-for="sceneInfo in todayScenes" :key="sceneInfo.id" class="scene-item-wrapper">
            <!-- 场次内容 -->
            <div class="scene-item" :class="getSceneItemClass(sceneInfo)">
              <!-- 可拍摄场次 -->
              <div v-if="sceneInfo.planType === 0" class="scene-shootable-content">
                <div class="scene-header">
                  <div class="scene-number">
                    {{ sceneInfo.sceneNumber || '未知场次' }}
                  </div>
                  <div class="scene-status">
                    <van-tag :type="sceneInfo.verification ? 'success' : 'warning'" size="large">
                      {{ sceneInfo.verification ? '已销场' : '未销场' }}
                    </van-tag>
                  </div>
                </div>

                <!-- 场次详细信息 - 仅 planType = 0 显示 -->
                <div class="scene-details">
                  <div class="detail-row" v-if="sceneInfo.atmosphere">
                    <span class="label">气氛：</span>
                    <span class="value">{{ sceneInfo.atmosphereStr || getAtmosphereText(sceneInfo.atmosphere) }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.locationType">
                    <span class="label">内/外景：</span>
                    <span class="value">{{ sceneInfo.locationTypeStr || getLocationTypeText(sceneInfo.locationType)
                      }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.shootingLocation">
                    <span class="label">拍摄场景：</span>
                    <span class="value">{{ sceneInfo.shootingLocation }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.scriptLocation">
                    <span class="label">剧本场景：</span>
                    <span class="value">{{ sceneInfo.scriptLocation }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.mainContent">
                    <span class="label">主要内容：</span>
                    <span class="value">{{ sceneInfo.mainContent }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.pageNumber">
                    <span class="label">页数：</span>
                    <span class="value">{{ sceneInfo.pageNumber }}</span>
                  </div>
                </div>

                <!-- 顺序调整按钮 - 所有场次类型且有权限时显示 -->
                <div class="scene-sort-actions" v-if="hasSceneVerifyPermission">
                  <van-button icon="arrow-up" type="default" size="mini" :disabled="isFirstScene(sceneInfo)"
                    :loading="addScenesLoading" @click="moveSceneUp(sceneInfo)">
                    上移
                  </van-button>
                  <van-button icon="arrow-down" type="default" size="mini" :disabled="isLastScene(sceneInfo)"
                    :loading="addScenesLoading" @click="moveSceneDown(sceneInfo)">
                    下移
                  </van-button>
                </div>

                <!-- 销场信息 - 仅已销场时显示 -->
                <div class="verification-info" v-if="sceneInfo.verification">
                  <div class="verification-header">
                    <van-icon name="passed" color="#07c160" />
                    <span class="verification-title">销场信息</span>
                  </div>
                  <div class="verification-details">
                    <div class="detail-row" v-if="sceneInfo.verification.creator">
                      <span class="label">销场人：</span>
                      <span class="value">{{ sceneInfo.verification.creator }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.verification.createTime">
                      <span class="label">销场时间：</span>
                      <span class="value">{{ formatDateTime(sceneInfo.verification.createTime) }}</span>
                    </div>
                    <div class="detail-row" v-if="sceneInfo.verification.remark">
                      <span class="label">备注：</span>
                      <span class="value">{{ sceneInfo.verification.remark }}</span>
                    </div>
                  </div>
                </div>


              </div>

              <!-- 备注信息 - planType = 1/2 只显示备注 -->
              <div class="scene-remark" v-if="sceneInfo.planType === 1 || sceneInfo.planType === 2">
                <div class="remark-content">
                  {{ sceneInfo.remark || (sceneInfo.planType === 1 ? '分割' : '转场') }}
                </div>
              </div>
            </div>

            <!-- 滑动操作 -->
            <template #right v-if="sceneInfo.planType === 0">
              <van-button v-if="!sceneInfo.verification" square type="primary" text="销场" class="swipe-action"
                :loading="verifyLoading" @click="handleVerifyScene(sceneInfo)" />
              <van-button v-else square type="warning" text="取消" class="swipe-action" :loading="verifyLoading"
                @click="handleCancelVerification(sceneInfo)" />
              <van-button square type="danger" text="删除" class="swipe-action" :loading="verifyLoading"
                @click="handleDeleteScene(sceneInfo)" />
            </template>
          </van-swipe-cell>
        </div>
      </div>
    </div>

    <!-- 批量添加场次浮动按钮 -->
    <van-floating-bubble v-if="hasSceneVerifyPermission" axis="xy" icon="plus" @click="handleBatchAddScenes" />

    <!-- 销场对话框 -->
    <van-dialog v-model:show="showVerifyDialog" title="销场确认" show-cancel-button confirm-button-text="确认销场"
      cancel-button-text="取消" :confirm-button-loading="verifyLoading" @confirm="confirmVerifyScene"
      @cancel="cancelVerifyDialog">
      <div class="verify-dialog-content">
        <div class="scene-info">
          场次: {{ selectedScene?.sceneNumber }}
        </div>
        <van-field v-model="verifyRemark" type="textarea" placeholder="请输入销场备注（可选）" rows="3" maxlength="200"
          show-word-limit />
      </div>
    </van-dialog>

    <!-- 批量添加场次弹出层 -->
    <van-popup v-model:show="showBatchAddPopup" position="bottom" :style="{ height: '80%' }" round closeable
      close-icon-position="top-right">
      <div class="batch-add-container">
        <div class="batch-add-header">
          <h3>批量添加场次</h3>
          <p class="batch-add-desc">从大计划中选择场次添加到当日通告单</p>
        </div>

        <!-- 加载状态 -->
        <van-loading v-if="planLoading" class="loading-container" vertical>
          加载大计划中...
        </van-loading>

        <!-- 场次选择列表 -->
        <div v-else-if="selectableScenes.length > 0" class="scene-select-list">
          <van-checkbox-group v-model="selectedSceneIds">
            <van-cell-group>
              <van-cell v-for="scene in selectableScenes" :key="scene.id" :title="scene.sceneNumber"
                :label="getSceneDescription(scene)" clickable @click="toggleSceneSelection(scene.id)">
                <template #right-icon>
                  <van-checkbox :name="scene.id" @click.stop />
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </div>

        <!-- 无可选场次 -->
        <div v-else class="empty-scenes">
          <van-empty description="暂无可添加的场次" />
        </div>

        <!-- 操作按钮 -->
        <div class="batch-add-actions">
          <van-button type="default" size="large" @click="cancelBatchAdd">
            取消
          </van-button>
          <van-button type="primary" size="large" :loading="addScenesLoading" :disabled="selectedSceneIds.length === 0"
            @click="confirmBatchAdd">
            添加 {{ selectedSceneIds.length }} 个场次
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import dayjs from 'dayjs'
import useTodaySceneVerifyStore from '@/stores/todaySceneVerify'
import useLoginStore from '@/stores/login'
import useHomeStore from '@/stores/home'
import { SCENE_VERIFY_ALLOWED_ROLES } from '@/consts/index'

const router = useRouter()
const route = useRoute()
const todaySceneVerifyStore = useTodaySceneVerifyStore()
const loginStore = useLoginStore()
const homeStore = useHomeStore()

// 响应式数据
const showVerifyDialog = ref(false)
const selectedScene = ref(null)
const verifyRemark = ref('')
const showBatchAddPopup = ref(false)
const selectedSceneIds = ref([])


// 计算属性
const loading = computed(() => todaySceneVerifyStore.isLoading)
const verifyLoading = computed(() => todaySceneVerifyStore.isVerifyLoading)
const error = computed(() => todaySceneVerifyStore.getError)
const hasSceneData = computed(() => todaySceneVerifyStore.hasSceneData)
const hasScenes = computed(() => todaySceneVerifyStore.hasScenes)
const todayScenes = computed(() => todaySceneVerifyStore.getTodayScenes)
const sceneStats = computed(() => todaySceneVerifyStore.getSceneStats)
const currentRole = computed(() => loginStore.getCurrentRole)
const planLoading = computed(() => todaySceneVerifyStore.isPlanLoading)
const addScenesLoading = computed(() => todaySceneVerifyStore.isAddScenesLoading)
const selectableScenes = computed(() => todaySceneVerifyStore.getSelectableScenes)
const accountInfo = computed(() => homeStore.accountInfo)

// 检查是否有当日销场权限
const hasSceneVerifyPermission = computed(() => {
  // 只有工作人员才有权限
  if (currentRole.value !== 'staff') {
    return false
  }

  // 检查工作人员的角色类型是否在允许的列表中
  const userRoleType = accountInfo.value?.roleType
  return userRoleType && SCENE_VERIFY_ALLOWED_ROLES.includes(userRoleType)
})

// 获取项目ID
const projectId = computed(() => route.params.projectId)

// 获取气氛文本
const getAtmosphereText = (atmosphere) => {
  const atmosphereMap = {
    1: '日',
    2: '夜',
    3: '日转夜',
    4: '夜转日'
  }
  return atmosphereMap[atmosphere] || '未知'
}

// 获取内外景文本
const getLocationTypeText = (locationType) => {
  const locationMap = {
    1: '内景',
    2: '外景'
  }
  return locationMap[locationType] || '未知'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '未设置'
  return dayjs(dateStr).format('YYYY年MM月DD日')
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '未设置'
  return dayjs(dateStr).format('MM月DD日 HH:mm')
}

// 获取场次项样式类
const getSceneItemClass = (sceneInfo) => {
  const classes = []
  if (sceneInfo.planType === 0) {
    classes.push('scene-shootable')
    if (sceneInfo.verification) {
      classes.push('verified')
    }
  } else if (sceneInfo.planType === 1) {
    classes.push('scene-remark')
  } else if (sceneInfo.planType === 2) {
    classes.push('scene-transfer')
  }
  return classes
}

// 处理销场操作
const handleVerifyScene = (sceneInfo) => {
  selectedScene.value = sceneInfo
  verifyRemark.value = ''
  showVerifyDialog.value = true
}

// 确认销场 - 与 sceneCall.vue 保持一致的逻辑
const confirmVerifyScene = async () => {
  if (!selectedScene.value) {
    showToast('请选择要销场的场次')
    return
  }

  // 检查是否有当日销场数据
  if (!todaySceneVerifyStore.todaySceneData || !todaySceneVerifyStore.todaySceneData.id) {
    showToast('无法获取通告单ID，请重新加载页面')
    return
  }

  try {
    // 构建销场记录数据 - 与 sceneCall.vue 保持一致
    const verification = {
      productionId: parseInt(projectId.value),
      sceneNumber: selectedScene.value.sceneNumber,
      callId: todaySceneVerifyStore.todaySceneData.id,
      remark: verifyRemark.value || ''
    }

    // 根据当前角色确定 personType
    const personType = currentRole.value === 'actor' ? 1 : 0

    // 调用销场接口 - 使用与 sceneCall 相同的方法
    const result = await todaySceneVerifyStore.saveSceneVerification(personType, verification)

    if (result.status) {
      showToast('销场成功')
      showVerifyDialog.value = false
      selectedScene.value = null
      verifyRemark.value = ''

      // 刷新数据
      await todaySceneVerifyStore.fetchTodaySceneData(parseInt(projectId.value), personType)
    } else {
      showToast(result.message || '销场失败')
    }
  } catch (error) {
    console.error('销场失败:', error)
    showToast('销场失败，请重试')
  }
}

// 取消销场对话框
const cancelVerifyDialog = () => {
  showVerifyDialog.value = false
  selectedScene.value = null
  verifyRemark.value = ''
}

// 处理取消销场 - 与 sceneCall.vue 保持一致的逻辑
const handleCancelVerification = async (sceneInfo) => {
  if (!sceneInfo.verification || !sceneInfo.verification.id) {
    showToast('未找到销场记录')
    return
  }

  try {
    await showConfirmDialog({
      title: '确认取消销场',
      message: `确定要取消场次"${sceneInfo.sceneNumber}"的销场记录吗？`,
      confirmButtonText: '确认取消',
      cancelButtonText: '保持销场'
    })

    // 根据当前角色确定 personType
    const personType = currentRole.value === 'actor' ? 1 : 0

    // 调用取消销场接口 - 使用与 sceneCall 相同的方法
    const result = await todaySceneVerifyStore.deleteSceneVerification(
      sceneInfo.verification.id,
      parseInt(projectId.value),
      personType
    )

    if (result.status) {
      showToast('取消销场成功')

      // 刷新数据
      await todaySceneVerifyStore.fetchTodaySceneData(parseInt(projectId.value), personType)
    } else {
      showToast(result.message || '取消销场失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消销场失败:', error)
      showToast('取消销场失败，请重试')
    }
  }
}

// 处理删除场次
const handleDeleteScene = async (sceneInfo) => {
  try {
    await showConfirmDialog({
      title: '确认删除场次',
      message: `确定要删除场次"${sceneInfo.sceneNumber}"吗？删除后无法恢复。`,
      confirmButtonText: '确认删除',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24'
    })

    const personType = currentRole.value === 'actor' ? 1 : 0
    const result = await todaySceneVerifyStore.deleteScene(
      sceneInfo.id,
      parseInt(projectId.value),
      personType
    )

    if (result.status) {
      showToast('删除场次成功')
    } else {
      showToast(result.message || '删除场次失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除场次失败:', error)
      showToast('删除场次失败，请重试')
    }
  }
}

// 初始化页面数据
const initPageData = async () => {
  if (!projectId.value) {
    showToast('项目ID不能为空')
    router.back()
    return
  }

  try {
    const personType = currentRole.value === 'actor' ? 1 : 0
    await todaySceneVerifyStore.fetchTodaySceneData(parseInt(projectId.value), personType)
  } catch (error) {
    console.error('加载当日销场数据失败:', error)
    showToast('加载失败，请重试')
  }
}

// 重试加载
const retryLoad = () => {
  initPageData()
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 获取场次描述
const getSceneDescription = (scene) => {
  const parts = []
  if (scene.atmosphereStr || scene.atmosphere) {
    parts.push(scene.atmosphereStr || getAtmosphereText(scene.atmosphere))
  }
  if (scene.locationTypeStr || scene.locationType) {
    parts.push(scene.locationTypeStr || getLocationTypeText(scene.locationType))
  }
  if (scene.shootingLocation) {
    parts.push(scene.shootingLocation)
  }
  if (scene.mainContent) {
    parts.push(scene.mainContent)
  }
  return parts.join(' · ') || '暂无描述'
}

// 处理批量添加场次
const handleBatchAddScenes = async () => {
  try {
    // 获取大计划数据
    const result = await todaySceneVerifyStore.fetchScenePlan(parseInt(projectId.value))
    if (result.status) {
      selectedSceneIds.value = []
      showBatchAddPopup.value = true
    } else {
      showToast(result.message || '获取大计划失败')
    }
  } catch (error) {
    console.error('获取大计划失败:', error)
    showToast('获取大计划失败，请重试')
  }
}

// 切换场次选择状态
const toggleSceneSelection = (sceneId) => {
  const index = selectedSceneIds.value.indexOf(sceneId)
  if (index > -1) {
    selectedSceneIds.value.splice(index, 1)
  } else {
    selectedSceneIds.value.push(sceneId)
  }
}

// 确认批量添加
const confirmBatchAdd = async () => {
  if (selectedSceneIds.value.length === 0) {
    showToast('请选择要添加的场次')
    return
  }

  // 检查是否有当日销场数据
  if (!todaySceneVerifyStore.todaySceneData?.id) {
    showToast('无法获取通告单ID，请重新加载页面')
    return
  }

  try {
    // 获取选中的场次数据
    const selectedScenes = selectableScenes.value.filter(scene =>
      selectedSceneIds.value.includes(scene.id)
    )

    // 从当日销场数据中获取callId
    const callId = todaySceneVerifyStore.todaySceneData.id

    // 获取现有的场次数据
    const existingScenes = todayScenes.value

    // 计算新场次的起始sort值（在原来最后一项的基础上）
    const maxSort = existingScenes.length > 0
      ? Math.max(...existingScenes.map(scene => scene.sort || 0))
      : 0

    // 为新场次设置sort值
    const newScenes = selectedScenes.map((scene, index) => ({
      ...scene,
      sort: maxSort + index + 1,
      callId: callId
    }))

    // 合并现有场次和新场次
    const combinedScenes = [...existingScenes, ...newScenes]

    const result = await todaySceneVerifyStore.addScenesToCall(callId, combinedScenes, false)

    if (result.status) {
      showToast('批量添加场次成功')
      showBatchAddPopup.value = false
      selectedSceneIds.value = []
      // 重新获取当日销场数据
      await initPageData()
    } else {
      showToast(result.message || '批量添加场次失败')
    }
  } catch (error) {
    console.error('批量添加场次失败:', error)
    showToast('批量添加场次失败，请重试')
  }
}

// 取消批量添加
const cancelBatchAdd = () => {
  showBatchAddPopup.value = false
  selectedSceneIds.value = []
}

// 判断是否是第一个场次
const isFirstScene = (sceneInfo) => {
  const scenes = todayScenes.value
  return scenes.length > 0 && scenes[0].id === sceneInfo.id
}

// 判断是否是最后一个场次
const isLastScene = (sceneInfo) => {
  const scenes = todayScenes.value
  return scenes.length > 0 && scenes[scenes.length - 1].id === sceneInfo.id
}

// 上移场次
const moveSceneUp = async (sceneInfo) => {
  const scenes = [...todayScenes.value]
  const index = scenes.findIndex(scene => scene.id === sceneInfo.id)

  if (index <= 0) return

  // 交换位置
  const temp = scenes[index]
  scenes[index] = scenes[index - 1]
  scenes[index - 1] = temp

  await updateSceneOrder(scenes)
}

// 下移场次
const moveSceneDown = async (sceneInfo) => {
  const scenes = [...todayScenes.value]
  const index = scenes.findIndex(scene => scene.id === sceneInfo.id)

  if (index >= scenes.length - 1) return

  // 交换位置
  const temp = scenes[index]
  scenes[index] = scenes[index + 1]
  scenes[index + 1] = temp

  await updateSceneOrder(scenes)
}

// 更新场次顺序
const updateSceneOrder = async (reorderedScenes) => {
  if (!todaySceneVerifyStore.todaySceneData?.id) {
    showToast('无法获取通告单ID，请重新加载页面')
    return
  }

  try {
    const callId = todaySceneVerifyStore.todaySceneData.id

    // 构造保存数据，重新设置sort字段
    const infos = reorderedScenes.map((scene, index) => ({
      id: scene.id,
      callId: callId,
      sort: index + 1,
      planType: scene.planType,
      sceneNumber: scene.sceneNumber,
      atmosphere: scene.atmosphere,
      locationType: scene.locationType,
      pageNumber: scene.pageNumber,
      lineNumber: scene.lineNumber,
      shootingLocation: scene.shootingLocation,
      scriptLocation: scene.scriptLocation,
      scene: scene.scene,
      mainContent: scene.mainContent,
      costumeMakeupTip: scene.costumeMakeupTip,
      mainActors: scene.mainActors,
      groupExtraActors: scene.groupExtraActors,
      specialActors: scene.specialActors,
      remark: scene.remark,
      consultation: scene.consultation || ''
    }))

    const result = await todaySceneVerifyStore.addScenesToCall(callId, infos, true)

    if (result.status) {
      showToast('调整顺序成功')
      // 重新获取当日销场数据
      await initPageData()
    } else {
      showToast(result.message || '调整顺序失败')
    }
  } catch (error) {
    console.error('更新场次顺序失败:', error)
    showToast('调整顺序失败，请重试')
  }
}

// 页面挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.today-scene-verify-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container,
.empty-container {
  padding: 50px 20px;
}

.stats-section {
  margin: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  padding: 12px 0 8px 8px;
  border-bottom: 1px solid #ebedf0;
  margin-bottom: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
}

.stat-item {
  text-align: center;
  padding: 12px 8px;
  border-radius: 6px;
  background: #f8f9fa;
}

.stat-item.verified {
  background: #e8f5e8;
}

.stat-item.unverified {
  background: #fff3e0;
}

.stat-item.rate {
  background: #e3f2fd;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #323233;
  margin-bottom: 4px;
}

.stat-item.verified .stat-number {
  color: #07c160;
}

.stat-item.unverified .stat-number {
  color: #ff6600;
}

.stat-item.rate .stat-number {
  color: #1989fa;
}

.stat-label {
  font-size: 12px;
  color: #969799;
}

.scene-list {
  margin: 16px;
}

.empty-scenes {
  padding: 40px 20px;
}

.scene-item-wrapper {
  margin-bottom: 12px;
}

.scene-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.scene-item.verified {
  border-left: 4px solid #07c160;
}

.scene-shootable-content {
  padding: 16px;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.scene-number {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
}

.scene-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  margin-bottom: 6px;
  font-size: 14px;
}

.detail-row .label {
  color: #969799;
  width: 80px;
  flex-shrink: 0;
}

.detail-row .value {
  color: #323233;
  flex: 1;
}

.verification-info {
  margin-top: 12px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 3px solid #07c160;
}

.verification-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.verification-title {
  margin-left: 6px;
  font-size: 14px;
  font-weight: bold;
  color: #07c160;
}

.verification-details {
  margin-left: 20px;
}

.scene-remark {
  margin-bottom: 12px;
}

.remark-content {
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  color: #646566;
  border-left: 3px solid #ff6600;
}





.swipe-action {
  height: 100%;
}

.verify-dialog-content {
  padding: 16px;
}

.verify-dialog-content .scene-info {
  font-size: 14px;
  color: #323233;
  margin-bottom: 12px;
  font-weight: bold;
}

.batch-add-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.batch-add-header {
  margin-bottom: 20px;
}

.batch-add-header h3 {
  font-size: 18px;
  font-weight: bold;
  color: #323233;
  margin: 0 0 8px 0;
}

.batch-add-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.scene-select-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.batch-add-actions {
  display: flex;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
}

.batch-add-actions .van-button {
  flex: 1;
}

.empty-scenes {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scene-sort-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.scene-sort-actions .van-button {
  min-width: 60px;
}
</style>
