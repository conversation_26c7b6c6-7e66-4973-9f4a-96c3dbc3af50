<template>
  <div class="room-clean-page">
    <!-- 导航栏 -->
    <van-nav-bar title="房间保洁" :border="false" />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      <template #icon>
        <van-icon name="spinner" class="loading-icon" />
      </template>
      加载中...
    </van-loading>

    <!-- 主要内容 -->
    <div v-else class="content">
      <!-- 标签页 -->
      <van-tabs
        v-model:active="activeTab"
        sticky
        color="#ff6600"
        title-active-color="#ff6600"
        title-inactive-color="#969799"
      >
        <!-- 待打扫标签页 -->
        <van-tab title="待打扫">
          <div class="tab-content">
            <div v-if="pendingCleanList.length === 0" class="empty-container">
              <van-empty description="暂无待打扫房间" />
            </div>
            <div v-else>
              <!-- 小区列表 -->
              <div v-for="community in pendingCleanList" :key="community.id" class="community-section">
                <div class="community-header">
                  <h3 class="community-name">{{ community.communityName }}</h3>
                  <p class="community-address">{{ community.communityArea }}</p>
                </div>

                <!-- 楼栋列表 -->
                <div v-for="building in community.buildings" :key="building.buildingNumber" class="building-section">
                  <div class="building-header">
                    <span class="building-title">{{ building.buildingNumber }}栋</span>
                    <span class="room-count">{{ building.rooms.length }}间房</span>
                  </div>

                  <!-- 房间列表 -->
                  <div class="room-list">
                    <div v-for="room in building.rooms" :key="room.id" class="room-item">
                      <div class="room-info">
                        <div class="room-number">{{ room.roomNumber }}{{ room.roomPosition || '' }}</div>
                        <div class="room-details">
                          <span class="room-type">{{ getRoomTypeText(room.roomType) }}</span>
                          <span class="room-level">{{ getRoomLevelText(room.roomLevel) }}</span>
                        </div>
                      </div>
                      <div class="room-actions">
                        <van-button
                          size="small"
                          type="warning"
                          :loading="updateLoading"
                          @click="updateRoomStatus(room, 1)"
                        >
                          开始打扫
                        </van-button>
                        <van-button
                          size="small"
                          type="success"
                          :loading="updateLoading"
                          @click="updateRoomStatus(room, 2)"
                        >
                          完成
                        </van-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-tab>

        <!-- 打扫中标签页 -->
        <van-tab title="打扫中">
          <div class="tab-content">
            <div v-if="cleaningList.length === 0" class="empty-container">
              <van-empty description="暂无正在打扫的房间" />
            </div>
            <div v-else>
              <!-- 小区列表 -->
              <div v-for="community in cleaningList" :key="community.id" class="community-section">
                <div class="community-header">
                  <h3 class="community-name">{{ community.communityName }}</h3>
                  <p class="community-address">{{ community.communityArea }}</p>
                </div>

                <!-- 楼栋列表 -->
                <div v-for="building in community.buildings" :key="building.buildingNumber" class="building-section">
                  <div class="building-header">
                    <span class="building-title">{{ building.buildingNumber }}栋</span>
                    <span class="room-count">{{ building.rooms.length }}间房</span>
                  </div>

                  <!-- 房间列表 -->
                  <div class="room-list">
                    <div v-for="room in building.rooms" :key="room.id" class="room-item cleaning">
                      <div class="room-info">
                        <div class="room-number">{{ room.roomNumber }}{{ room.roomPosition || '' }}</div>
                        <div class="room-details">
                          <span class="room-type">{{ getRoomTypeText(room.roomType) }}</span>
                          <span class="room-level">{{ getRoomLevelText(room.roomLevel) }}</span>
                          <span class="cleaning-status">打扫中</span>
                        </div>
                      </div>
                      <div class="room-actions">
                        <van-button
                          size="small"
                          type="success"
                          :loading="updateLoading"
                          @click="updateRoomStatus(room, 2)"
                        >
                          完成
                        </van-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import useRoomCleanStore from '@/stores/roomClean'

const router = useRouter()
const roomCleanStore = useRoomCleanStore()

// 响应式数据
const activeTab = ref(0)

// 计算属性
const loading = computed(() => roomCleanStore.loading)
const updateLoading = computed(() => roomCleanStore.updateLoading)
const pendingCleanList = computed(() => roomCleanStore.pendingCleanList)
const cleaningList = computed(() => roomCleanStore.cleaningList)
const communityList = computed(() => roomCleanStore.communityList)

// 房型文本映射
const getRoomTypeText = (roomType) => {
  const typeMap = {
    1: '单床房',
    2: '双床房', 
    3: '三床房',
    4: '四床房',
    5: '五床房',
    6: '六床房'
  }
  return typeMap[roomType] || '未知房型'
}

// 房间等级文本映射
const getRoomLevelText = (roomLevel) => {
  const levelMap = {
    1: '优',
    2: '良', 
    3: '一般'
  }
  return levelMap[roomLevel] || '未知等级'
}

// 更新房间状态
const updateRoomStatus = async (room, newStatus) => {
  if (!room.roomClean?.id) {
    showToast('房间保洁信息不完整')
    return
  }

  const statusText = {
    1: '开始打扫',
    2: '完成打扫'
  }

  try {
    await showConfirmDialog({
      title: '确认操作',
      message: `确定要${statusText[newStatus]}吗？`
    })

    const result = await roomCleanStore.updateCleanStatus(room.roomClean.id, newStatus)
    if (result.status) {
      showToast(`${statusText[newStatus]}成功`)
    } else {
      showToast(result.message || `${statusText[newStatus]}失败`)
    }
  } catch (error) {
    // 用户取消操作
  }
}



// 页面挂载时获取数据
onMounted(async () => {
  await roomCleanStore.fetchRoomCleanList()
})
</script>

<style scoped lang="scss">
@import '@/assets/styles/variables.scss';

.room-clean-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content {
  padding-bottom: 20px;
}

.tab-content {
  padding: 16px;
}

.empty-container {
  padding: 60px 0;
}

.community-section {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
}

.community-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebedf0;
}

.community-name {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 4px 0;
}

.community-address {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.building-section {
  border-bottom: 1px solid #ebedf0;
  
  &:last-child {
    border-bottom: none;
  }
}

.building-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafbfc;
}

.building-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
}

.room-count {
  font-size: 12px;
  color: #969799;
}

.room-list {
  padding: 0 16px;
}

.room-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebedf0;
  
  &:last-child {
    border-bottom: none;
  }

}

.room-info {
  flex: 1;
}

.room-number {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 4px;
}

.room-details {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.room-type, .room-level {
  color: #646566;
}

.cleaning-status {
  color: #ff6600;
  font-weight: 500;
}

.room-actions {
  display: flex;
  gap: 8px;
}

/* 标签页样式调整 */
:deep(.van-tabs__nav) {
  background-color: white;
}

:deep(.van-tabs__content) {
  background-color: #f7f8fa;
}
</style>
