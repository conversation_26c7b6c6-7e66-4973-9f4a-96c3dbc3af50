<template>
  <div class="bg-primary">
    <van-nav-bar title="项目住宿" :border="false" left-arrow @click-left="handleBack" />

    <div class="accommodation-page">
      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading && !projectInfo">
        <van-loading size="24px" vertical>加载中...</van-loading>
      </div>

      <!-- 错误状态 -->
      <div class="error-state" v-else-if="error">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <van-button type="primary" @click="retryLoad" :loading="loading">
          重新加载
        </van-button>
      </div>

      <!-- 项目信息和住宿内容 -->
      <div class="accommodation-content" v-else-if="projectInfo">
        <!-- 项目基本信息 -->
        <div class="project-info-card">
          <div class="project-header">
            <h2 class="project-title">{{ projectInfo.productionName }}</h2>
          </div>

          <div class="project-details">
            <div class="detail-item" v-if="projectInfo.secondProductionCode">
              <span class="label">项目编号:</span>
              <span class="value">{{ projectInfo.secondProductionCode }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.startDate">
              <span class="label">开机时间:</span>
              <span class="value">{{ formatDate(projectInfo.startDate) }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.endDate">
              <span class="label">杀青时间:</span>
              <span class="value">{{ formatDate(projectInfo.endDate) }}</span>
            </div>

            <div class="detail-item" v-if="projectInfo.cityName">
              <span class="label">拍摄地点:</span>
              <span class="value">{{ projectInfo.cityName }}</span>
            </div>
          </div>
        </div>

        <!-- 住宿信息 -->
        <div class="accommodation-section">
          <!-- 有住宿信息时显示详情 -->
          <div class="room-info-card" v-if="projectRoomInfo">
            <div class="room-header">
              <span class="room-icon">🏠</span>
              <span class="room-title">住宿信息</span>
            </div>

            <div class="room-details">
              <div class="detail-item" v-if="projectRoomInfo.communityName">
                <span class="label">小区名称:</span>
                <span class="value">{{ projectRoomInfo.communityName }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.communityArea">
                <span class="label">详细地址:</span>
                <span class="value">{{ projectRoomInfo.communityArea }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.buildingNumber && projectRoomInfo.roomNumber">
                <span class="label">房间号:</span>
                <span class="value">{{ projectRoomInfo.buildingNumber }}栋{{ projectRoomInfo.roomNumber }}号</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.roomPosition">
                <span class="label">房间位置:</span>
                <span class="value">{{ projectRoomInfo.roomPosition }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.roomType">
                <span class="label">房间类型:</span>
                <span class="value">{{ getRoomTypeText(projectRoomInfo.roomType) }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.startTime">
                <span class="label">入住时间:</span>
                <span class="value">{{ projectRoomInfo.startTime }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.endTime">
                <span class="label">退房时间:</span>
                <span class="value">{{ projectRoomInfo.endTime }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.mainDoorPassword">
                <span class="label">大门密码:</span>
                <span class="value password">{{ projectRoomInfo.mainDoorPassword }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.innerDoorPassword">
                <span class="label">房门密码:</span>
                <span class="value password">{{ projectRoomInfo.innerDoorPassword }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.broadbandAccount">
                <span class="label">宽带账号:</span>
                <span class="value">{{ projectRoomInfo.broadbandAccount }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.broadbandPassword">
                <span class="label">宽带密码:</span>
                <span class="value password">{{ projectRoomInfo.broadbandPassword }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.equipments">
                <span class="label">房间设备:</span>
                <span class="value">{{ projectRoomInfo.equipments }}</span>
              </div>

              <div class="detail-item" v-if="projectRoomInfo.remark">
                <span class="label">备注信息:</span>
                <span class="value">{{ projectRoomInfo.remark }}</span>
              </div>
            </div>
          </div>

          <!-- 无住宿信息时显示提示 -->
          <div class="no-room-info" v-else>
            <div class="no-room-icon">🏠</div>
            <div class="no-room-text">暂无住宿信息</div>
            <div class="no-room-desc">请联系项目负责人获取住宿安排</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import useMyProjectStore from '@/stores/myProject'
import useLoginStore from '@/stores/login'

const router = useRouter()
const route = useRoute()
const myProjectStore = useMyProjectStore()
const loginStore = useLoginStore()

// 页面状态
const loading = ref(false)
const error = ref('')

// 从store获取数据
const projectInfo = computed(() => myProjectStore.getCurrentProject)
const projectRoomInfo = computed(() => myProjectStore.getCurrentProjectRoom)

// 初始化页面数据
const initPageData = async () => {
  const projectId = route.params.projectId
  if (!projectId) {
    error.value = '缺少项目ID'
    return
  }

  loading.value = true
  error.value = ''

  try {
    const personType = loginStore.getCurrentPersonType

    // 如果当前没有项目信息，需要先获取项目列表
    if (!projectInfo.value) {
      const projectsRes = await myProjectStore.fetchMyProjects(personType)
      if (!projectsRes.status) {
        error.value = projectsRes.message || '获取项目信息失败'
        return
      }

      // 从项目列表中找到对应的项目
      const targetProject = myProjectStore.getProjectList.find(p => p.id == projectId)
      if (targetProject) {
        myProjectStore.setCurrentProject(targetProject)
      } else {
        error.value = '未找到指定项目'
        return
      }
    }

    // 获取房间信息
    const roomRes = await myProjectStore.fetchCurrentProjectRoom(personType)
    if (!roomRes.status && roomRes.message !== '暂无房间信息') {
      console.warn('获取房间信息失败:', roomRes.message)
    }

  } catch (err) {
    console.error('初始化页面数据失败:', err)
    error.value = err.message || '网络错误'
  } finally {
    loading.value = false
  }
}

// 重试加载
const retryLoad = () => {
  initPageData()
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 获取房间类型文本
const getRoomTypeText = (roomType) => {
  const typeMap = {
    1: '单人间',
    2: '双人间'
  }
  return typeMap[roomType] || '未知类型'
}

// 页面初始化
onMounted(() => {
  // 检查登录状态
  if (!localStorage.getItem('token')) {
    router.push('/login')
    return
  }

  // 初始化页面数据
  initPageData()
})
</script>

<style lang="scss" scoped>
.accommodation-page {
  padding: 16px;
  min-height: calc(100vh - 46px);
  background: #f5f5f5;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.error-state {
  .error-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .error-text {
    color: #666;
    margin-bottom: 24px;
    font-size: 14px;
  }
}

.accommodation-content {
  .project-info-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .project-header {
      margin-bottom: 16px;

      .project-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }

    .project-details {
      .detail-item {
        display: flex;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #666;
          font-size: 14px;
          min-width: 80px;
          flex-shrink: 0;
        }

        .value {
          color: #333;
          font-size: 14px;
          flex: 1;
        }
      }
    }
  }

  .accommodation-section {
    .room-info-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .room-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .room-icon {
          font-size: 18px;
          margin-right: 8px;
        }

        .room-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .room-details {
        .detail-item {
          display: flex;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            color: #666;
            font-size: 14px;
            min-width: 100px;
            flex-shrink: 0;
          }

          .value {
            color: #333;
            font-size: 14px;
            flex: 1;

            &.password {
              font-family: 'Courier New', monospace;
              background: #f5f5f5;
              padding: 2px 6px;
              border-radius: 4px;
              font-weight: 600;
            }
          }
        }
      }
    }

    .no-room-info {
      background: white;
      border-radius: 12px;
      padding: 40px 20px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .no-room-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }

      .no-room-text {
        font-size: 16px;
        color: #666;
        margin-bottom: 8px;
      }

      .no-room-desc {
        font-size: 14px;
        color: #999;
        line-height: 1.5;
      }
    }
  }
}
</style>
