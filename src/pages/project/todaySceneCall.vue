<template>
  <div class="today-scene-call-page">
    <!-- 导航栏 -->
    <van-nav-bar title="" left-arrow @click-left="handleBack" />

    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      加载中...
    </van-loading>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <van-empty description="加载失败">
        <van-button round type="primary" @click="retryLoad">
          重新加载
        </van-button>
      </van-empty>
    </div>

    <!-- 无数据状态 -->
    <div v-else-if="!hasSceneCallData" class="empty-container">
      <van-empty description="今日暂无通告单" />
    </div>

    <!-- 通告单内容 -->
    <div v-else class="scene-call-content">
      <!-- 通告单基本信息 -->
      <div class="scene-call-info" v-if="sceneCallInfo">
        <div class="info-card">
          <div class="info-header">
            <h3 class="info-title">今日通告单</h3>
            <span class="day-number" v-if="sceneCallInfo.dayNumber">第{{ sceneCallInfo.dayNumber }}天</span>
          </div>

          <div class="info-content">
            <div class="info-item" v-if="weatherInfo">
              <div class="info-value">{{ weatherInfo }}</div>
            </div>

            <div class="info-item" v-if="sceneCallInfo.scheduleRemark">
              <div class="info-label">行程安排</div>
              <div class="info-value multiline-text">{{ sceneCallInfo.scheduleRemark }}</div>
            </div>

            <div class="info-item" v-if="sceneCallInfo.responsibleDept">
              <div class="info-label">责任部门</div>
              <div class="info-value multiline-text">{{ sceneCallInfo.responsibleDept }}</div>
            </div>

            <div class="info-item" v-if="sceneCallInfo.contact">
              <div class="info-label">联系方式</div>
              <div class="info-value">{{ sceneCallInfo.contact }}</div>
            </div>

            <div class="info-item" v-if="sceneCallInfo.remark">
              <div class="info-label">备注</div>
              <div class="info-value multiline-text">{{ sceneCallInfo.remark }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用餐信息 -->
      <div class="meals-section" v-if="meals.length > 0">
        <div class="section-title">用餐安排</div>
        <van-cell-group>
          <van-cell v-for="meal in meals" :key="meal.id" :title="meal.mealTypeStr"
            :value="`${meal.mealTime} - ${meal.location}`" />
        </van-cell-group>
      </div>

      <!-- 演员信息 -->
      <div class="actors-section" v-if="actors.length > 0">
        <div class="section-title">演员安排</div>
        <van-cell-group>
          <van-cell v-for="actor in actors" :key="actor.id" :title="`${actor.roleName} (${actor.actorName})`"
            :value="getRoleTypeText(actor.roleType)">
            <template #label v-if="actor.arrivalTime || actor.makeupStartTime">
              <div class="actor-time-info">
                <span v-if="actor.arrivalTime">到场: {{ actor.arrivalTime }}</span>
                <span v-if="actor.makeupStartTime">化妆: {{ actor.makeupStartTime }}</span>
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <van-cell-group>
          <van-cell title="拍摄统计" :value="`拍摄场次${sceneStats.scenes}场，转场${sceneStats.transfers}次`" />
        </van-cell-group>
      </div>

      <!-- 场景列表 -->
      <div class="scene-list">
        <div v-if="!hasSceneData" class="empty-scenes">
          <van-empty description="暂无场次数据" />
        </div>
        <div v-else>
          <div v-for="sceneInfo in todayScenes" :key="sceneInfo.id" class="scene-item-wrapper">
            <!-- 场次内容 -->
            <div class="scene-item" :class="getSceneItemClass(sceneInfo)">
              <!-- 场次基本信息 -->
              <div class="scene-header" v-if="sceneInfo.planType === 0">
                <div class="scene-number">
                  {{ sceneInfo.sceneNumber || '未知场次' }}
                </div>
                <div class="scene-status">
                  <van-tag :type="sceneInfo.verification ? 'success' : 'warning'" size="large">
                    {{ sceneInfo.verification ? '已销场' : '未销场' }}
                  </van-tag>
                </div>
              </div>

              <!-- 场次详细信息 - 仅 planType = 0 显示 -->
              <div class="scene-details" v-if="sceneInfo.planType === 0">
                <div class="detail-row" v-if="sceneInfo.atmosphere">
                  <span class="label">气氛：</span>
                  <span class="value">{{ sceneInfo.atmosphereStr || getAtmosphereText(sceneInfo.atmosphere) }}</span>
                </div>
                <div class="detail-row" v-if="sceneInfo.locationType">
                  <span class="label">内/外景：</span>
                  <span class="value">{{ sceneInfo.locationTypeStr || getLocationTypeText(sceneInfo.locationType)
                    }}</span>
                </div>
                <div class="detail-row" v-if="sceneInfo.shootingLocation">
                  <span class="label">拍摄场景：</span>
                  <span class="value">{{ sceneInfo.shootingLocation }}</span>
                </div>
                <div class="detail-row" v-if="sceneInfo.scriptLocation">
                  <span class="label">剧本场景：</span>
                  <span class="value">{{ sceneInfo.scriptLocation }}</span>
                </div>
                <div class="detail-row" v-if="sceneInfo.mainContent">
                  <span class="label">主要内容：</span>
                  <span class="value">{{ sceneInfo.mainContent }}</span>
                </div>
                <div class="detail-row" v-if="sceneInfo.pageNumber">
                  <span class="label">页数：</span>
                  <span class="value">{{ sceneInfo.pageNumber }}</span>
                </div>
              </div>

              <!-- 备注信息 - planType = 1/2 只显示备注 -->
              <div class="scene-remark" v-if="sceneInfo.planType === 1 || sceneInfo.planType === 2">
                <div class="remark-content">
                  {{ sceneInfo.remark || (sceneInfo.planType === 1 ? '分割' : '转场') }}
                </div>
              </div>

              <!-- 销场信息 - 仅 planType = 0 且已销场时显示 -->
              <div class="verification-info" v-if="sceneInfo.planType === 0 && sceneInfo.verification">
                <div class="verification-header">
                  <van-icon name="passed" color="#07c160" />
                  <span class="verification-title">销场信息</span>
                </div>
                <div class="verification-details">
                  <div class="detail-row" v-if="sceneInfo.verification.creator">
                    <span class="label">销场人：</span>
                    <span class="value">{{ sceneInfo.verification.creator }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.verification.createTime">
                    <span class="label">销场时间：</span>
                    <span class="value">{{ formatDateTime(sceneInfo.verification.createTime) }}</span>
                  </div>
                  <div class="detail-row" v-if="sceneInfo.verification.remark">
                    <span class="label">备注：</span>
                    <span class="value">{{ sceneInfo.verification.remark }}</span>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from 'vant'
import dayjs from 'dayjs'
import useTodaySceneCallStore from '@/stores/todaySceneCall'
import useLoginStore from '@/stores/login'

const router = useRouter()
const route = useRoute()
const todaySceneCallStore = useTodaySceneCallStore()
const loginStore = useLoginStore()

// 计算属性
const loading = computed(() => todaySceneCallStore.isLoading)
const error = computed(() => todaySceneCallStore.getError)
const hasSceneCallData = computed(() => todaySceneCallStore.hasSceneCallData)
const hasSceneData = computed(() => todaySceneCallStore.hasSceneData)
const sceneCallInfo = computed(() => todaySceneCallStore.getSceneCallInfo)
const meals = computed(() => todaySceneCallStore.getMeals)
const actors = computed(() => todaySceneCallStore.getActors)
const todayScenes = computed(() => todaySceneCallStore.getTodayScenes)
const sceneStats = computed(() => todaySceneCallStore.getSceneStats)
const currentRole = computed(() => loginStore.getCurrentRole)
const weatherInfo = computed(() => todaySceneCallStore.getWeatherInfo)

// 获取项目ID
const projectId = computed(() => route.params.projectId)

// 获取气氛文本
const getAtmosphereText = (atmosphere) => {
  const atmosphereMap = {
    1: '日',
    2: '夜',
    3: '日转夜',
    4: '夜转日'
  }
  return atmosphereMap[atmosphere] || '未知'
}

// 获取内外景文本
const getLocationTypeText = (locationType) => {
  const locationMap = {
    1: '内景',
    2: '外景'
  }
  return locationMap[locationType] || '未知'
}

// 获取角色类型文本
const getRoleTypeText = (roleType) => {
  const roleTypeMap = {
    1: '协领主演',
    2: '主演',
    3: '特邀'
  }
  return roleTypeMap[roleType] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 获取场次项样式类
const getSceneItemClass = (sceneInfo) => {
  const classes = []
  if (sceneInfo.planType === 0) {
    // 可拍摄场次
    classes.push('scene-shootable')
  } else if (sceneInfo.planType === 1) {
    // 场次备注
    classes.push('scene-remark')
  } else if (sceneInfo.planType === 2) {
    // 转场
    classes.push('scene-transfer')
  }
  return classes
}




// 初始化页面数据
const initPageData = async () => {
  if (!projectId.value) {
    showToast('项目ID不能为空')
    router.back()
    return
  }

  try {
    // 根据当前角色确定 personType
    const personType = currentRole.value === 'actor' ? 1 : 0

    // 获取当天通告单数据
    await todaySceneCallStore.fetchTodaySceneCall(parseInt(projectId.value), personType)
  } catch (error) {
    console.error('加载当天通告单数据失败:', error)
    showToast('加载失败，请重试')
  }
}

// 重试加载
const retryLoad = () => {
  initPageData()
}

// 返回上一页
const handleBack = () => {
  router.back()
}

// 页面挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<style scoped>
.today-scene-call-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error-container,
.empty-container {
  padding: 50px 20px;
}

.scene-call-info,
.meals-section,
.actors-section,
.stats-section {
  margin: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  padding: 12px 0 8px 0;
  border-bottom: 1px solid #ebedf0;
  margin-bottom: 12px;
}

.actor-time-info {
  font-size: 12px;
  color: #969799;
  margin-top: 4px;
}

.actor-time-info span {
  margin-right: 12px;
}

.multiline-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.5;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebedf0;
}

.info-title {
  font-size: 18px;
  font-weight: bold;
  color: #323233;
  margin: 0;
}

.day-number {
  background: #ff6600;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  font-weight: bold;
  color: #646566;
}

.info-value {
  font-size: 14px;
  color: #323233;
  line-height: 1.5;
}

.scene-content {
  padding-bottom: 20px;
}

.scene-list {
  margin: 16px;
}

.scene-item-wrapper {
  margin-bottom: 12px;
}

.scene-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ddd;
}

.scene-shootable {
  border-left-color: #ff6600;
}



.scene-remark {
  border-left-color: #1989fa;
  background-color: #f0f9ff;
}

.scene-transfer {
  border-left-color: #969799;
  background-color: #f8f8f8;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.scene-number {
  font-size: 18px;
  font-weight: bold;
  color: #323233;
}

.scene-status {
  flex-shrink: 0;
}

.scene-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  flex-direction: row;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #969799;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #323233;
  flex: 1;
  word-break: break-all;
}



.remark-content,
.scene-remark {
  margin-top: 8px;
}

.remark-content {
  font-size: 14px;
  color: #646566;
  line-height: 1.5;
  background-color: #f2f3f5;
  padding: 12px;
  border-radius: 6px;
}

.verification-info {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebedf0;
}

.verification-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.verification-title {
  margin-left: 6px;
  font-size: 14px;
  font-weight: bold;
  color: #07c160;
}

.verification-details {
  font-size: 13px;
}
</style>
