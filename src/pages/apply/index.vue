<template>
  <div class="page">
    <!-- 导航栏 -->
    <van-nav-bar title="我的报名" :border="false" left-arrow @click-left="handleBack" />

    <div class="content">
      <!-- 加载状态 -->
      <div v-if="loading && applicationList.length === 0">
        <van-loading size="36px" color="#ff6600" vertical>加载中...</van-loading>
      </div>

      <!-- 报名列表 -->
      <div class="list" v-else-if="applicationList.length > 0">
        <Card v-for="application in applicationList" :key="application.id">
          <template #header>
            <div class="space-between">
              <strong class="title-3">{{
                getRoleTypeText(application.roleType, application.parentType)
              }}</strong>
              <strong>{{ application.productionName }}</strong>
            </div>
          </template>
          <div>
            <div class="space-between" v-if="application.introduction">
              <span>个人介绍</span>
              <span class="value">{{ application.introduction }}</span>
            </div>

            <div class="space-between" v-if="application.expectedSalary">
              <span>期望报酬</span>
              <span class="value salary">{{
                formatSalary(application.expectedSalary, application.currency)
              }}</span>
            </div>

            <div class="space-between" v-if="application.createTime">
              <span>报名时间</span>

              <van-space class="value time" justify="end"
                >{{ formatDate(application.createTime) }}
                <van-button
                  type="default"
                  size="mini"
                  v-if="
                    application.status !== 4 &&
                    application.status !== 3 &&
                    application.status !== 5 &&
                    application.status !== 2
                  "
                  @click="showCancelDialog(application)"
                  :disabled="cancelingIds.includes(application.id)">
                  {{ cancelingIds.includes(application.id) ? '取消中...' : '取消报名' }}
                </van-button></van-space
              >
            </div>

            <!-- 介绍文件 -->
            <div class="space-between" v-if="application.introductionUrls">
              <div class="detail-label">附件</div>
              <div class="value">
                <van-space>
                  <van-tag
                    type="primary"
                    plain
                    v-for="(file, index) in getFileList(application.introductionUrls)"
                    :key="index"
                    @click="previewFile(file)">
                    <div class="file-icon">{{ getFileIcon(file) }}</div>
                  </van-tag>
                </van-space>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <!-- 空状态 -->
      <van-empty v-else description="暂无数据" />
    </div>

    <!-- 取消报名确认对话框 -->
    <van-dialog
      v-model:show="showCancelConfirm"
      title="取消报名"
      :message="`确定要取消「${selectedApplication?.productionName}」报名吗？`"
      show-cancel-button
      confirm-button-text="确认取消"
      cancel-button-text="我再想想"
      @confirm="confirmCancel"
      @cancel="showCancelConfirm = false" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showImagePreview } from 'vant'
import useRecruitmentStore from '@/stores/recruitment'
import useLoginStore from '@/stores/login'
import { showToast } from 'vant'
import { getRoleTypeText } from '@/consts/index'
import { API } from '@/utils/env'
import Card from '@/components/Card.vue'

const router = useRouter()
const recruitmentStore = useRecruitmentStore()
const loginStore = useLoginStore()

// 响应式数据
const applicationList = ref([])
const loading = ref(false)
const error = ref(null)
const showCancelConfirm = ref(false)
const selectedApplication = ref(null)
const cancelingIds = ref([])

// 获取当前角色
const currentRole = computed(() => loginStore.getCurrentRole)

// 返回上一页
const handleBack = () => {
  router.back()
}

// 获取已报名列表
const fetchApplications = async () => {
  loading.value = true
  error.value = null

  try {
    const personType = loginStore.getCurrentPersonType
    const result = await recruitmentStore.fetchPersonApplications(personType)

    if (result.status && result.data?.dataList) {
      // 将分组的数据转换为平铺的报名列表
      const flatApplications = []
      const projectList = Array.isArray(result.data.dataList)
        ? result.data.dataList
        : [result.data.dataList]

      projectList.forEach(project => {
        if (project.personApplication && Array.isArray(project.personApplication)) {
          project.personApplication.forEach(application => {
            // 将项目信息合并到报名信息中
            flatApplications.push({
              ...application,
              productionName: project.productionName,
              projectStartDate: project.startDate,
              projectEndDate: project.endDate,
              projectDescription: project.description,
              currency: project.priceCurrency || 1, // 从项目级别获取货币类型
            })
          })
        }
      })
      applicationList.value = flatApplications
    } else {
      applicationList.value = []
      error.value = result.message || '获取报名列表失败'
    }
  } catch (err) {
    console.error('获取报名列表失败:', err)
    error.value = err.message || '网络错误'
    applicationList.value = []
  } finally {
    loading.value = false
  }
}

// 显示取消报名对话框
const showCancelDialog = application => {
  selectedApplication.value = application
  showCancelConfirm.value = true
}

// 确认取消报名
const confirmCancel = async () => {
  if (!selectedApplication.value) return

  const applicationId = selectedApplication.value.id
  cancelingIds.value.push(applicationId)

  try {
    const result = await recruitmentStore.cancelApplication(applicationId)
    if (result.status) {
      showToast('取消报名成功')
      applicationList.value = []
      // 重新获取列表

      await fetchApplications()
    } else {
      showToast(result.message || '取消报名失败')
    }
  } catch (error) {
    console.error('取消报名失败:', error)
    showToast('取消报名失败，请重试')
  } finally {
    cancelingIds.value = cancelingIds.value.filter(id => id !== applicationId)
    showCancelConfirm.value = false
    selectedApplication.value = null
  }
}

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    1: '待审核',
    2: '审核通过',
    3: '已报名',
    // 3: '审核未通过',
    4: '已取消',
    5: '已完成',
  }
  return statusMap[status] || '未知状态'
}

// 获取状态样式类
const getStatusClass = status => {
  const classMap = {
    1: 'pending',
    2: 'approved',
    3: 'rejected',
    4: 'cancelled',
    5: 'completed',
  }
  return classMap[status] || ''
}

// 格式化薪资
const formatSalary = (salary, currency) => {
  if (!salary) return ''
  const currencySymbol = { 1: '¥', 2: '$', 3: '¥' }[currency] || '¥'
  return `${currencySymbol}${salary}`
}

// 格式化日期
const formatDate = dateString => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 获取文件列表
const getFileList = urlString => {
  if (!urlString) return []
  return urlString
    .split(',')
    .filter(Boolean)
    .map(url => url.trim())
}

// 获取文件图标
const getFileIcon = url => {
  if (!url) return '📄'
  const lowerUrl = url.toLowerCase()

  if (lowerUrl.includes('image') || /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(url)) return '🖼️'
  if (lowerUrl.includes('video') || /\.(mp4|avi|mov|wmv|flv|mkv)$/i.test(url)) return '🎬'
  if (lowerUrl.includes('audio') || /\.(mp3|wav|ogg|aac)$/i.test(url)) return '🎵'
  if (/\.(pdf)$/i.test(url)) return '📕'
  if (/\.(doc|docx)$/i.test(url)) return '📘'
  if (/\.(xls|xlsx)$/i.test(url)) return '📗'

  return '📄'
}

// 获取文件名
const getFileName = url => {
  if (!url) return '未知文件'
  const fileName = url.split('/').pop() || url
  return fileName.length > 20 ? fileName.substring(0, 17) + '...' : fileName
}

// 预览文件
const previewFile = url => {
  if (!url) return

  const fullUrl = url.startsWith('http') ? url : API + url
  const fileType = getFileIcon(url)

  if (fileType === '🖼️') {
    // 图片预览
    showImagePreview([fullUrl])
  } else {
    // 其他文件直接打开
    window.open(fullUrl, '_blank')
  }
}

// 页面初始化
onMounted(() => {
  fetchApplications()
})
</script>

<style scoped lang="scss">
.page {
  min-height: 100vh;
  background: #f5f5f5;
}

.content {
  padding: 12px;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.time {
  justify-content: end;
}

.value {
  color: #333;
  flex: 1;
  text-align: right;
  line-height: 2.4;

  &.salary {
    color: #ff8124;
  }
}
</style>
