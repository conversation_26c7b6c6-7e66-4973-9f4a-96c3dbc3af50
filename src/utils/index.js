import COS from 'cos-js-sdk-v5'
import Handlebars from 'handlebars'
import axios from 'axios'
import useStore from '@/stores/home'
import { showToast } from 'vant'
import { useEnv } from '@fe/base'

import Cookies from 'js-cookie'
import CryptoJS from 'crypto-js'
// 5a3e8f7d1c9b0a2f4e6d5c8b7a9e0f1d 5a3e8f7d1c9b0a2f4e6d5c8b7a9e0f1d2c3b4a5d6e7f8a9b0c1d2e3f4a5b6c7d8
const SECRET_KEY = CryptoJS.enc.Utf8.parse('5a3e8f7d1c9b0a2f4e6d5c8b7a9e0f1d')
// 3f4a5d6e7f8a9b0c 3f4a5d6e7f8a9b0c1d2e3f4a5b6c7d8
const iv = CryptoJS.enc.Utf8.parse('3f4a5d6e7f8a9b0c')

/**
 * 设置加密的 Cookie
 * @param {string} key - Cookie 键名
 * @param {string} value - Cookie 值
 * @param {Object} options - Cookie 配置项
 */
export function setEncryptedCookie(key, value, options = {}) {
  const curValue = typeof value === 'string' ? value : JSON.stringify(value)
  const encryptedValue = CryptoJS.AES.encrypt(curValue, SECRET_KEY, { iv }).toString()
  Cookies.set(key, encryptedValue, options)
}

/**
 * 获取并解密的 Cookie
 * @param {string} key - Cookie 键名
 * @returns {string} - 解密后的值
 */
export function getDecryptedCookie(key) {
  const encryptedValue = Cookies.get(key)
  if (!encryptedValue) return null
  const bytes = CryptoJS.AES.decrypt(encryptedValue, SECRET_KEY, { iv })
  console.log(bytes.toString(CryptoJS.enc.Utf8), bytes)
  return bytes.toString(CryptoJS.enc.Utf8)
}

export const EncryptedCookies = {
  set: setEncryptedCookie,
  get: getDecryptedCookie,
  remove: Cookies.remove,
}

// 解析query
export const getQueryParams = (url = window.location.href) => {
  const theRequest = {}

  if (url.indexOf('?') !== -1) {
    const str = url.split('?')[1] || ''
    const params = str.split('&')

    for (let i = 0; i < params.length; i++) {
      theRequest[params[i].split('=')[0]] = decodeURIComponent(params[i].split('=')[1])
    }
  }

  return theRequest
}

export const debounce = (func, wait = 300) => {
  let timeout

  return function () {
    const context = this,
      args = arguments
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(context, args), wait)
  }
}

// 节流
export const throttle = (func, wait = 300) => {
  let context, args
  let timeoutId
  let previous = 0

  const later = function () {
    previous = Date.now()
    timeoutId = null
    func.apply(context, args)
  }

  return function (...theArgs) {
    context = this
    args = theArgs
    const now = Date.now()

    if (!timeoutId && now - previous > wait) {
      later()
    } else if (timeoutId) {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(later, wait)
    } else {
      timeoutId = setTimeout(later, wait - (now - previous))
    }
  }
}

// 获取时间范围
export const getTimeRange = range => {
  const today = new Date()
  let startDate, endDate

  switch (range) {
    case 0:
      startDate = new Date(today)
      endDate = new Date(today)
      break
    case 1:
      startDate = new Date(today)
      startDate.setDate(startDate.getDate() - 1)
      endDate = new Date(startDate)
      break
    case 2:
      startDate = new Date(today)
      startDate.setDate(startDate.getDate() - 6)
      endDate = new Date(today)
      break
    case 3:
      startDate = new Date(today)
      startDate.setDate(startDate.getDate() - 14)
      endDate = new Date(today)
      break
    case 4:
      startDate = new Date(today.getFullYear(), today.getMonth(), 1)
      endDate = new Date(today)
      break
    case 5:
      startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      endDate = new Date(today.getFullYear(), today.getMonth(), 0)
      break
    default:
      startDate = null
      endDate = null
  }

  return { startDate, endDate }
}

/**
 * cos文件上传
 * @param {string} file - 模板内容html
 * @param {string} filename - 文件名
 * @param {object} auth - 临时授权信息
 * @param {function} fn - 回调方法
 */
export const fileUpload = (file, filename, auth, fn) => {
  const cos = new COS({
    Protocol: 'https:',
    getAuthorization(options, callback) {
      callback({
        TmpSecretId: auth.tmpSecretId, // 临时密钥的 tmpSecretId
        TmpSecretKey: auth.tmpSecretKey, // 临时密钥的 tmpSecretKey
        SecurityToken: auth.token, // 临时密钥的 sessionToken
        // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
        StartTime: +auth.startTime, // 时间戳，单位秒，如：1580000000
        ExpiredTime: +auth.expiredTime, // 临时密钥失效时间戳，是申请临时密钥时，时间戳加 durationSeconds
      })
    },
  })

  const BASE_BUCKET =
    systemLanguage === 'cn'
      ? {
          Bucket: 'koc-gz-1319644609',
          Region: 'ap-guangzhou',
        }
      : {
          Bucket: 'koc-hongkong-1319644609',
          Region: 'ap-hongkong',
        }

  cos.putObject(
    {
      ...BASE_BUCKET,
      Key: `${filename}`,
      Body: file, // 上传文件对象
      ContentDisposition: 'inline',
      ContentType: 'text/html', // 设置 Content-Type
    },
    (err, data) => {
      if (data?.statusCode === 200) {
        // message.success('文件上传成功')
        fn(true)
      } else {
        fn(false)
      }
      // message.warning('文件上传失败')
    }
  )
}

// 邮箱校验
export function validateEmail(email) {
  // 正则表达式用于匹配基本的电子邮件格式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

  return emailRegex.test(email)
}

// 密码校验
export function validatePassword(password) {
  // 正则表达式用于匹配基本的电子邮件格式
  const emailRegex =
    /^(?![0-9]+$)(?![A-Za-z]+$)(?![_]+$)(?!.*[\u4E00-\u9FA5\uF900-\uFA2D])(?!^[!@#$%^&*()_+=\\[\\]{}|;:',.<>\/-?"·~\$`!\^"()#]+?$)[\S]{6,20}$/

  return emailRegex.test(password)
}

export const getRandomInt = (min, max) => {
  min = Math.ceil(min)
  max = Math.floor(max)

  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 获取当前状态
export const checkStatus = async (router, t, name = 'verify') => {
  const { fetchUserLastStatus } = useStore()
  const translation = window.t || t
  const userStatus = localStorage.userStatus || 1

  if (userStatus < 3) {
    const { status } = await fetchUserLastStatus()

    if (status >= 3) {
      localStorage.userStatus === status

      return true
    }

    showToast(translation('mkoc_my14'))
    router.push({ name })

    return false
  }

  return true
}

export const isMobile = (userAgent = navigator?.userAgent) =>
  /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Harmony/i.test(userAgent)

export const isCNAsync = async (loadnav = true) => {
  const userLang = navigator.language || navigator.userLanguage
  if (userLang.includes('CN') && loadnav) {
    return true
  }
  const { data } = await axios.get('https://ipinfo.io/json').catch(error => {
    console.error('Error fetching location:', error)

    return { data: {} }
  })

  return data?.country === 'CN'
}

export const isWeChat = (() => {
  const userAgent = window.navigator.userAgent.toLowerCase()
  const vendor = window.navigator.vendor
  const appVersion = window.navigator.appVersion.toLowerCase()

  return (
    /micromessenger/.test(userAgent) ||
    (vendor &&
      vendor.indexOf('Google Inc') >= 0 &&
      /chrome\/([\d.]+)/.test(appVersion) &&
      parseFloat(RegExp.$1) <= 59)
  )
})()

export const weChatLogin = ({ id, account }) => {
  console.log(window.location.href, window.location)
  const obj = new window.WxLogin({
    self_redirect: false,
    id,
    appid: 'wx0e3141adef3afa9d',
    scope: 'snsapi_login',
    redirect_uri: encodeURIComponent(
      `https://cdproductionserverwebapi.51changdu.com/Home/WxLogin?${
        account ? `account=${account}&` : ''
      }redirectUrl=${encodeURIComponent(location.href.split('?')[0])}`
    ),
    fast_login: 1,
    state: '',
    style: 'width: 200',
    href: '',
    stylelite: 1,
  })
}

export const getLangResource = async (code, key) => {
  const res = await axios.get(`https://web.cdreader.com/static/en/2283.${code}.json?v=*********`)

  return key ? res.data?.[key] : res.data
}

function measureTextHeight(el) {
  const styles = getComputedStyle(el)
  const tempDiv = document.createElement('div')
  tempDiv.style.lineHeight = styles.lineHeight
  tempDiv.style.fontFamily = styles.fontFamily
  tempDiv.style.fontSize = `${styles.fontSize}`
  tempDiv.style.fontWeight = `${styles.fontWeight}`
  tempDiv.style.letterSpacing = `${styles.letterSpacing}`
  tempDiv.style.width = `${styles.width}`

  tempDiv.innerText = el.innerText

  document.body.appendChild(tempDiv)
  const height = tempDiv.clientHeight
  document.body.removeChild(tempDiv)

  return height
}

export const checkTextOverflow = (element, line = 2) => {
  if (element) {
    const height = measureTextHeight(element)
    console.log(height, getComputedStyle(element).lineHeight)
    // 实际高度
    const actualHeight = parseFloat(getComputedStyle(element).lineHeight) * line

    return height > actualHeight
  }
}

export const systemLanguage = (() =>
  ['cn', 'en'].find(item => navigator.language?.toLocaleLowerCase()?.includes(item)) || 'cn')()

/**
 * once随机数
 * @param {number} len 畅读
 */
export function randomString(len = 32) {
  const $chars = 'abcdefhijkmnprstwxyz0123456789'
  const maxPos = $chars.length
  let nonce = ''
  for (let i = 0; i < len; i += 1) {
    nonce += $chars.charAt(Math.floor(Math.random() * maxPos))
  }

  return nonce
}

export const setCache = key => {
  const expireTime = 6 * 60 * 60 * 1000
  const value = Date.now() + expireTime

  localStorage.setItem(key, value.toString())
}

export const getCache = key => {
  const cacheDataStr = localStorage.getItem(key)

  if (!cacheDataStr) {
    return null
  }

  const value = Number(cacheDataStr)

  if (value < Date.now()) {
    return null
  }

  return cacheDataStr
}

export const toast = (message, duration = 2000, position = 'bottom') => {
  showToast({
    message,
    duration,
    zIndex: 9999,
    position,
  })
}

export function withFallback(target, fallback) {
  if (!target || (typeof target === 'object' && Object.keys(target).length === 0)) {
    return fallback
  }
  return new Proxy(target, {
    get(obj, prop) {
      const value = obj[prop]
      if (!value) {
        return fallback[prop]
      }
      return value
    },
  })
}
