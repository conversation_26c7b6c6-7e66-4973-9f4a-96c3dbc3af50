export default function dynamicLoadSdk(
  sdk_url,
  { defer, crossOrigin = 'anonymous', async = true } = {}
) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')

    script.type = 'text/javascript'
    if (async) {
      script.async = true
    }
    if (defer) {
      script.defer = true
    }
    if (crossOrigin) {
      script.crossOrigin = crossOrigin
    }
    script.src = sdk_url
    script.onload = () => {
      console.log(`${sdk_url}下载完成`)
      resolve()
    }
    script.onerror = () => {
      reject()
    }
    document.getElementsByTagName('head')[0].appendChild(script)
  })
}
