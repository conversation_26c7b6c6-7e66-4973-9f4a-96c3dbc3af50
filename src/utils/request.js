import axios from 'axios'
import { API } from '@/utils/env'
import { useEnv, useContextParams } from '@fe/base'
import { useBridge } from '@fe/bridge'
import { language } from '@/utils/env'
import { showToast } from 'vant'

const { account } = useContextParams()
const env = useEnv()

const TIMEOUT = 450 * 1000

const request = axios.create({ timeout: TIMEOUT })

const baseUrl = API

const handleLogin = () => {
  if (location.href.includes('pr.cdreader.com') && language() !== 'cn') {
    location.href = `${location.origin}/en/#/login`
  } else {
    location.href = `${location.origin}/#/login`
  }

  return true
}

request.interceptors.request.use(config => {
  if (!config.url.includes('user/login')) {
    config.headers.Authorization = `Bearer ${localStorage.token}`
  }
  config.baseURL = baseUrl
  config.headers.AreaInterface = language() || 'cn'

  return config
})
request.interceptors.response.use(
  response => {
    const { data = {}, message, status } = response || {}
    const { statusCode, message: msg, statusMsg, code = 312 } = data
    if (status !== 200 || code == 312 || code == -1) {
      // toast.destroy()
      // toast.error(message || msg || statusMsg || '请求异常')
      showToast(message || msg || statusMsg || '请求异常')

      return { status: false, message: message || msg || statusMsg, data: {} }
    }
    return { data: data?.data || data || {}, status: true, message: message || statusMsg }
  },
  error => {
    if (error?.message?.includes('401')) {
      if (env.isChangDu) {
        useBridge().util.ndaction('ndaction:goLogin()')
      } else {
        // handleLogin()
      }
    } else {
      if (error.code === 'ECONNABORTED' && error.message === `timeout of ${TIMEOUT}ms exceeded`) {
        error.message = `服务请求超时，超时${TIMEOUT / 1000}秒`
      }
      showToast(error.message)
      // toast.destroy()
      // toast.error(error.message)
    }

    return { status: false, message: error.message, data: {} }
  }
)

export const get = (url, params = {}, ...other) => request.get(url, { params }, ...other)
export const post = request.post
export default request
