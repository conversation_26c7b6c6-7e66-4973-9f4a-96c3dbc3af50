/**
 * 身份证号码工具函数
 */

// 身份证号码正则表达式
const ID_CARD_REGEX = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/

// 省份代码映射
const PROVINCE_CODE = {
  11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古',
  21: '辽宁', 22: '吉林', 23: '黑龙江',
  31: '上海', 32: '江苏', 33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东',
  41: '河南', 42: '湖北', 43: '湖南', 44: '广东', 45: '广西', 46: '海南',
  50: '重庆', 51: '四川', 52: '贵州', 53: '云南', 54: '西藏',
  61: '陕西', 62: '甘肃', 63: '青海', 64: '宁夏', 65: '新疆',
  71: '台湾', 81: '香港', 82: '澳门', 91: '国外'
}

/**
 * 验证身份证号码格式
 * @param {string} idCard 身份证号码
 * @returns {boolean} 是否有效
 */
export function validateIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') {
    return false
  }

  // 去除空格并转换为大写
  const cleanIdCard = idCard.replace(/\s/g, '').toUpperCase()

  // 长度检查
  if (cleanIdCard.length !== 18) {
    return false
  }

  // 格式检查
  if (!ID_CARD_REGEX.test(cleanIdCard)) {
    return false
  }

  // 校验码检查
  return validateCheckCode(cleanIdCard)
}

/**
 * 验证身份证校验码
 * @param {string} idCard 身份证号码
 * @returns {boolean} 校验码是否正确
 */
function validateCheckCode(idCard) {
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  let sum = 0
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idCard[i]) * weights[i]
  }

  const checkCodeIndex = sum % 11
  const expectedCheckCode = checkCodes[checkCodeIndex]

  return idCard[17] === expectedCheckCode
}

/**
 * 从身份证号码中提取生日
 * @param {string} idCard 身份证号码
 * @returns {object|null} 生日信息 {year, month, day, date}
 */
export function extractBirthFromIdCard(idCard) {
  if (!validateIdCard(idCard)) {
    return null
  }

  const cleanIdCard = idCard.replace(/\s/g, '').toUpperCase()
  const year = parseInt(cleanIdCard.substring(6, 10))
  const month = parseInt(cleanIdCard.substring(10, 12))
  const day = parseInt(cleanIdCard.substring(12, 14))

  // 验证日期有效性
  const date = new Date(year, month - 1, day)
  if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
    return null
  }

  return {
    year,
    month,
    day,
    date,
    dateString: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
  }
}

/**
 * 从身份证号码中提取性别
 * @param {string} idCard 身份证号码
 * @returns {number|null} 性别 1-男 2-女
 */
export function extractGenderFromIdCard(idCard) {
  if (!validateIdCard(idCard)) {
    return null
  }

  const cleanIdCard = idCard.replace(/\s/g, '').toUpperCase()
  const genderCode = parseInt(cleanIdCard[16])

  // 奇数为男性，偶数为女性
  return genderCode % 2 === 1 ? 1 : 2
}

/**
 * 从身份证号码中提取省份
 * @param {string} idCard 身份证号码
 * @returns {string|null} 省份名称
 */
export function extractProvinceFromIdCard(idCard) {
  if (!validateIdCard(idCard)) {
    return null
  }

  const cleanIdCard = idCard.replace(/\s/g, '').toUpperCase()
  const provinceCode = parseInt(cleanIdCard.substring(0, 2))

  return PROVINCE_CODE[provinceCode] || null
}

/**
 * 计算年龄
 * @param {string} idCard 身份证号码
 * @returns {number|null} 年龄
 */
export function calculateAgeFromIdCard(idCard) {
  const birthInfo = extractBirthFromIdCard(idCard)
  if (!birthInfo) {
    return null
  }

  const today = new Date()
  const birthDate = birthInfo.date
  let age = today.getFullYear() - birthDate.getFullYear()

  // 检查是否还没到生日
  const monthDiff = today.getMonth() - birthDate.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  return age
}

/**
 * 判断是否为未成年人
 * @param {string} idCard 身份证号码
 * @returns {boolean|null} 是否为未成年人
 */
export function isMinorFromIdCard(idCard) {
  const age = calculateAgeFromIdCard(idCard)
  if (age === null) {
    return null
  }

  return age < 18
}

/**
 * 从身份证号码中提取所有信息
 * @param {string} idCard 身份证号码
 * @returns {object|null} 身份证信息
 */
export function parseIdCard(idCard) {
  if (!validateIdCard(idCard)) {
    return null
  }

  const birthInfo = extractBirthFromIdCard(idCard)
  const gender = extractGenderFromIdCard(idCard)
  const province = extractProvinceFromIdCard(idCard)
  const age = calculateAgeFromIdCard(idCard)
  const isMinor = isMinorFromIdCard(idCard)

  return {
    valid: true,
    birth: birthInfo,
    gender,
    province,
    age,
    isMinor,
    genderText: gender === 1 ? '男' : '女'
  }
}

/**
 * 格式化身份证号码显示（隐藏中间部分）
 * @param {string} idCard 身份证号码
 * @returns {string} 格式化后的身份证号码
 */
export function formatIdCardForDisplay(idCard) {
  if (!idCard || idCard.length !== 18) {
    return idCard
  }

  return `${idCard.substring(0, 6)}********${idCard.substring(14)}`
}

/**
 * 身份证号码输入格式化（自动添加空格）
 * @param {string} value 输入值
 * @returns {string} 格式化后的值
 */
export function formatIdCardInput(value) {
  if (!value) return ''

  // 移除所有非数字和非X字符
  const cleaned = value.replace(/[^\dXx]/g, '').toUpperCase()

  // 限制长度为18位
  const limited = cleaned.substring(0, 18)

  // 添加空格分隔
  return limited.replace(/(\d{6})(\d{8})(\d{4})/, '$1 $2 $3').trim()
}
