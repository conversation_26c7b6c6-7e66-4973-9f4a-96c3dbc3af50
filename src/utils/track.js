// 导入useQuick方法，用于快速追踪事件
import { useQuick } from '@fe/seal/vue'

// 解析localStorage.USER_INFO并获取account字段
const getAccount = () => {
  const userInfo = JSON.parse(localStorage.USER_INFO || '{}')

  return userInfo.account || ''
}

// 导出track对象，包含多个事件追踪方法
export default {
  // 用户注册事件追踪
  signUp: params =>
    useQuick().quickTrack('signUp', {
      login_id: getAccount(), // 用户账号
      status_result: params.status ? '成功' : '失败', // 注册结果
      sign_type: '邮箱', // 登录类型
      failure_reason: params.status ? '' : params.message, // 失败原因
    }),
  // 用户登录事件追踪
  login: params =>
    useQuick().quickTrack('login', {
      login_id: getAccount(), // 用户账号
      status_result: params.status ? '成功' : '失败', // 登录结果
      login_type: '邮箱', // 登录类型
      failure_reason: params.status ? '' : params.message, // 失败原因
    }),
  // 快速登录事件追踪
  quickLogin: params =>
    useQuick().quickTrack('login', {
      login_id: getAccount(), // 用户账号
      login_type: params.loginType, // 登录类型
    }),
  // 元素曝光事件追踪
  exposure: params => ({
    eventName: 'elementExposure',
    params: {
      login_id: getAccount(), // 用户账号
      element_id: '310004', // 元素ID
      element_name: '分享邀请码', // 元素名称
      ...params, // 其他参数
    },
  }),
  // 元素点击事件追踪
  click: params => ({
    eventName: 'elementClick',
    params: {
      login_id: getAccount(), // 用户账号
      element_id: '310004', // 元素ID
      element_name: '分享邀请码', // 元素名称
      ...params, // 其他参数
    },
  }),

  // 元素曝光事件追踪
  elementExposure: params =>
    useQuick().quickTrack('elementExposure', {
      login_id: getAccount(), // 用户账号
      element_id: '310004', // 元素ID
      element_name: '分享邀请码', // 元素名称
      ...params, // 其他参数
    }),

  // 元素点击事件追踪
  elementClick: params =>
    useQuick().quickTrack('elementClick', {
      login_id: getAccount(), // 用户账号
      element_id: '310004', // 元素ID
      element_name: '分享邀请码', // 元素名称
      ...params, // 其他参数
    }),
}
