export function getDeviceInfo() {
  const userAgent = navigator.userAgent
  const platform = navigator.platform
  const appVersion = navigator.appVersion
  const language = navigator.language
  const languages = navigator.languages
  const devicePixelRatio = window.devicePixelRatio
  const hardwareConcurrency = navigator.hardwareConcurrency
  const connection = navigator.connection ? navigator.connection.effectiveType : 'Unknown'
  const orientation = window.orientation || 'Unknown'

  let osName = 'Unknown'
  let osVersion = 'Unknown'

  if (/iPhone|iPad|iPod/.test(userAgent)) {
    osName = 'iOS'
    osVersion = getIOSVersion(userAgent)
  } else if (/Android/.test(userAgent)) {
    osName = 'Android'
    osVersion = getAndroidVersion(userAgent)
  }

  return {
    userAgent: userAgent,
    platform: platform,
    appVersion: appVersion,
    language: language,
    languages: languages,
    devicePixelRatio: devicePixelRatio,
    hardwareConcurrency: hardwareConcurrency,
    connection: connection,
    orientation: orientation,
    osName: osName,
    osVersion: osVersion,
  }
}

export function getIOSVersion(userAgent) {
  const match = userAgent.match(/OS (\d+)_?(\d*)/)
  if (match && match.length >= 1) {
    return parseFloat(match[1]) + (parseFloat(match[2]) > 0 ? '.' + match[2] : '')
  }

  return 'Unknown'
}

export function getAndroidVersion(userAgent) {
  const match = userAgent.match(/Android\s([\d.]+)/)
  if (match && match.length >= 2) {
    return parseFloat(match[1])
  }

  return 'Unknown'
}
