import { useEnv } from '@fe/base'

// 解析query
const getQueryParams = (url = window.location.href) => {
  const theRequest = {}

  if (url.indexOf('?') !== -1) {
    const str = url.split('?')[1] || ''
    const params = str.split('&')

    for (let i = 0; i < params.length; i++) {
      theRequest[params[i].split('=')[0]] = unescape(params[i].split('=')[1])
    }
  }

  return theRequest
}

// 获取环境信息
export const getEnv = () => {
  const options = ['dev', 'test', 'stage', 'pro']
  const params = getQueryParams()
  const origin = window.location.origin
  let env = 'dev'

  if (
    origin.includes('-dev') ||
    origin.includes('localhost') ||
    location.href.includes(':8010') ||
    location.href.includes(':1024') ||
    origin.includes('-test')
  ) {
    env = localStorage.env || env
  } else if (origin.includes('-stage')) {
    env = 'stage'
  } else {
    env = 'pro'
  }
  params.env && (env = params.env) // url参数优先级最高
  env = options.includes(env) ? env : 'dev1'
  localStorage.setItem('env', env)

  return env
}

const env = getEnv().replace(/\d/g, '')
export const language = () => (useEnv().isChangDu ? useEnv().urlLangCode : localStorage.lang)

const API_MAP = {
  dev: 'https://cdproductionserverwebapi-none-new-test.changdu.ltd/',
  test: `https://cdproductionserverwebapi-none-new-test.changdu.ltd/`,
  stage: 'https://cdproductionserverwebapi-stage.changdu.vip/',
  pro: 'https://cdproductionserverwebapi.51changdu.com/',
}
const IMG_PI_MAP = {
  dev: 'https://cdproductionserverwebapi-none-new-test.changdu.ltd/',
  test: `https://cdproductionserverwebapi-none-new-test.changdu.ltd/`,
  stage: 'https://cdproductionserverwebapi-stage.changdu.vip/',
  pro: 'https://prg.51changdu.com/',
}
const LINK_MAP = {
  dev: 'https://pr-dev.changdu.ltd/',
  test: 'https://pr-test.changdu.ltd/',
  stage: 'https://pr-stage.changdu.ltd/',
  pro: 'https://pr.cdreader.com/',
}

// 获取操作系统信息
export const getOS = () => {
  const userAgent = navigator.userAgent || navigator.vendor || window.opera

  if (/android/i.test(userAgent)) {
    return 'Android'
  }

  if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
    return 'iOS'
  }

  if (/HarmonyOS/i.test(userAgent)) {
    return 'HarmonyOS'
  }

  return 'Unknown'
}

export const LINK = LINK_MAP[env]
export const API = API_MAP[env]
export const ImageApi = IMG_PI_MAP[env]
export const BASE_ENV = env
export const UPLOAD_API = '/PrActors/UploadFile'
