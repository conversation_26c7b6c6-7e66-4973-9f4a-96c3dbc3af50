/**
 * 视频处理工具函数
 */

// 缩略图缓存Map，避免重复生成
const thumbnailCache = new Map()

/**
 * 生成视频缩略图
 * @param {string} videoUrl 视频URL
 * @param {number} currentTime 截取时间点（秒），默认为1秒
 * @returns {Promise<string>} 返回base64格式的缩略图
 */
export const getVideoThumbnail = (videoUrl, currentTime = 1) => {
  return new Promise((resolve, reject) => {
    // 检查缓存
    const cacheKey = `${videoUrl}_${currentTime}`
    if (thumbnailCache.has(cacheKey)) {
      const cachedThumbnail = thumbnailCache.get(cacheKey)
      if (cachedThumbnail) {
        resolve(cachedThumbnail)
        return
      }
    }

    const video = document.createElement('video')
    const canvas = document.createElement('canvas')

    video.crossOrigin = 'anonymous'
    video.currentTime = currentTime
    video.muted = true // 静音以避免自动播放限制

    video.onloadeddata = () => {
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        reject(new Error('无法获取Canvas上下文'))
        return
      }

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      try {
        const thumbnail = canvas.toDataURL('image/jpeg', 0.8)

        // 缓存结果
        thumbnailCache.set(cacheKey, thumbnail)
        resolve(thumbnail)
      } catch (error) {
        reject(error)
      }
    }

    video.onerror = () => reject(new Error('视频加载失败'))
    video.src = videoUrl.replace(
      'https://cdproductionserverwebapi.51changdu.com',
      'https://cdproductionserverwebapi-2.51changdu.com'
    )

  })
}

/**
 * 清除缩略图缓存
 * @param {string} videoUrl 可选，指定清除某个视频的缓存，不传则清除所有
 */
export const clearThumbnailCache = (videoUrl) => {
  if (videoUrl) {
    // 清除指定视频的所有缓存
    for (const key of thumbnailCache.keys()) {
      if (key.startsWith(videoUrl)) {
        thumbnailCache.delete(key)
      }
    }
  } else {
    // 清除所有缓存
    thumbnailCache.clear()
  }
}

/**
 * 获取视频信息
 * @param {string} videoUrl 视频URL
 * @returns {Promise<object>} 返回视频信息对象
 */
export const getVideoInfo = (videoUrl) => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')

    video.onloadedmetadata = () => {
      resolve({
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        aspectRatio: video.videoWidth / video.videoHeight
      })
    }

    video.onerror = () => reject(new Error('视频加载失败'))
    video.src = videoUrl
  })
}

/**
 * 检查是否为视频文件
 * @param {string} url 文件URL
 * @returns {boolean} 是否为视频文件
 */
export const isVideoFile = (url) => {
  if (!url) return false

  const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v']
  const lowerUrl = url.toLowerCase()

  return videoExtensions.some(ext => lowerUrl.includes(ext))
}

/**
 * 获取视频文件扩展名
 * @param {string} url 视频URL
 * @returns {string} 文件扩展名
 */
export const getVideoExtension = (url) => {
  if (!url) return ''

  const match = url.toLowerCase().match(/\.([^.?#]+)(?:\?|#|$)/)
  return match ? match[1] : ''
}
