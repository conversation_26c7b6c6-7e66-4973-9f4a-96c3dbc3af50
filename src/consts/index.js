import { getDeviceInfo } from '@/utils/device'
import { onlyEnglish, idCard, realName, onlyLinkOrEmail } from '@/consts/rules'

export const LANG_OPTIONS = [
  { value: 1, label: '简体', code: 'cn', id: 'cn' },
  { value: 2, label: '繁体', code: 'ft', id: 'ft' },
  { value: 3, label: '英语', code: 'en', id: 'en' },
  { value: 4, label: '西语', code: 'es', id: 'sp' },
  { value: 5, label: '葡语', code: 'pt', id: 'pt' },
  { value: 6, label: '法语', code: 'fr', id: 'fr' },
  { value: 7, label: '俄语', code: 'ru', id: 'ru' },
  { value: 8, label: '意大利语', code: 'it', id: 'it' },
  { value: 9, label: '日语', code: 'ja', id: 'ja' },
  { value: 10, label: '阿拉伯语', code: 'ar', id: 'ar' },
  { value: 11, label: '印尼语', code: 'id', id: 'id' },
  { value: 12, label: '泰语', code: 'th', id: 'th' },
  { value: 13, label: '越南语', code: 'vi', id: 'vi' },
  { value: 14, label: '韩语', code: 'ko', id: 'ko' },
  { value: 15, label: '菲律宾语', code: 'tl', id: 'tl' },
  { value: 16, label: '德语', code: 'de', id: 'de' },
]

export const LANGS = [
  { value: 0, label: 'mckoc_lang_all' },

  { value: 3, label: 'koc_english' },
  { value: 4, label: 'koc_spanish' },
  { value: 5, label: 'koc_portuguese' },
  { value: 6, label: 'koc_french' },
  { value: 7, label: 'koc_russian' },
  { value: 11, label: 'koc_indonesian' },
  { value: 12, label: 'koc_thai' },
  { value: 14, label: 'koc_korean' },
  { value: 16, label: 'koc_german' },
  { value: 2, label: 'koc_traditional' },
  { value: 9, label: 'koc_japanese' },
  { value: 10, label: 'kocs_ar' }, // 无
  { value: 15, label: 'koc_filipino' },
  { value: 8, label: 'kocs_it' }, // 无
  { value: 13, label: 'kocs_vi' }, // 无
]

export const SORTS = [
  { value: 0, label: 'mkoc_shelves' },
  { value: 1, label: 'mkoc_recommendations' },
]

export const SCREEN_LANG = [
  { value: 'cn', label: '简体中文', code: 1 },
  { value: 'en', label: 'English', code: 3 },
]

export const LANG_MAP = {
  cn: '简体中文',
  en: 'English',
}

export const DEVICE = getDeviceInfo()
export const IS_IOS = DEVICE.osName === 'iOS'

// 媒体类型常量映射 - 演员附加资料
export const MEDIA_TYPE_OPTIONS = [
  {
    value: 2,
    label: '自我介绍',
    component: 'video',
    icon: 'video-o',
    color: 'purple',
    multiple: false,
    maxCount: 1,
    maxSize: 100,
    accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv',
  },
  {
    value: 1,
    label: '近照',
    component: 'image',
    icon: 'photo-o',
    color: 'blue',
    multiple: true,
    maxCount: 10,
    maxSize: 20,
    accept: '.png,.jpg,.jpeg,.gif,.webp',
  },
  {
    value: 3,
    label: '剧照',
    component: 'image',
    icon: 'photo-o',
    color: 'blue',
    multiple: true,
    maxCount: 10,
    maxSize: 20,
    accept: '.png,.jpg,.jpeg,.gif,.webp',
  },
  {
    value: 6,
    label: '作品片段',
    component: 'video',
    icon: 'video-o',
    color: 'purple',
    multiple: true,
    maxCount: 5,
    maxSize: 200,
    accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv',
  },
  {
    value: 5,
    label: '简历',
    component: 'file',
    icon: 'records-o',
    color: 'green',
    multiple: false,
    maxCount: 1,
    maxSize: 100,
    accept: '.pdf,.doc,.docx,.xlsx,.xls,.txt',
  },
  {
    value: 4,
    label: '最新报价',
    component: 'inputNumber',
    icon: 'gold-coin-o',
    color: 'orange',
    unit: '元',
    placeholder: '请输入报价金额',
  },
  {
    value: 7,
    label: '荣誉榜',
    component: 'image',
    icon: 'medal-o',
    color: 'gold',
    multiple: true,
    maxCount: 8,
    maxSize: 20,
    accept: '.png,.jpg,.jpeg,.gif,.webp',
  },
  {
    value: 8,
    label: '才艺展示',
    component: 'video',
    icon: 'music-o',
    color: 'purple',
    multiple: true,
    maxCount: 5,
    maxSize: 150,
    accept: '.mp4,.avi,.mov,.wmv,.flv,.mkv',
  },
]

// 演员角色类型枚举（参考后台项目）
export const ActorRoleType = {
  LeadingActress: 1, // 女一
  SecondActress: 2, // 女二
  SupportingActress: 3, // 女配
  LeadingActor: 4, // 男一
  SecondActor: 5, // 男二
  SupportingActor: 6, // 男配
  SupportingRole: 7, // 配角
  GuestRole: 8, // 客串
  Extra: 9, // 群演
  SpecialGuest: 10, // 特约
  ThirdActress: 11, // 女三
  FourthActress: 12, // 女四
  FifthActress: 13, // 女五
  ThirdActor: 14, // 男三
  FourthActor: 15, // 男四
  FifthActor: 16, // 男五
  FemaleAntagonist: 17, // 女反
  MaleAntagonist: 18, // 男反
  FollowingActor: 19, // 跟组演员
}

// 演员角色类型配置（参考后台项目）
export const ACTOR_ROLE_TYPE_CONFIG = {
  [ActorRoleType.LeadingActress]: { label: '女一', color: 'red' },
  [ActorRoleType.SecondActress]: { label: '女二', color: 'volcano' },
  [ActorRoleType.SupportingActress]: { label: '女配', color: 'pink' },
  [ActorRoleType.LeadingActor]: { label: '男一', color: 'blue' },
  [ActorRoleType.SecondActor]: { label: '男二', color: 'geekblue' },
  [ActorRoleType.SupportingActor]: { label: '男配', color: 'cyan' },
  [ActorRoleType.SupportingRole]: { label: '配角', color: 'green' },
  [ActorRoleType.GuestRole]: { label: '客串', color: 'orange' },
  [ActorRoleType.Extra]: { label: '群演', color: 'purple' },
  [ActorRoleType.SpecialGuest]: { label: '特约', color: 'magenta' },
  [ActorRoleType.ThirdActress]: { label: '女三', color: 'pink' },
  [ActorRoleType.FourthActress]: { label: '女四', color: 'pink' },
  [ActorRoleType.FifthActress]: { label: '女五', color: 'pink' },
  [ActorRoleType.ThirdActor]: { label: '男三', color: 'cyan' },
  [ActorRoleType.FourthActor]: { label: '男四', color: 'cyan' },
  [ActorRoleType.FifthActor]: { label: '男五', color: 'cyan' },
  [ActorRoleType.FemaleAntagonist]: { label: '女反', color: 'red' },
  [ActorRoleType.MaleAntagonist]: { label: '男反', color: 'red' },
  [ActorRoleType.FollowingActor]: { label: '跟组演员', color: 'red' },
}

// 演员角色类型选项（用于选择器）
export const ACTOR_ROLE_TYPE_OPTIONS = Object.entries(ACTOR_ROLE_TYPE_CONFIG).map(
  ([value, config]) => ({
    text: config.label,
    value: Number(value),
    color: config.color,
  })
)

// 工作人员角色类型枚举（来自 staffInfo.vue，更完整的定义）
export const RoleType = {
  Director: 1, // 导演
  Screenwriter: 2, // 编剧
  Producer: 3, // 制片人
  Photographer: 4, // 摄影
  Lighting: 5, // 灯光
  Costume: 6, // 服装
  Props: 7, // 道具
  Makeup: 8, // 化妆
  Actor: 9, // 演员
  ExecutiveProducer: 10, // 总监制
  ResponsibleProducer: 11, // 负责监制
  LineProducer: 12, // 执行制片
  ActorCoordinator: 13, // 演员统筹
  AssistantDirectorActor: 14, // 演员副导
  Coordinator: 15, // 统筹
  ExecutiveDirector: 16, // 执行导演
  DIT: 17, // DIT
  FieldProducer: 18, // 现场制片
  Driver: 19, // 司机
  ArtDirector: 20, // 美术
  CostumeAndMakeupHead: 21, // 服化负责人
  ProductionManager: 22, // 制片主任
  ExternalProducer: 23, // 外联制片
  LifeProducer: 24, // 生活制片
  SetAssistant: 25, // 场务
  ScriptSupervisor: 26, // 场记
  PhotographyAssistant: 27, // 摄影助理
  StillPhotographer: 28, // 剧照
  SoundRecordist: 29, // 收音师
  SoundAssistant: 30, // 收音助理
  LightingTechnician: 31, // 灯光师
  LightingAssistant: 32, // 灯光师助理
  Stylist: 33, // 造型师
  OnSetSupervisor: 34, // 现场主盯
  OnSetMakeup: 35, // 现场跟妆
  MakeupArtist: 36, // 改妆师
  HairAndMakeup: 37, // 梳化
  SetConstruction: 38, // 制景
  CameraEquipment: 39, // 摄影器材
  LightingEquipment: 40, // 灯光器材
  SetSupplies: 41, // 场务用品
  MartialArtsDirector: 42, // 武术指导
  Wuxing: 43, // 武行
  ArtAssistant: 44, // 美术助理
  StylistAssistant: 45, // 造型师助理
  CostumeAssistant: 46, // 服装助理
  DirectorAssistant: 47, // 导演助理
  ProducerAssistant: 48, // 制片助理
  MakeupAssistant: 49, // 化妆助理
  ActorAssistant: 50,  // 演员助理
  CleaningStaff: 51    // 保洁人员
}

// 工作人员角色类型配置（来自 staffInfo.vue，更完整的定义）
export const ROLE_TYPE_CONFIG = {
  [RoleType.Director]: { label: '导演', color: 'red', sort: 1 },
  [RoleType.DirectorAssistant]: { label: '导演助理', color: 'geekblue', sort: 2 },
  [RoleType.ExecutiveDirector]: { label: '执行导演', color: 'orange', sort: 3 },
  [RoleType.AssistantDirectorActor]: { label: '演员副导', color: 'red', sort: 4 },
  [RoleType.Screenwriter]: { label: '编剧', color: 'blue', sort: 10 },
  [RoleType.ExecutiveProducer]: { label: '总监制', color: 'magenta', sort: 20 },
  [RoleType.ResponsibleProducer]: { label: '负责监制', color: 'gold', sort: 21 },
  [RoleType.Producer]: { label: '制片人', color: 'green', sort: 22 },
  [RoleType.LineProducer]: { label: '执行制片', color: 'lime', sort: 23 },
  [RoleType.ProducerAssistant]: { label: '制片助理', color: 'lime', sort: 24 },
  [RoleType.ProductionManager]: { label: '制片主任', color: 'gold', sort: 25 },
  [RoleType.ExternalProducer]: { label: '外联制片', color: 'blue', sort: 26 },
  [RoleType.LifeProducer]: { label: '生活制片', color: 'green', sort: 27 },
  [RoleType.FieldProducer]: { label: '现场制片', color: 'cyan', sort: 28 },
  [RoleType.Photographer]: { label: '摄影', color: 'orange', sort: 30 },
  [RoleType.PhotographyAssistant]: { label: '摄影助理', color: 'orange', sort: 31 },
  [RoleType.StillPhotographer]: { label: '剧照', color: 'blue', sort: 32 },
  [RoleType.DIT]: { label: 'DIT', color: 'purple', sort: 33 },
  [RoleType.CameraEquipment]: { label: '摄影器材', color: 'orange', sort: 34 },
  [RoleType.Lighting]: { label: '灯光', color: 'purple', sort: 40 },
  [RoleType.LightingTechnician]: { label: '灯光师', color: 'purple', sort: 41 },
  [RoleType.LightingAssistant]: { label: '灯光助理', color: 'purple', sort: 42 },
  [RoleType.LightingEquipment]: { label: '灯光器材', color: 'purple', sort: 43 },
  [RoleType.ArtDirector]: { label: '美术', color: 'magenta', sort: 50 },
  [RoleType.ArtAssistant]: { label: '美术助理', color: 'geekblue', sort: 51 },
  [RoleType.SetConstruction]: { label: '制景', color: 'magenta', sort: 52 },
  [RoleType.Costume]: { label: '服装', color: 'pink', sort: 60 },
  [RoleType.CostumeAssistant]: { label: '服装助理', color: 'pink', sort: 61 },
  [RoleType.CostumeAndMakeupHead]: { label: '服化负责人', color: 'volcano', sort: 62 },
  [RoleType.Makeup]: { label: '化妆', color: 'volcano', sort: 70 },
  [RoleType.MakeupAssistant]: { label: '化妆助理', color: 'volcano', sort: 71 },
  [RoleType.Stylist]: { label: '造型师', color: 'pink', sort: 72 },
  [RoleType.StylistAssistant]: { label: '造型师助理', color: 'pink', sort: 73 },
  [RoleType.OnSetMakeup]: { label: '现场跟妆', color: 'volcano', sort: 74 },
  [RoleType.MakeupArtist]: { label: '改妆师', color: 'volcano', sort: 75 },
  [RoleType.HairAndMakeup]: { label: '梳化', color: 'volcano', sort: 76 },
  [RoleType.Props]: { label: '道具', color: 'cyan', sort: 80 },
  [RoleType.SoundRecordist]: { label: '收音师', color: 'purple', sort: 90 },
  [RoleType.SoundAssistant]: { label: '收音助理', color: 'purple', sort: 91 },
  [RoleType.Actor]: { label: '演员', color: 'geekblue', sort: 100, disable: true },
  [RoleType.ActorAssistant]: { label: '演员助理', color: 'blue', sort: 102 },
  [RoleType.ActorCoordinator]: { label: '演员统筹', color: 'blue', sort: 101 },
  [RoleType.MartialArtsDirector]: { label: '武术指导', color: 'magenta', sort: 110 },
  [RoleType.Wuxing]: { label: '武行', color: 'volcano', sort: 111 },
  [RoleType.Coordinator]: { label: '统筹', color: 'green', sort: 120 },
  [RoleType.SetAssistant]: { label: '场务', color: 'orange', sort: 121 },
  [RoleType.ScriptSupervisor]: { label: '场记', color: 'cyan', sort: 122 },
  [RoleType.SetSupplies]: { label: '场务用品', color: 'orange', sort: 123 },
  [RoleType.OnSetSupervisor]: { label: '现场主盯', color: 'red', sort: 124 },
  [RoleType.Driver]: { label: '司机', color: 'geekblue', sort: 130 },
  [RoleType.CleaningStaff]: { label: '保洁人员', color: 'green', sort: 140 },
}

// 工作人员角色类型选项（用于选择器，排除演员角色，按sort排序）
export const ROLE_TYPE_OPTIONS = Object.entries(ROLE_TYPE_CONFIG)
  .sort(([, a], [, b]) => a.sort - b.sort) // 按sort字段排序
  .map(([value, config]) => ({
    text: config.label,
    value: Number(value),
    disable: !!config?.disable,
  }))

// 保持向后兼容性的别名
export const StaffRoleType = RoleType
export const STAFF_ROLE_TYPE_CONFIG = ROLE_TYPE_CONFIG
export const STAFF_ROLE_TYPE_OPTIONS = ROLE_TYPE_OPTIONS

// 人员类型枚举
export const PersonType = {
  Individual: 1, // 个人
  Group: 2, // 团体
}

// 人员类型配置
export const PERSON_TYPE_CONFIG = {
  [PersonType.Individual]: { label: '个人', color: 'blue' },
  [PersonType.Group]: { label: '团体', color: 'green' },
}

// 人员类型选项（用于选择器）
export const PERSON_TYPE_OPTIONS = Object.entries(PERSON_TYPE_CONFIG).map(([value, config]) => ({
  text: config.label,
  value: Number(value),
  color: config.color,
}))

// 通用角色类型获取函数
export const getRoleTypeText = (roleType, parentType) => {
  if (parentType === 2) {
    // 演员角色
    return ACTOR_ROLE_TYPE_CONFIG[roleType]?.label || `演员角色${roleType}`
  } else if (parentType === 1 || parentType === 0) {
    // 工作人员角色（兼容 parentType 为 0 或 1 的情况）
    return ROLE_TYPE_CONFIG[roleType]?.label || `工作人员${roleType}`
  }
  return `角色${roleType}`
}

// 获取人员类型文本
export const getPersonTypeText = personType => {
  return PERSON_TYPE_CONFIG[personType]?.label || `人员类型${personType}`
}

// 有权限访问当日销场的角色类型
export const SCENE_VERIFY_ALLOWED_ROLES = [
  RoleType.Director, // 导演
  RoleType.Producer, // 制片人
  RoleType.ExecutiveProducer, // 总监制
  RoleType.ResponsibleProducer, // 负责监制
  RoleType.LineProducer, // 执行制片
  RoleType.ActorCoordinator, // 演员统筹
  RoleType.AssistantDirectorActor, // 演员副导
  RoleType.Coordinator, // 统筹
  RoleType.ExecutiveDirector, // 执行导演
  RoleType.FieldProducer, // 现场制片
  RoleType.ProductionManager, // 制片主任
  RoleType.DirectorAssistant, // 导演助理
  RoleType.ProducerAssistant, // 制片助理
  RoleType.SetAssistant, // 场务
  RoleType.ScriptSupervisor, // 场记
]

// 有权限访问场地管理的角色类型
export const VENUE_MANAGEMENT_ALLOWED_ROLES = [
  RoleType.Director, // 导演
  RoleType.Producer, // 制片人
  RoleType.ExecutiveProducer, // 总监制
  RoleType.ResponsibleProducer, // 负责监制
  RoleType.LineProducer, // 执行制片
  RoleType.Coordinator, // 统筹
  RoleType.ExecutiveDirector, // 执行导演
  RoleType.FieldProducer, // 现场制片
  RoleType.ProductionManager, // 制片主任
]

// 场地适合类型枚举
export const VenueSuitableType = {
  Ancient: '古装',
  Modern: '现代',
  Martial: '武侠',
  SciFi: '科幻',
  Republic: '民国',
  Rural: '乡村',
}

// 场地适合类型配置
export const VENUE_SUITABLE_TYPE_CONFIG = {
  [VenueSuitableType.Ancient]: { label: '古装', color: 'orange' },
  [VenueSuitableType.Modern]: { label: '现代', color: 'blue' },
  [VenueSuitableType.Martial]: { label: '武侠', color: 'red' },
  [VenueSuitableType.SciFi]: { label: '科幻', color: 'purple' },
  [VenueSuitableType.Republic]: { label: '民国', color: 'gold' },
  [VenueSuitableType.Rural]: { label: '乡村', color: 'green' },
}

// 场地适合类型选项（用于选择器）
export const VENUE_SUITABLE_TYPE_OPTIONS = Object.entries(VENUE_SUITABLE_TYPE_CONFIG).map(
  ([value, config]) => ({
    label: config.label,
    value: value,
    color: config.color,
  })
)

// 引入城市配置
export { CHINA_CITY_OPTIONS, getCityText } from './city.js'


// 场地内部/外部类型枚举
export const VenueInternalType = {
  External: 0, // 外部场地
  Internal: 1, // 内部场地
}

// 场地内部/外部类型配置
export const VENUE_INTERNAL_TYPE_CONFIG = {
  [VenueInternalType.External]: { label: '外部', color: 'blue' },
  [VenueInternalType.Internal]: { label: '内部', color: 'green' },
}

// 场地内部/外部类型选项（用于选择器）
export const VENUE_INTERNAL_TYPE_OPTIONS = Object.entries(VENUE_INTERNAL_TYPE_CONFIG).map(
  ([value, config]) => ({
    text: config.label,
    value: Number(value),
    color: config.color,
  })
)

// 获取场地适合类型文本
export const getVenueSuitableTypeText = (suitableType) => {
  return VENUE_SUITABLE_TYPE_CONFIG[suitableType]?.label || suitableType
}



// 获取场地内部类型文本
export const getVenueInternalTypeText = (internalType) => {
  return VENUE_INTERNAL_TYPE_CONFIG[internalType]?.label || `类型${internalType}`
}
