import { createRouter, createWebHashHistory } from 'vue-router'
import Login from './pages/login/login'
import useLoginStore from '@/stores/login'

const routes = [
  {
    path: '/',
    redirect: '/login',
  },
  {
    path: '/login',
    name: 'login',
    component: Login,
  },
  {
    path: '/bindEmail',
    name: 'bindEmail',
    component: () => import('./pages/login/bindEmail.vue'),
  },
  {
    path: '/logoutSuccess',
    name: 'logoutSuccess',
    component: () => import('./pages/login/logoutSuccess.vue'),
  },
  {
    path: '/switch',
    name: 'switch',
    component: () => import('./pages/switch/index.vue'),
  },
  {
    path: '/layout',
    name: 'layout',
    component: () => import('./layouts/index.vue'),
    children: [
      {
        path: 'home',
        name: 'layoutHome',
        component: () => import('./pages/recruitment/index.vue'),
      },
      {
        path: 'roomClean',
        name: 'layoutRoom<PERSON>lean',
        component: () => import('./pages/project/roomClean.vue'),
      },
      {
        path: 'myProjects',
        name: 'myProjects',
        component: () => import('./pages/project/myProjects.vue'),
      },
      {
        path: 'mine',
        name: 'mine',
        component: () => import('./pages/mine/index.vue'),
      },
    ],
  },
  // 演员信息页面
  {
    path: '/actor',
    name: 'actor',
    component: () => import('./pages/actor/index.vue'),
  },
  // 演员附加资料页面
  {
    path: '/talent',
    name: 'talent',
    component: () => import('./pages/actor/talent.vue'),
  },
  // 工作人员信息页面
  {
    path: '/staffInfo',
    name: 'staffInfo',
    component: () => import('./pages/staff/index.vue'),
  },
  // 合同列表页面
  {
    path: '/contracts',
    name: 'contracts',
    component: () => import('./pages/contract/list.vue'),
  },
  // 招募项目列表页面
  {
    path: '/recruitmentList',
    name: 'recruitmentList',
    component: () => import('./pages/recruitment/index.vue'),
  },
  // 招募项目详情页面
  {
    path: '/recruitment/:id',
    name: 'recruitmentDetail',
    component: () => import('./pages/recruitment/detail.vue'),
  },
  // 我的报名页面
  {
    path: '/apply',
    name: 'apply',
    component: () => import('./pages/apply/index.vue'),
  },
  // 加入项目
  {
    path: '/joinProject/:id',
    name: 'joinProject',
    component: () => import('./pages/project/joinProject.vue'),
  },
  // 项目住宿
  {
    path: '/accommodation/:projectId',
    name: 'projectAccommodation',
    component: () => import('./pages/project/accommodation.vue'),
  },
  // 项目销场
  {
    path: '/sceneCall/:projectId',
    name: 'sceneCall',
    component: () => import('./pages/project/sceneCall.vue'),
  },
  // 今日通告单
  {
    path: '/todaySceneCall/:projectId',
    name: 'todaySceneCall',
    component: () => import('./pages/project/todaySceneCall.vue'),
  },
  // 当日销场
  {
    path: '/todaySceneVerify/:projectId',
    name: 'todaySceneVerify',
    component: () => import('./pages/project/todaySceneVerify.vue'),
  },
  // 场地管理
  {
    path: '/venue/list',
    name: 'venueList',
    component: () => import('./pages/venue/list.vue'),
  },
  {
    path: '/venue/detail/:id',
    name: 'venueDetail',
    component: () => import('./pages/venue/detail.vue'),
  },
  {
    path: '/venue/add',
    name: 'venueAdd',
    component: () => import('./pages/venue/add.vue'),
  },
  {
    path: '/venue/edit/:id',
    name: 'venueEdit',
    component: () => import('./pages/venue/add.vue'),
  },
]

const router = new createRouter({
  history: createWebHashHistory(),
  routes,
})

// 需要登录验证的路由名称列表
const authRequiredRoutes = [
  'layout',
  'mine',
  'layoutHome',
  'layoutRoomClean',
  'bindEmail',
  'switch',
  'home',
  'actorInfo',
  'actorProfile',
  'staffInfo',
  'contracts',
  'recruitmentList',
  'recruitmentDetail',
  'apply',
  'joinProject',
  'projectAccommodation',
  'myProjects',
  'sceneCall',
  'todaySceneCall',
  'todaySceneVerify',
  'venueList',
  'venueDetail',
  'venueAdd',
  'venueEdit',
  'projectVenueList'
]

// 需要角色信息的路由名称列表
const roleRequiredRoutes = [
  'layout',
  'mine',
  'layoutHome',
  'layoutRoomClean',
  'home',
  'actorInfo',
  'actorProfile',
  'staffInfo',
  'contracts',
  'recruitmentList',
  'recruitmentDetail',
  'apply',
  'joinProject',
  'projectAccommodation',
  'myProjects',
  'sceneCall',
  'todaySceneCall',
  'todaySceneVerify',
  'venueList',
  'venueDetail',
  'venueAdd',
  'venueEdit',
  'projectVenueList'
]

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  const loginStore = useLoginStore()

  // 如果不是需要登录的页面，直接通过
  if (!authRequiredRoutes.includes(to.name)) {
    next()
    return
  }

  // 检查登录状态
  if (!loginStore.isLoggedIn) {
    // 未登录，跳转到登录页
    next({ name: 'login' })
    return
  }

  // 已登录，初始化用户信息和角色信息
  loginStore.initUserInfo()
  loginStore.initRole()

  // 检查是否需要角色但没有角色信息
  if (roleRequiredRoutes.includes(to.name) && !loginStore.getCurrentRole) {
    // 没有角色信息，跳转到角色选择页面
    next({ name: 'switch' })
    return
  }

  // 通过所有检查，允许访问
  next()
})

export default router
