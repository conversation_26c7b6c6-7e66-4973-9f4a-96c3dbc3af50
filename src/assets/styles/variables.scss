// 主色调
$primary-color: #ff6600;
$primary-color-light: #ff9547;
$primary-color-dark: #ff6b00;
$primary-color-darker: #ff5500;

// 辅助色
$secondary-color: #bbbbbb;
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #1890ff;

// 中性色
$text-primary: #333333;
$text-secondary: #666666;
$text-placeholder: #999999;
$text-disabled: #cccccc;

// 背景色
$bg-primary: #ffffff;
$bg-secondary: #f5f5f5;
$bg-tertiary: #030303;
$bg-disabled: #e9ecef;

// 边框色
$border-primary: #e9ecef;
$border-secondary: #ddd;

// 间距变量
$spacing-xs: 10px;
$spacing-sm: 20px;
$spacing-md: 30px;
$spacing-lg: 40px;
$spacing-xl: 60px;
$spacing-xxl: 80px;

// 字体大小
$font-size-xs: 28px;
$font-size-sm: 32px;
$font-size-md: 36px;
$font-size-lg: 42px;
$font-size-xl: 48px;
$font-size-xxl: 51px;

// Vant 主题变量覆盖
:root {
  --van-primary-color: #{$primary-color};
  --van-success-color: #{$success-color};
  --van-warning-color: #{$warning-color};
  --van-danger-color: #{$danger-color};
  --van-info-color: #{$info-color};
  --van-text-color: #{$text-primary};
  --van-text-color-2: #{$text-secondary};
  --van-text-color-3: #{$text-placeholder};
  --van-border-color: #{$border-primary};
  --van-background-color: #{$bg-primary};
  --van-background-color-light: #{$bg-secondary};
}
