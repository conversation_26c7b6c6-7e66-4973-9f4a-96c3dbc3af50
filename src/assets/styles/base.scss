/* 导入颜色变量 */
@import './variables.scss';

/* reset */
body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
textarea,
p,
blockquote,
th,
td,
input,
select,
button {
  margin: 0;
  padding: 0;
  -webkit-tap-highlight-color: rgb(0 0 0 / 0%);
}

input,
textarea {
  font-family: var(--van-base-font) !important;
}

* {
  user-select: none;
}

/* 初始化标签在所有浏览器中的margin、padding值 */

fieldset,
img {
  border: 0 none;
}

/* 重置fieldset（表单分组）、图片的边框为0 */
dl,
ul,
ol,
menu,
li {
  list-style: none;
}

/* 重置类表前导符号为onne,menu在HTML5中有效 */

blockquote,
q {
  quotes: none;
}

/* 重置嵌套引用的引号类型 */

blockquote::before,
blockquote::after,
q::before,
q::after {
  content: none;
}

/* 重置嵌套引用 */

input,
select,
textarea,
button {
  vertical-align: middle;
  outline: medium none;
}

button {
  border: 0 none;
  background-color: transparent;
  cursor: pointer;
}

address,
caption,
cite,
code,
dfn,
em,
var {
  font-style: normal;
  font-weight: normal;
}

caption {
  display: none;
}

img {
  vertical-align: middle;
}

html,
body,
#app {
  font-size: 14px;
  height: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 设置页面文字等在拖动鼠标选中情况下的背景色与文字颜色 */

::selection {
  color: #fff;
  background-color: #4c6e78;
}

a {
  text-decoration: none;
}

a:active {
  -webkit-tap-highlight-color: rgb(0 0 0 / 0%);
}

body {
  padding-bottom: env(safe-area-inset-bottom);
}

/**
 * 去掉滚动条
 */
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}

.disabled {
  pointer-events: none;
}

.space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.space-between-end {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.space-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-inline-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-inline {
  display: flex;
  align-items: center;
}

.flex-inline-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-column-space-between {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.text-primary {
  color: $primary-color !important;
}

.text-secondary {
  color: $text-secondary !important;
}

.nowrap {
  white-space: nowrap;
}

.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.ellipsis--l2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ellipsis--l3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

// 状态栏高度
.safe-area-inset-margin-top {
  @supports (margin-top: env(safe-area-inset-top)) {
    margin-top: env(safe-area-inset-top) !important;
  }

  @supports (margin-top: constant(safe-area-inset-top)) {
    margin-top: constant(safe-area-inset-top) !important;
  }
}

.safe-area-inset-padding-top {
  @supports (padding-top: env(safe-area-inset-top)) {
    padding-top: env(safe-area-inset-top) !important;
  }

  @supports (padding-top: constant(safe-area-inset-top)) {
    padding-top: constant(safe-area-inset-top) !important;
  }
}

.iOS-WebView .safe-area-inset-padding-top {
  padding-top: 0 !important;
}

.safe-area-inset-padding-bottom {
  @supports (padding-bottom: env(safe-area-inset-bottom)) {
    padding-bottom: env(safe-area-inset-bottom) !important;
  }

  @supports (padding-bottom: constant(safe-area-inset-bottom)) {
    padding-bottom: constant(safe-area-inset-bottom) !important;
  }
}

//  弹窗遮罩动画
.mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
}

.mask-enter-from,
.mask-leave-to {
  opacity: 0;
}

.mask-enter-active,
.mask-leave-active {
  transition: all 0.3s ease;
}

.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  bottom: 0;
  right: 0;
  margin: auto;
}

// Background
.bg-primary {
  background-color: $primary-color;
}

// Text
.title-3 {
  font-size: 18px;
}

.title-1 {
  font-size: 32px;
}

// ========== Vant ==========
:root:root {
  --van-dropdown-menu-title-active-text-color: #{$primary-color};
  --van-dropdown-menu-option-active-color: #{$primary-color};

  // Navbar 变量
  --van-nav-bar-background: #{$primary-color};
  --van-nav-bar-title-text-color: #ffffff;
  --van-nav-bar-icon-color: #fff;
  --van-nav-bar-arrow-size: 2rem;

  // Radio
  --van-radio-checked-icon-color: #{$primary-color};

  // Tag
  --van-tag-primary-color: #{$primary-color};

  // Button
  --van-button-primary-background: #{$primary-color};
  --van-button-primary-border-color: #{$primary-color};
}

.van-dropdown-item__content {
  border-radius: 0 0 46px 46px;
}
