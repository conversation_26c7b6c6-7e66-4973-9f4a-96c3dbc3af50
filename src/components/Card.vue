<template>
  <div class="card">
    <div class="card-header" v-if="$slots.header">
      <slot name="header"></slot>
    </div>
    <div class="card-body">
      <slot></slot>
    </div>
    <div class="card-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.card {
  border-radius: 0.5rem;
  background: #fff;
  padding: 1.2rem;
}

.card-header {
  padding-bottom: 1.2rem;
  font-weight: bold;
}

.card-footer {
  padding-top: 1.2rem;
}
</style>
