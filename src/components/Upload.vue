<template>
    <van-uploader v-model="fileList" :max-count="props.maxCount" :accept="mobileAccept" :disabled="isDisabled"
        :multiple="props.multiple" :deletable="!isDisabled" :max-size="props.maxSize" :preview-size="props.previewSize"
        :upload-icon="props.uploadIcon" :upload-text="props.uploadText" :after-read="afterRead" @delete="handleDelete"
        @oversize="handleOversize" @click-preview="handlePreview">
        <template #preview-cover="{ file }" v-if="props.showPreviewCover">
            <div class="upload-preview-cover">
                <van-icon name="eye-o" @click="handlePreview(file)" />
                <van-icon name="delete-o" @click="handleDeleteFile(file)" v-if="!isDisabled" />
            </div>
        </template>
    </van-uploader>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { showToast, showLoadingToast, closeToast, showImagePreview } from 'vant'
import { useLoad } from '@/components/loading/useLoad'
// 确保 Vant 组件和样式已在 main.js 中全局引入，或者在此处按需引入
// import { Uploader, Icon } from 'vant'; // 如果是按需引入，这里要引入 Uploader 和 Icon 组件
// import 'vant/lib/index.css';

// 假设 API 和 UPLOAD_API 路径正确
import { API, UPLOAD_API } from '@/utils/env'

// Props
const props = defineProps({
    modelValue: {
        type: [String, Array],
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    },
    multiple: {
        type: Boolean,
        default: false
    },
    maxCount: {
        type: Number,
        default: 1
    },
    maxSize: {
        type: Number,
        default: 20 * 1024 * 1024 // 5MB
    },
    accept: {
        type: String,
        default: 'image/*'
    },
    // 新增：输出格式控制
    outputFormat: {
        type: String,
        default: 'array', // 'array' | 'string' | 'comma'
        validator: (value) => ['array', 'string', 'comma'].includes(value)
    },
    previewSize: {
        type: [String, Number],
        default: 80
    },
    uploadIcon: {
        type: String,
        default: 'plus'
    },
    uploadText: {
        type: String,
        default: '上传'
    },
    showPreviewCover: {
        type: Boolean,
        default: false
    },
    // Vant Field 自定义组件规范属性
    readonly: {
        type: Boolean,
        default: false
    },
    error: {
        type: Boolean,
        default: false
    }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'upload-success', 'upload-error'])

// 全局 loading
const { open: openLoading, close: closeLoading } = useLoad()

// 响应式数据
const fileList = ref([]) // 核心：v-model 绑定的文件列表

// 计算属性
const isDisabled = computed(() => props.disabled || props.readonly)

// 处理移动端accept属性
const mobileAccept = computed(() => {
    if (!props.accept) return undefined

    // 移动端对某些accept格式支持不好，转换为更兼容的格式
    const acceptMap = {
        '.png,.jpg,.jpeg,.gif,.webp': 'image/*',
        '.mp4,.avi,.mov,.wmv,.flv,.mkv': 'video/*',
        '.pdf,.doc,.docx,.xlsx,.xls,.txt': '.pdf,.doc,.docx,.xlsx,.xls,.txt'
    }

    return acceptMap[props.accept] || props.accept
})

// 监听 modelValue 变化，同步到 fileList
// 这一部分是正确的，用于初始化和外部控制
watch(() => props.modelValue, (newValue, oldValue) => {
    // 避免循环更新
    if (newValue === oldValue) return

    try {
        if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
            fileList.value = []
            return
        }

        // 处理不同格式的输入值
        let urls
        if (Array.isArray(newValue)) {
            urls = newValue
        } else if (typeof newValue === 'string') {
            // 如果是逗号分隔的字符串，分割成数组
            urls = newValue.includes(',') ? newValue.split(',').map(url => url.trim()).filter(Boolean) : [newValue]
        } else {
            urls = [newValue]
        }
        // 过滤掉空的 URL，并构建 Vant Uploader 期望的文件对象结构
        const newFileList = urls.filter(Boolean).map((url, index) => {
            const fullUrl = getFullUrl(url)
            const fileType = getFileTypeFromUrl(fullUrl)

            return {
                // Vant Uploader 在内部可能会用一个独特的 ID，但对于外部传入的初始值，
                // 简单的唯一 ID 即可，只要不与 Vant 内部生成的重复就行
                name: `uploaded-${index}`, // 添加 name 属性，有助于调试
                url: fullUrl, // 完整的文件 URL
                isImage: fileType === 'image', // 根据文件类型判断是否为图片
                isVideo: fileType === 'video', // 标记是否为视频
                status: 'done', // 初始状态为已完成
                message: '上传成功', // 消息
                file: null // 历史文件通常没有 File 对象
            }
        })

        // 只有当新的文件列表与当前文件列表不同时才更新，防止不必要的 UI 刷新
        if (JSON.stringify(newFileList) !== JSON.stringify(fileList.value)) {
            fileList.value = newFileList
        }
    } catch (error) {
        console.error('更新文件列表时出错:', error)
        fileList.value = []
    }
}, { immediate: true })

// 工具函数
const getFullUrl = (url) => {
    if (!url) return ''
    return url.startsWith('http') ? url : API + url
}

const getRelativeUrl = (url) => {
    if (!url) return ''
    // 确保只替换一次 API 前缀
    return url.startsWith(API) ? url.replace(API, '') : url
}

// 根据URL判断文件类型
const getFileTypeFromUrl = (url) => {
    if (!url) return 'unknown'

    const lowerUrl = url.toLowerCase()

    // 图片类型
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg']
    if (imageExtensions.some(ext => lowerUrl.includes(ext))) {
        return 'image'
    }

    // 视频类型
    const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv', '.webm', '.m4v']
    if (videoExtensions.some(ext => lowerUrl.includes(ext))) {
        return 'video'
    }

    // 音频类型
    const audioExtensions = ['.mp3', '.wav', '.ogg', '.aac', '.flac']
    if (audioExtensions.some(ext => lowerUrl.includes(ext))) {
        return 'audio'
    }

    // 文档类型
    const docExtensions = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
    if (docExtensions.some(ext => lowerUrl.includes(ext))) {
        return 'document'
    }

    return 'unknown'
}




// 文件类型验证函数
const validateFileType = (file, acceptString) => {
    if (!acceptString || acceptString === '*/*') return true

    const fileName = file.name.toLowerCase()
    const fileType = file.type.toLowerCase()

    // 处理常见的accept格式
    const acceptTypes = acceptString.toLowerCase().split(',').map(type => type.trim())

    for (const acceptType of acceptTypes) {
        // 处理MIME类型 (如 image/*, video/*, application/pdf)
        if (acceptType.includes('/')) {
            if (acceptType.endsWith('/*')) {
                const category = acceptType.split('/')[0]
                if (fileType.startsWith(category + '/')) return true
            } else if (fileType === acceptType) {
                return true
            }
        }
        // 处理文件扩展名 (如 .jpg, .png, .mp4)
        else if (acceptType.startsWith('.')) {
            if (fileName.endsWith(acceptType)) return true
        }
    }

    return false
}

// 获取友好的文件类型描述
const getFileTypeDescription = (acceptString) => {
    if (!acceptString || acceptString === '*/*') return '任意文件'

    const typeMap = {
        'image/*': '图片',
        'video/*': '视频',
        'audio/*': '音频',
        'application/pdf': 'PDF文档',
        'application/msword': 'Word文档',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word文档',
        'application/vnd.ms-excel': 'Excel文档',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel文档',
        '.jpg,.jpeg,.png,.gif,.webp': '图片',
        '.mp4,.avi,.mov,.wmv,.flv,.mkv': '视频',
        '.pdf,.doc,.docx,.xlsx,.xls,.txt': '文档'
    }

    return typeMap[acceptString.toLowerCase()] || '指定格式文件'
}

// 文件超出大小限制处理
const handleOversize = (file) => {
    const maxSizeMB = (props.maxSize / 1024 / 1024).toFixed(1)
    showToast(`文件大小超出限制，最大支持 ${maxSizeMB}MB`)
    // Vant Uploader 默认不会将 oversize 的文件添加到 fileList，这里无需额外处理
}

// 预览文件
const handlePreview = (file) => {
    if (file && file.url) {
        showImagePreview([file.url])
    }
}

// 删除单个文件 (用于自定义预览封面)
const handleDeleteFile = (file) => {
    // 找到在 fileList 中的索引，并删除
    const index = fileList.value.findIndex(item => item === file)
    if (index > -1) {
        fileList.value.splice(index, 1)
        // 触发 Vant Uploader 的 @delete 事件，它会间接调用 handleDelete
        // 但这里直接更新 modelValue 更直接
        updateModelValue()
    }
}

// after-read 回调函数 - 处理文件上传
const afterRead = (fileItems) => {
    console.log('文件读取完成，准备上传:', fileItems)

    // 如果是多选，fileItems 是一个数组；单选则是一个对象
    const filesToUpload = Array.isArray(fileItems) ? fileItems : [fileItems]

    filesToUpload.forEach(file => {
        // 标记为上传中状态
        file.status = 'uploading'
        file.message = '上传中...'
    })

    // 批量上传文件
    uploadMultipleFiles(filesToUpload)
}

// 批量上传文件 - 新增函数
const uploadMultipleFiles = async (filesToUpload) => {
    if (!filesToUpload || filesToUpload.length === 0) return

    // 开启全局loading（只开启一次）
    openLoading()

    let successCount = 0
    let failedCount = 0

    try {
        // 限制并发数量，避免服务器压力过大
        const concurrentLimit = 3
        const uploadPromises = []

        for (let i = 0; i < filesToUpload.length; i += concurrentLimit) {
            const batch = filesToUpload.slice(i, i + concurrentLimit)
            const batchPromises = batch.map(file => uploadSingleFileInBatch(file))

            const batchResults = await Promise.allSettled(batchPromises)

            batchResults.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    successCount++
                } else {
                    failedCount++
                    console.error('文件上传失败:', result.reason)
                    // 移除失败的文件
                    removeFileFromList(batch[index])
                }
            })
        }

        // 更新modelValue
        if (successCount > 0) {
            await nextTick()
            updateModelValue()
        }

        // 显示上传结果
        if (failedCount === 0) {
            showToast(filesToUpload.length > 1 ? '所有文件上传成功' : '文件上传成功')
        } else if (successCount > 0) {
            showToast(`上传完成：${successCount}个成功，${failedCount}个失败`)
        } else {
            showToast('所有文件上传失败')
        }

    } catch (error) {
        console.error('批量上传出错:', error)
        showToast('上传过程中出现错误')
    } finally {
        // 确保loading被关闭
        closeLoading()
    }
}

// 单个文件上传（用于批量上传） - 新增函数
const uploadSingleFileInBatch = async (fileItem) => {
    // 输入验证
    if (!fileItem || !fileItem.file) {
        throw new Error('文件异常，请重新选择')
    }

    const targetFile = fileItem.file

    // 文件类型验证
    if (!validateFileType(targetFile, props.accept)) {
        const typeDesc = getFileTypeDescription(props.accept)
        throw new Error(`只能上传${typeDesc}文件`)
    }

    try {
        const uploadedUrl = await uploadToServer(targetFile)

        // 更新文件对象状态
        fileItem.url = getFullUrl(uploadedUrl)
        fileItem.content = getFullUrl(uploadedUrl)
        fileItem.status = 'done'
        fileItem.message = '上传成功'

        emit('upload-success', {
            file: targetFile,
            url: uploadedUrl,
            response: uploadedUrl
        })

        return fileItem
    } catch (error) {
        console.error('上传失败:', error)
        const errorMessage = error.message || '上传失败'

        // 更新文件对象状态
        fileItem.status = 'failed'
        fileItem.message = errorMessage

        emit('upload-error', {
            file: targetFile,
            error: error
        })

        throw error
    }
}

// 上传单个文件（保留原函数，用于兼容性）
const uploadFile = async (fileItem) => {
    // 输入验证
    if (!fileItem || !fileItem.file) {
        showToast('文件异常，请重新选择')
        removeFileFromList(fileItem)
        return
    }

    const targetFile = fileItem.file

    // 文件类型验证
    if (!validateFileType(targetFile, props.accept)) {
        const typeDesc = getFileTypeDescription(props.accept)
        showToast(`只能上传${typeDesc}文件`)
        removeFileFromList(fileItem)
        return
    }

    // 开启全局 loading
    openLoading()

    try {
        const uploadedUrl = await uploadToServer(targetFile)

        // 更新文件对象状态和URL
        fileItem.url = getFullUrl(uploadedUrl)
        fileItem.content = getFullUrl(uploadedUrl)
        fileItem.status = 'done'
        fileItem.message = '上传成功'

        // 更新modelValue
        await nextTick()
        updateModelValue()

        showToast('上传成功')

        emit('upload-success', {
            file: targetFile,
            url: uploadedUrl,
            response: uploadedUrl
        })

    } catch (error) {
        console.error('上传失败:', error)
        const errorMessage = error.message || '上传失败'
        showToast(errorMessage)

        // 更新文件对象状态
        fileItem.status = 'failed'
        fileItem.message = errorMessage

        // 失败后从列表中移除
        removeFileFromList(fileItem)

        emit('upload-error', {
            file: targetFile,
            error: error
        })
    } finally {
        // 确保loading被关闭
        closeLoading()
    }
}

// 服务器上传逻辑 (保持不变)
const uploadToServer = async (file) => {
    try {
        const formData = new FormData()
        formData.append('filename', file) // 确保这里的字段名 'filename' 与后端期望的匹配

        console.log('上传文件信息:', {
            fileName: file.name,
            fileSize: file.size,
            fileType: file.type,
            uploadUrl: API + UPLOAD_API
        })
        console.log('Authorization token:', localStorage.getItem('token') || 'No Token');


        const response = await fetch(API + UPLOAD_API, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                'Area': 'Changdu',
                'AreaInterface': 'cn'
            },
            body: formData
        })
        if (!response.ok) {
            const errorText = await response.text()
            console.error('上传失败响应:', errorText)
            throw new Error(`网络错误: ${response.status} - ${response.statusText} - ${errorText}`)
        }

        const data = await response.json()
        console.log('上传响应数据:', data)

        // 假设后端返回的数据结构是 { code: 200, data: [{ url: '...' }] }
        if (data.code == 311 && data.data?.data && Array.isArray(data.data.data) && data.data.data.length > 0) {
            return data.data.data[0].url
        }

        throw new Error(data.msg || data.message || '服务器返回异常数据')
    } catch (error) {
        console.error('上传过程中发生错误:', error)
        throw error
    }
}

// 从文件列表中移除指定文件 (确保移除的是 Vant Uploader 的文件对象)
const removeFileFromList = (targetFileItem) => {
    if (!targetFileItem) return

    const index = fileList.value.findIndex(item => item === targetFileItem)
    if (index > -1) {
        fileList.value.splice(index, 1)
        updateModelValue()
    }
}

// 更新 modelValue
const updateModelValue = () => {
    try {
        const successFiles = fileList.value
            .filter(file => file && file.url && file.status === 'done') // 确保只取上传成功的
            .map(file => getRelativeUrl(file.url))
            .filter(Boolean) // 过滤掉空值

        let newValue

        // 根据outputFormat和multiple属性决定输出格式
        if (props.multiple) {
            switch (props.outputFormat) {
                case 'string':
                case 'comma':
                    newValue = successFiles.join(',')
                    break
                case 'array':
                default:
                    newValue = successFiles
                    break
            }
        } else {
            newValue = successFiles[0] || ''
        }

        // 避免不必要的更新
        if (JSON.stringify(newValue) !== JSON.stringify(props.modelValue)) {
            emit('update:modelValue', newValue)
            emit('change', newValue)
        }
    } catch (error) {
        console.error('更新 modelValue 时出错:', error)
    }
}

// 删除文件处理 (@delete 事件)
const handleDelete = (deletedFileItem) => {
    // Vant Uploader 已经从 fileList.value 中移除了这个文件
    // 这里只需要确保 modelValue 同步更新
    console.log('文件被删除:', deletedFileItem);
    updateModelValue();
}
</script>

<style scoped lang="scss">
// 样式保持不变，但为了完整性包含在这里
.upload-preview-cover {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;

    .van-icon {
        color: white;
        font-size: 20px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.3);

        &:hover {
            background: rgba(0, 0, 0, 0.5);
        }
    }
}

:deep(.van-uploader__preview) {
    &:hover .upload-preview-cover {
        opacity: 1;
    }
}

// 错误状态样式
:deep(.van-uploader--error) {
    .van-uploader__upload {
        border-color: #ee0a24;
        background: rgba(238, 10, 36, 0.05);
    }
}

// 禁用状态样式
:deep(.van-uploader--disabled) {
    .van-uploader__upload {
        background: #f7f8fa;
        border-color: #ebedf0;
        cursor: not-allowed;
    }
}
</style>