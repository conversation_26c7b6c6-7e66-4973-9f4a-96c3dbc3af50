<template>
    <van-uploader
        v-model="internalFileList"
        :max-count="maxCount"
        :accept="accept"
        :disabled="disabled"
        :multiple="multiple"
        :deletable="!disabled"
        :max-size="maxSize"
        @after-read="handleAfterRead"
        @delete="handleDelete"
        @oversize="handleOversize"
    />
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { API, UPLOAD_API } from '@/utils/env'

// Props
const props = defineProps({
    modelValue: {
        type: [String, Array],
        default: () => ''
    },
    disabled: {
        type: Boolean,
        default: false
    },
    multiple: {
        type: Boolean,
        default: false
    },
    maxCount: {
        type: Number,
        default: 1
    },
    maxSize: {
        type: Number,
        default: 5 * 1024 * 1024 // 5MB
    },
    accept: {
        type: String,
        default: 'image/*'
    }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'upload-success', 'upload-error'])

// 内部文件列表
const internalFileList = ref([])

// 工具函数
const getFullUrl = (url) => {
    if (!url) return ''
    return url.startsWith('http') ? url : API + url
}

const getRelativeUrl = (url) => {
    if (!url) return ''
    return url.startsWith('http') ? url.replace(API, '') : url
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
    if (!newValue || (Array.isArray(newValue) && newValue.length === 0)) {
        internalFileList.value = []
        return
    }

    const urls = Array.isArray(newValue) ? newValue : [newValue]
    internalFileList.value = urls.filter(Boolean).map((url, index) => ({
        uid: `existing-${index}`,
        url: getFullUrl(url),
        status: 'done'
    }))
}, { immediate: true })

// 文件超出大小限制
const handleOversize = () => {
    const maxSizeMB = (props.maxSize / 1024 / 1024).toFixed(1)
    showToast(`文件大小超出限制，最大支持 ${maxSizeMB}MB`)
}

// 上传文件
const handleAfterRead = (file) => {
    if (Array.isArray(file)) {
        file.forEach(uploadSingleFile)
    } else {
        uploadSingleFile(file)
    }
}

// 上传单个文件
const uploadSingleFile = async (file) => {
    if (!file?.file) {
        showToast('文件异常，请重新选择')
        return
    }

    const targetFile = file.file

    // 文件类型验证
    if (props.accept === 'image/*' && !targetFile.type.startsWith('image/')) {
        showToast('只能上传图片文件')
        // 从列表中移除
        const index = internalFileList.value.findIndex(item => item === file)
        if (index > -1) {
            internalFileList.value.splice(index, 1)
        }
        return
    }

    // 显示上传进度
    const loadingToast = showLoadingToast({
        message: '上传中...',
        forbidClick: true,
        duration: 0
    })

    try {
        file.status = 'uploading'

        const uploadedUrl = await uploadToServer(targetFile)

        // 更新文件信息
        file.url = getFullUrl(uploadedUrl)
        file.status = 'done'

        updateModelValue()
        showToast('上传成功')
        
        emit('upload-success', {
            file: targetFile,
            url: uploadedUrl
        })

    } catch (error) {
        console.error('上传失败:', error)
        showToast(error.message || '上传失败')

        // 从列表中移除失败的文件
        const index = internalFileList.value.findIndex(item => item === file)
        if (index > -1) {
            internalFileList.value.splice(index, 1)
        }
        
        emit('upload-error', {
            file: targetFile,
            error: error
        })
    } finally {
        closeToast()
    }
}

// 服务器上传逻辑
const uploadToServer = async (file) => {
    const formData = new FormData()
    formData.append('filename', file)

    const response = await fetch(API + UPLOAD_API, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
            'Area': 'Changdu',
            'AreaInterface': 'cn'
        },
        body: formData
    })

    if (!response.ok) {
        throw new Error(`网络错误: ${response.status}`)
    }

    const data = await response.json()

    if (data.code === 200 && data.data?.length > 0) {
        return data.data[0].url
    }

    throw new Error(data.msg || data.message || '上传失败')
}

// 更新 modelValue
const updateModelValue = () => {
    const successFiles = internalFileList.value
        .filter(file => file.url && file.status === 'done')
        .map(file => getRelativeUrl(file.url))

    const newValue = props.multiple ? successFiles : (successFiles[0] || '')

    emit('update:modelValue', newValue)
    emit('change', newValue)
}

// 删除文件处理
const handleDelete = () => {
    // 延迟更新以确保 DOM 更新完成
    setTimeout(() => {
        updateModelValue()
    }, 0)
}
</script>

<style scoped lang="scss">
// 基础样式
:deep(.van-uploader) {
    .van-uploader__upload {
        border-radius: 8px;
        border: 2px dashed #ddd;
        transition: border-color 0.3s ease;
        
        &:hover {
            border-color: #ff8124;
        }
    }
}
</style>
