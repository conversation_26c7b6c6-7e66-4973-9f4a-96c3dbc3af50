<script setup>
defineProps({
  fixed: {
    type: Number,
    default: true,
  },
})
</script>

<template>
  <div class="loading" :class="{ 'loading-fixed': fixed }">
    <img src="@/assets/images/loading.gif" alt="" />
  </div>
</template>

<style scoped lang="scss">
.loading-fixed {
  position: fixed;
  z-index: 99999;
  left: 0;
  top: 0;
}
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: none;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  img {
    width: 120px;
    height: 35px;
  }
}
</style>
