import { createVNode, ref, render, watch, onMounted, onUnmounted } from 'vue'
import LoadComponent from './index.vue'
import { useRouter } from 'vue-router'

let container
let timer

function createContainer() {
  container = document.createElement('div')
  container.style.position = 'fixed'
  container.style.zIndex = '99999999'
  document.body.appendChild(container)
}

export const useLoad = () => {
  const loading = ref(false)
  const router = useRouter()

  const open = () => {
    loading.value = true
  }
  const close = () => {
    loading.value = false
  }

  const handleRouteChange = () => {
    if (container) {
      document.body.removeChild(container)
      container = null
    }
    close()
  }

  onMounted(() => {
    router.afterEach(handleRouteChange)
  })

  watch(
    () => loading.value,
    value => {
      if (value) {
        clearTimeout(timer)
        timer = setTimeout(() => {
          if (!container) {
            createContainer()
          }

          const toastInstance = createVNode(LoadComponent)
          render(toastInstance, container)
        }, 100)
      } else {
        clearTimeout(timer)
        if (container) {
          document.body.removeChild(container)
          container = null
        }
      }
    }
  )

  return {
    loading,
    open,
    close,
  }
}
