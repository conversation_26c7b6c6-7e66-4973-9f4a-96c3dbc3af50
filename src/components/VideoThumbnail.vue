<template>
  <div
    class="video-thumbnail"
    :style="{
      width: `${width}${typeof width === 'number' ? 'px' : ''}`,
      height: `${height}${typeof height === 'number' ? 'px' : ''}`,
    }"
    @click="handleClick">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <van-loading size="24px" />
      <span class="loading-text">生成封面中...</span>
    </div>

    <!-- 缩略图 -->
    <van-image
      v-else
      :src="displayThumbnail"
      :width="width"
      :height="height"
      fit="cover"
      :show-error="true"
      :show-loading="true"
      error-icon="video-o"
      loading-icon="video-o"
      class="thumbnail-image"
      @error="handleImageError" />

    <!-- 播放图标 -->
    <div v-if="showPlayIcon && !loading" class="play-icon">
      <van-icon name="play" />
    </div>

    <!-- 视频时长 -->
    <div v-if="duration && !loading" class="duration">
      {{ formatDuration(duration) }}
    </div>

    <!-- 错误状态 -->
    <div v-if="error && !loading" class="error-state">
      <van-icon name="video-o" />
      <span class="error-text">封面生成失败</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getVideoThumbnail, getVideoInfo } from '@/utils/video'
import { API } from '@/utils/env'

const props = defineProps({
  videoUrl: {
    type: String,
    required: true,
  },
  width: {
    type: Number,
    default: 200,
  },
  height: {
    type: Number,
    default: 150,
  },
  showPlayIcon: {
    type: Boolean,
    default: true,
  },
  thumbnailTime: {
    type: Number,
    default: 1,
  },
  defaultThumbnail: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['click', 'thumbnail-generated', 'error'])

// 响应式数据
const loading = ref(false)
const error = ref(false)
const thumbnail = ref('')
const duration = ref(0)

// 计算属性
const fullVideoUrl = computed(() => {
  if (!props.videoUrl) return ''
  return props.videoUrl.startsWith('http') ? props.videoUrl : API + props.videoUrl
})

const displayThumbnail = computed(() => {
  if (thumbnail.value) return thumbnail.value
  if (props.defaultThumbnail) return props.defaultThumbnail
  return '' // 使用van-image的默认占位符
})

// 方法
const generateThumbnail = async () => {
  if (!fullVideoUrl.value) return

  loading.value = true
  error.value = false

  try {
    // 并行获取缩略图和视频信息
    const [thumbnailData, videoInfo] = await Promise.all([
      getVideoThumbnail(fullVideoUrl.value, props.thumbnailTime),
      getVideoInfo(fullVideoUrl.value).catch(() => ({ duration: 0 })), // 视频信息获取失败不影响缩略图
    ])

    thumbnail.value = thumbnailData
    duration.value = videoInfo.duration

    emit('thumbnail-generated', {
      thumbnail: thumbnailData,
      duration: videoInfo.duration,
      videoInfo,
    })
  } catch (err) {
    console.warn('生成视频缩略图失败:', err)
    error.value = true
    emit('error', err)
  } finally {
    loading.value = false
  }
}

const handleClick = () => {
  if (!loading.value && !error.value) {
    emit('click', {
      videoUrl: fullVideoUrl.value,
      thumbnail: thumbnail.value,
    })
  }
}

const handleImageError = () => {
  error.value = true
}

const formatDuration = seconds => {
  if (!seconds || seconds <= 0) return ''

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)

  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`
  }

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 监听videoUrl变化
watch(
  () => props.videoUrl,
  () => {
    if (props.videoUrl) {
      generateThumbnail()
    }
  },
  { immediate: true }
)

// 组件挂载时生成缩略图
onMounted(() => {
  if (props.videoUrl) {
    generateThumbnail()
  }
})
</script>

<style scoped lang="scss">
.video-thumbnail {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    .play-icon {
      transform: scale(1.1);
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  z-index: 2;
}

.loading-text {
  color: white;
  font-size: 12px;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: transform 0.3s ease;
  z-index: 1;
}

.duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #999;
  font-size: 12px;

  .van-icon {
    font-size: 32px;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .play-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .duration {
    font-size: 10px;
    padding: 1px 4px;
  }

  .loading-text,
  .error-text {
    font-size: 10px;
  }
}
</style>
