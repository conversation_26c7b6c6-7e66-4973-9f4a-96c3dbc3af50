<template>
  <van-popup
    v-model:show="visible"
    position="center"
    :style="{ width: '90%', maxWidth: '800px' }"
    round
    :close-on-click-overlay="false">
    <div class="container">
      <van-space direction="vertical" fill class="text-center">
        <div class="title-1">📧</div>
        <div class="title-3">绑定邮箱</div>
        <div class="text-secondary">请绑定您的邮箱以完善账户信息</div>
      </van-space>
      <van-cell-group>
        <van-field
          v-model="email"
          type="email"
          :placeholder="$t('enter_email_address')"
          :error-message="emailError"
          @input="emailError = ''"
          left-icon="user-o"
          size="large"
          :disabled="isLoading" />

        <van-field
          v-model="verificationCode"
          :placeholder="$t('enter_verification_code')"
          :error-message="codeError"
          @input="codeError = ''"
          left-icon="envelop-o"
          size="large"
          :disabled="isLoading">
          <template #button>
            <van-button
              type="primary"
              plain
              size="mini"
              :disabled="countdown > 0 || isSendingCode"
              @click="sendVerificationCode">
              <van-loading v-if="isSendingCode" size="20px" color="#fff" />
              <span v-else>{{ countdown > 0 ? `${countdown}s` : '发送验证码' }}</span>
            </van-button>
          </template>
        </van-field>
      </van-cell-group>
      <van-space direction="vertical" fill>
        <van-button type="primary" block round :disable="isBindingEmail" @click="handleBindEmail">
          <van-loading v-if="isBindingEmail" size="24px" color="#fff" />
          <span v-else>绑定邮箱</span>
        </van-button>
        <van-button block round @click="handleSkipBinding">跳过绑定</van-button>
      </van-space>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import useLoginStore from '@/stores/login'
import { showToast } from 'vant'
import { useI18n } from 'vue-i18n'
import { validateEmail } from '@/utils'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['update:show', 'success', 'skip'])

// Stores and composables
const loginStore = useLoginStore()
const { t } = useI18n()

// Reactive data
const visible = ref(props.show)
const email = ref('')
const verificationCode = ref('')
const emailError = ref('')
const codeError = ref('')
const countdown = ref(0)
const isSendingCode = ref(false)
const isBindingEmail = ref(false)

let countdownTimer = null

// Computed
const isLoading = computed(() => isSendingCode.value || isBindingEmail.value)

// Watch props.show to sync with internal visible state
watch(
  () => props.show,
  newVal => {
    visible.value = newVal
  }
)

// Watch visible to emit update
watch(visible, newVal => {
  emit('update:show', newVal)
  if (!newVal) {
    // 重置表单数据
    resetForm()
  }
})

// Methods
const resetForm = () => {
  email.value = ''
  verificationCode.value = ''
  emailError.value = ''
  codeError.value = ''
  countdown.value = 0
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

const sendVerificationCode = async () => {
  if (countdown.value > 0 || isSendingCode.value) return

  if (!email.value) {
    emailError.value = t('enter_email_address')
    return
  }

  if (!validateEmail(email.value)) {
    emailError.value = t('invalid_email_format')
    return
  }

  try {
    isSendingCode.value = true

    const res = await loginStore.fetchSendCode({
      email: email.value,
    })

    if (res && res.status) {
      showToast(t('verification_code_sent'))
      startCountdown()
    } else {
      showToast(res?.message || t('send_failed_retry'))
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    showToast(t('send_failed_retry'))
  } finally {
    isSendingCode.value = false
  }
}

const startCountdown = () => {
  countdown.value = 180
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
    }
  }, 1000)
}

const validateForm = () => {
  let isValid = true

  if (!email.value) {
    emailError.value = t('enter_email_address')
    isValid = false
  } else if (!validateEmail(email.value)) {
    emailError.value = t('invalid_email_format')
    isValid = false
  }

  if (!verificationCode.value) {
    codeError.value = t('enter_verification_code')
    isValid = false
  }

  return isValid
}

const handleBindEmail = async () => {
  if (isBindingEmail.value) return

  if (!validateForm()) return

  try {
    isBindingEmail.value = true

    const verifyRes = await loginStore.fetchVerifyCode({
      email: email.value,
      code: verificationCode.value,
    })

    if (verifyRes && verifyRes.status) {
      showToast(t('email_bind_success'))

      // 更新用户信息，标记已绑定邮箱
      const updatedUserInfo = {
        ...JSON.parse(localStorage.getItem('USER_INFO') || '{}'),
        hasBindEmail: true,
      }

      loginStore.setUserInfo(updatedUserInfo)

      // 关闭弹窗并触发成功事件
      visible.value = false
      emit('success')
    } else {
      showToast(verifyRes?.message || t('bind_failed_retry'))
    }
  } catch (error) {
    console.error('绑定邮箱失败:', error)
    showToast(t('bind_failed_retry'))
  } finally {
    isBindingEmail.value = false
  }
}

const handleSkipBinding = () => {
  try {
    showToast('已跳过邮箱绑定')

    // 关闭弹窗并触发跳过事件
    visible.value = false
    emit('skip')
  } catch (error) {
    console.error('跳过绑定失败:', error)
    showToast('操作失败，请重试')
  }
}

// 清理定时器
const cleanup = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  cleanup()
})
</script>

<style scoped lang="scss">
.container {
  padding: 24px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 32px;
}
</style>
