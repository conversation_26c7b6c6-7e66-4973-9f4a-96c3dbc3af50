// 合同状态枚举
export const ContractStatus = {
  PENDING_EDITOR_REVIEW: 0, // 待责编审核
  WAITING_AUTHOR_SIGN: 1, // 等待作者签章
  AUTHOR_SIGNED: 2, // 作者签署完成
  EDITOR_REJECTED: 3, // 责编审核拒绝
  OA_APPROVING: 4, // OA审批中
  OA_APPROVED: 5, // OA审批通过
  OA_REJECTED: 6, // OA审批拒绝
  COMPANY_SIGNING: 11, // 公司正在签署
  SIGN_COMPLETED: 12, // 签章完成
  ARCHIVED: 100, // 合同归档
  INVALID: 101, // 作废
}

// 合同状态文本映射
export const ContractStatusText = {
  [ContractStatus.PENDING_EDITOR_REVIEW]: '待审核',
  [ContractStatus.WAITING_AUTHOR_SIGN]: '等待用户签章',
  [ContractStatus.AUTHOR_SIGNED]: '用户签署完成',
  [ContractStatus.EDITOR_REJECTED]: '审核拒绝',
  [ContractStatus.OA_APPROVING]: 'OA审批中',
  [ContractStatus.OA_APPROVED]: 'OA审批通过',
  [ContractStatus.OA_REJECTED]: 'OA审批拒绝',
  [ContractStatus.COMPANY_SIGNING]: '公司正在签署',
  [ContractStatus.SIGN_COMPLETED]: '签章完成',
  [ContractStatus.ARCHIVED]: '合同归档',
  [ContractStatus.INVALID]: '作废',
}

// 合同状态样式类映射
export const ContractStatusClass = {
  [ContractStatus.PENDING_EDITOR_REVIEW]: 'primary',
  [ContractStatus.WAITING_AUTHOR_SIGN]: 'primary',
  [ContractStatus.AUTHOR_SIGNED]: 'primary',
  [ContractStatus.EDITOR_REJECTED]: 'danger',
  [ContractStatus.OA_APPROVING]: 'primary',
  [ContractStatus.OA_APPROVED]: 'primary',
  [ContractStatus.OA_REJECTED]: 'danger',
  [ContractStatus.COMPANY_SIGNING]: 'primary',
  [ContractStatus.SIGN_COMPLETED]: 'success',
  [ContractStatus.ARCHIVED]: 'success',
  [ContractStatus.INVALID]: 'danger',
}

// 合同类型枚举
export const ContractType = {
  CONFIDENTIALITY_AGREEMENT: 101, // 保密协议
}

// 平台类型枚举
export const PlatformType = {
  PRODUCTION_PLATFORM: 101, // 制片平台
}

// 合作模式枚举
export const CooperationMode = {
  GUARANTEED: 0, // 保底
  REVENUE_SHARING: 1, // 分成
}

// 评分类型枚举
export const ScoreType = {
  D: 0,
  S: 1,
  A: 2,
  B: 3,
  C: 4,
}

// 故事类型枚举
export const StoryType = {
  LONG_NOVEL: 0, // 长篇小说
  SHORT_NOVEL: 1, // 短篇小说
}

// 承诺函状态枚举
export const PromiseStatus = {
  PENDING_ARCHIVE: 0, // 待归档
  ARCHIVED: 1, // 已归档
}

/**
 * 获取合同链接
 * @param {Object} contract 合同对象
 * @returns {string} 合同链接
 */
export const getContractUrl = contract => {
  // 优先级：正式文件查看地址 > 签约地址(ext) > 扩展字段9 > 扩展字段10
  return contract.ext9 || contract.ext10 || ''
}

/**
 * 获取合同状态文本
 * @param {number} status 状态码
 * @returns {string} 状态文本
 */
export const getContractStatusText = status => {
  return ContractStatusText[status] || '未知状态'
}

/**
 * 获取合同状态样式类
 * @param {number} status 状态码
 * @returns {string} 样式类名
 */
export const getContractStatusClass = status => {
  return ContractStatusClass[status] || 'status-unknown'
}

/**
 * 判断合同是否可以查看
 * @param {Object} contract 合同对象
 * @returns {boolean} 是否可以查看
 */
export const canViewContract = contract => {
  return contract.canView !== false && getContractUrl(contract) !== ''
}

/**
 * 判断合同是否可以编辑
 * @param {Object} contract 合同对象
 * @returns {boolean} 是否可以编辑
 */
export const canEditContract = contract => {
  return contract.canEdit === true
}

/**
 * 判断合同是否可以重签
 * @param {Object} contract 合同对象
 * @returns {boolean} 是否可以重签
 */
export const canReSignContract = contract => {
  return contract.canReSign === true
}

/**
 * 格式化合同日期
 * @param {string} dateString 日期字符串
 * @returns {string} 格式化后的日期
 */
export const formatContractDate = dateString => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

/**
 * 格式化合同金额
 * @param {number} amount 金额
 * @returns {string} 格式化后的金额
 */
export const formatContractAmount = amount => {
  if (!amount) return '0'
  return Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2 })
}
