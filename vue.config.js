const { defineConfig } = require('@vue/cli-service')
const pkg = require('./package.json')
const path = require('path')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const Components = require('unplugin-vue-components/webpack')
const AutoImport = require('unplugin-auto-import/webpack')
const { VioletResolver } = require('@fe/violet-resolvers')
const argv = require('minimist')(process.argv.slice(2))
const isBuiild = process.env.NODE_ENV === 'production'
const isDev = process.env.NODE_ENV === 'development'
const plugins = []
const vioLertplugins = [
  Components({
    resolvers: [VioletResolver()],
  }),
  AutoImport({
    resolvers: [VioletResolver({ autoImport: true })],
  }),
]
const lang = argv.lang
let env = argv.env
const domain = argv.domain
const webType = argv.type
let publicPath = isDev ? './' : 'https://g.changdu.vip/'
// publicPath = 'https://g.cdreader.com/'
if (env === 'prod') {
  publicPath = 'https://g.cdreader.com/'
}
if (isBuiild) {
  // 校验
  if (!domain || !webType || !lang || !env) {
    throw Error('构建参数补全，请检查[lang]、[env]、[domain]、[type]是否缺失！')
  }
  if (argv.env === 'dev') {
    env = 'dev3'
  } else if (argv.env === 'test') {
    env = 'test1'
  }

  lang.split(',').forEach(language => {
    plugins.push(
      new HtmlWebpackPlugin({
        filename: isDev
          ? 'index.html'
          : `${domain}${env === 'prod' ? '/' + language : ''}/index.html`,
        favicon: path.resolve(__dirname, './public/mpr.ico'),
        // filename: `${domain}/${lang}`,
        template: path.resolve(__dirname, './public/index.html'),
        inject: 'body',
        lang: language,
        title: '畅读制片',
        manifest:
          env === 'prod'
            ? `${publicPath}${language}.pr.manifest.json`
            : `${publicPath}test.pr.manifest.json`,
        baseHref: publicPath,
        meta: {
          'apple-mobile-web-app-title': '畅读制片',
          'apple-mobile-web-app-status-bar-style': 'black-translucent',
          'apple-mobile-web-app-capable': 'yes',
        },
      })
    )
  })
}

const extractCssStyles = ['css', 'postcss', 'scss', 'sass', 'less', 'stylus']
const extractCssModules = ['vue-modules', 'vue', 'normal-modules', 'normal']

module.exports = defineConfig({
  publicPath,
  pages: isBuiild
    ? {
      ['index']: {
        entry: 'src/main.js',
        template: 'public/index.html',
        filename: 'index.html',
        chunks: ['chunk-vendors', 'chunk-common', 'runtime', 'index'],
      },
    }
    : undefined,
  transpileDependencies: true,
  lintOnSave: false,
  devServer: {
    port: 80,
    open: false,
  },
  chainWebpack: config => {
    if (isBuiild) {
      config.plugins.delete('prefetch-index').delete('preload-index').delete('html-index')
      config.output.filename('[name].[contenthash:8].js').end()
      config.output.chunkFilename('[name].[contenthash:8].js').end()
      config.plugin('extract-css').tap(args => {
        return [
          {
            filename: '[name].[contenthash:8].css',
            chunkFilename: '[name].[contenthash:8].css',
          },
        ]
      })
      config.optimization.runtimeChunk('single')
      config.module.rule('images').set('generator', {
        filename: '[contenthash:8][ext]',
      })
      extractCssStyles.forEach(s => {
        extractCssModules.forEach(m =>
          config.module
            .rule(s)
            .oneOf(m)
            .use('extract-css-loader')
            .tap(options => {
              options.publicPath = './' // Set whatever you want as publicPath

              return options
            })
        )
      })
    } else {
      config.plugin('html').tap(args => {
        args[0].title = '畅读制片'
        args[0].env = 'dev'
        args[0].lang = 'en'
        args[0].htmlLangCode = 'en'

        return args
      })
    }

    config.plugin('define').tap(definitions => {
      definitions[0].VUE_APP_ASSET_VERSION = parseInt(pkg.version.replace(/\./g, ''), 10)

      return definitions
    })

    config.module.rule('images').set('parser', {
      dataUrlCondition: {
        maxSize: 2 * 1024,
      },
    })
  },
  configureWebpack: {
    plugins: isBuiild ? [...plugins, ...vioLertplugins] : vioLertplugins,
  },
})
