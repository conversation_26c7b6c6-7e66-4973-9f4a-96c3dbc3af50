FROM harbor.changdu.ltd/public/node:v6 as builder

WORKDIR /app
COPY . .

ARG LANG
ARG DOMAIN
ARG ENV
ARG TYPE

RUN pnpm install   && echo $LANG   && echo $DOMAIN   && echo $ENV   && echo $TYPE   && pnpm run build --lang=$LANG --domain=$DOMAIN  --env=$ENV --type=$TYPE

FROM harbor.changdu.ltd/cos/coscmd:v5

WORKDIR /app/dist

ARG WEB_BUCKET
ARG WEB_REGION
ARG G_BUCKET
ARG G_REGION
ARG DOMAIN

COPY --from=builder /app/dist .

RUN bash /opt/pc-cos-client.sh $WEB_BUCKET $WEB_REGION $G_BUCKET $G_REGION $DOMAIN


CMD ["/bin/bash"]
