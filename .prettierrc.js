module.exports = {
  requireConfig: true, // 必须有.prettierrc.js配置文件
  semi: false, // 不加分号
  tabWidth: 2, // 缩进 2 个空格
  printWidth: 100, // 单行最长100个字符
  singleQuote: true, // 单引号
  quoteProps: 'as-needed', // 对象属性名尽量不用引号
  trailingComma: 'es5', // 对象、数组最后添加逗号
  bracketSpacing: true, // 括号之间添加空格
  bracketSameLine: true, // 尖括号 > 不需要单独一行
  arrowParens: 'avoid', // 箭头函数只有1个参数时不需要小括号
  htmlWhitespaceSensitivity: 'css', // html空格不敏感
  jsxSingleQuote: false, // jsx中使用双引号
  jsxBracketSameLine: true, // 尖括号 > 不需要单独一行
  overrides: [
    {
      files: '*.json',
      options: {
        printWidth: 200,
      },
    },
  ],
}
