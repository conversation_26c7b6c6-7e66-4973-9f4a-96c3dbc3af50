{"name": "mpr", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build --no-module", "lint": "eslint --ext .ts,.tsx,.vue,.js,.jsx ./src --fix", "stylelint": "stylelint 'src/**/*.(scss|css|less)' --fix", "prettier": "prettier --write ./src", "prepare": "husky install", "build-test": "vue-cli-service build --no-module --lang=cn --domain=mpr-test.changdu.ltd --env=test --type=PC"}, "browserslist": ["iOS >= 11", "Android >= 5"], "eslintConfig": {"extends": ["@fe/eslint-config-vue"]}, "eslintIgnore": ["node_modules", "dist"], "prettierIgnore": ["node_modules", "dist"], "stylelint": {"stylelint": "stylelint 'src/**/*.scss|css|less) --fix"}, "lint-staged": {"src/**/*.{js,vue,ts,json}": ["prettier --write", "eslint --fix", "git add"], "*.md": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"@fe/base": "^1.0.22", "@fe/bridge": "^0.2.3", "@fe/seal": "3.0.11", "@fe/violet": "0.2.0-beta.2", "@vant/use": "^1.6.0", "@vueuse/core": "^8.9.4", "axios": "1.6.0", "clipboard": "2.0.11", "core-js": "^3.8.3", "cos-js-sdk-v5": "^1.8.4", "crypto-js": "^4.2.0", "css-vars-ponyfill": "^2.4.8", "dayjs": "^1.11.4", "decimal.js": "^10.4.3", "handlebars": "^4.7.8", "hls.js": "^1.5.20", "intersection-observer": "^0.12.2", "js-cookie": "^3.0.5", "lottie-web": "^5.9.6", "md5": "^2.3.0", "nanoid": "^5.0.7", "pinia": "^2.0.12", "pinia-plugin-persistedstate": "^3.1.0", "swiper": "^8.3.1", "vant": "^4.9.20", "vue": "^3.4.26", "vue-awesome-swiper": "^5.0.1", "vue-i18n": "9.0.0", "vue-picture-cropper": "^0.7.0", "vue-router": "^4.0.3", "vue3-infinite-scroll-good": "^1.0.2", "webpack-sources": "^3.2.3"}, "devDependencies": {"@commitlint/cli": "^17.6.3", "@commitlint/config-conventional": "^17.6.3", "@fe/eslint-config-vue": "0.2.0", "@fe/stylelint-config-scss": "0.3.0", "@fe/violet-resolvers": "^0.0.5", "@fe/vue-ci": "^0.2.10", "@province-city-china/level": "^8.5.8", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "commitlint": "^17.6.3", "eslint": "^7.32.0", "file-loader": "^6.2.0", "html-minifier-terser": "^7.1.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "lint-staged": "^11.2.6", "minimist": "^1.2.7", "postcss": "^8.4.14", "postcss-html": "^1.5.0", "postcss-pxtorem": "^6.0.0", "prettier": "^2.8.7", "sass": "^1.32.8", "sass-loader": "^11.1.1", "speed-measure-webpack-plugin": "^1.5.0", "stylelint": "^15.6.2", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^10.0.0", "stylelint-config-standard-vue": "^1.0.0", "stylelint-order": "^6.0.3", "typescript": "^5.0.4", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "url-loader": "^4.1.1", "webpack-bundle-analyzer": "^4.5.0", "webpack-manifest-plugin": "^5.0.0"}, "packageManager": "pnpm@10.6.3+sha512.bb45e34d50a9a76e858a95837301bfb6bd6d35aea2c5d52094fa497a467c43f5c440103ce2511e9e0a2f89c3d6071baac3358fc68ac6fb75e2ceb3d2736065e6"}