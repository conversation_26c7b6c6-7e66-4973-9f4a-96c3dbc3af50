{"files.exclude": {"node_modules": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.tslint": "explicit", "source.fixAll.stylelint": "explicit"}, "eslint.enable": true, "eslint.alwaysShowStatus": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "stylelint.enable": true, "stylelint.validate": ["css", "less", "postcss", "scss", "vue", "sass"], "[css]": {"editor.suggest.insertMode": "replace"}}